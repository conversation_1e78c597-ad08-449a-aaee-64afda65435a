using Keleid.BLL.DTOs;
using Keleid.BLL.Services.Interfaces;
using Keleid.DAL;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Keleid.BLL.Services
{
    public class LocationService : ILocationService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<LocationService> _logger;

        public LocationService(ApplicationDbContext context, ILogger<LocationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<ProvinceDto>> GetAllProvincesAsync()
        {
            try
            {
                var provinces = await _context.Provinces
                    .Include(p => p.Cities)
                    .OrderBy(p => p.Title)
                    .ToListAsync();

                return provinces.Select(p => new ProvinceDto
                {
                    Id = p.Id,
                    Title = p.Title,
                    Cities = p.Cities.Select(c => new CityDto
                    {
                        Id = c.Id,
                        Title = c.Title,
                        ProvinceId = c.ProvinceId,
                        ProvinceName = p.Title
                    }).OrderBy(c => c.Title).ToList()
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all provinces");
                return new List<ProvinceDto>();
            }
        }

        public async Task<List<CityDto>> GetCitiesByProvinceIdAsync(int provinceId)
        {
            try
            {
                var cities = await _context.Cities
                    .Include(c => c.Province)
                    .Where(c => c.ProvinceId == provinceId)
                    .OrderBy(c => c.Title)
                    .ToListAsync();

                return cities.Select(c => new CityDto
                {
                    Id = c.Id,
                    Title = c.Title,
                    ProvinceId = c.ProvinceId,
                    ProvinceName = c.Province.Title
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cities for province {ProvinceId}", provinceId);
                return new List<CityDto>();
            }
        }

        public async Task<List<CityDto>> GetCitiesByProvinceNameAsync(string provinceName)
        {
            try
            {
                var cities = await _context.Cities
                    .Include(c => c.Province)
                    .Where(c => c.Province.Title == provinceName)
                    .OrderBy(c => c.Title)
                    .ToListAsync();

                return cities.Select(c => new CityDto
                {
                    Id = c.Id,
                    Title = c.Title,
                    ProvinceId = c.ProvinceId,
                    ProvinceName = c.Province.Title
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cities for province {ProvinceName}", provinceName);
                return new List<CityDto>();
            }
        }

        public async Task<ProvinceDto?> GetProvinceByNameAsync(string provinceName)
        {
            try
            {
                var province = await _context.Provinces
                    .Include(p => p.Cities)
                    .FirstOrDefaultAsync(p => p.Title == provinceName);

                if (province == null)
                    return null;

                return new ProvinceDto
                {
                    Id = province.Id,
                    Title = province.Title,
                    Cities = province.Cities.Select(c => new CityDto
                    {
                        Id = c.Id,
                        Title = c.Title,
                        ProvinceId = c.ProvinceId,
                        ProvinceName = province.Title
                    }).OrderBy(c => c.Title).ToList()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting province {ProvinceName}", provinceName);
                return null;
            }
        }

        public async Task<CityDto?> GetCityByNameAsync(string cityName)
        {
            try
            {
                var city = await _context.Cities
                    .Include(c => c.Province)
                    .FirstOrDefaultAsync(c => c.Title == cityName);

                if (city == null)
                    return null;

                return new CityDto
                {
                    Id = city.Id,
                    Title = city.Title,
                    ProvinceId = city.ProvinceId,
                    ProvinceName = city.Province.Title
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting city {CityName}", cityName);
                return null;
            }
        }
    }
}
