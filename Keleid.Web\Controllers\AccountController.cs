﻿using Keleid.BLL.Services;
using Keleid.BLL.Services.Interfaces;
using Keleid.DAL.Models;
using Keleid.Web.Extensions;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;

namespace Keleid.Web.Controllers
{
    public class AccountController : Controller
    {
        private readonly IAccountService _accountService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly LoggingService _loggingService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public AccountController(IAccountService accountService, UserManager<ApplicationUser> userManager, SignInManager<ApplicationUser> signInManager, LoggingService loggingService, IHttpContextAccessor httpContextAccessor)
        {
            _accountService = accountService;
            _userManager = userManager;
            _signInManager = signInManager;
            _loggingService = loggingService;
            _httpContextAccessor = httpContextAccessor;
        }

        public IActionResult Index()
        {
            // اگر کاربر قبلاً لاگین کرده، به صفحه User/Index هدایت کن
            if (User.Identity.IsAuthenticated)
            {
                return RedirectToAction("Index", "User");
            }

            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CheckPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
            {
                return Json(new { success = false, message = "شماره تلفن وارد نشده است" });
            }

            // بررسی فرمت شماره تلفن
            if (phoneNumber.Length != 11 || !phoneNumber.StartsWith("09"))
            {
                return Json(new { success = false, message = "فرمت شماره تلفن صحیح نیست" });
            }

            try
            {
                // ذخیره شماره تلفن در session
                HttpContext.Session.SetString("PhoneNumber", phoneNumber);

                // چک کردن اینکه کاربر قبلاً ثبت‌نام کرده یا نه
                bool isRegistered = await _accountService.IsPhoneNumberRegisteredAsync(phoneNumber);

                if (isRegistered)
                {
                    // کاربر قبلاً ثبت‌نام کرده، بررسی کنیم که آیا پسورد دارد و تایید شده یا نه
                    bool hasPassword = await _accountService.UserHasPasswordAsync(phoneNumber);
                    bool isPhoneConfirmed = await _accountService.UserPhoneConfirmedAsync(phoneNumber);

                    // اگر پسورد ندارد یا تلفن تایید نشده، به صفحه Active هدایت شود
                    if (!hasPassword || !isPhoneConfirmed)
                    {
                        // دریافت IP Address
                        var ipAddress = GetClientIpAddress();

                        // بررسی محدودیت‌ها و ارسال کد تایید
                        var (canSend, message) = await _accountService.CanSendVerificationCodeWithIpCheckAsync(phoneNumber, ipAddress);
                        if (!canSend)
                        {
                            return Json(new { success = false, message = message });
                        }

                        // ارسال کد تایید
                        var sendResult = await _accountService.SendVerificationCodeAsync(phoneNumber, ipAddress);
                        if (!sendResult)
                        {
                            return Json(new { success = false, message = "خطا در ارسال کد تایید" });
                        }

                        return Json(new { success = true, isRegistered = true, redirectUrl = Url.Action("Active") });
                    }

                    // اگر پسورد دارد و تلفن تایید شده، به صفحه رمز عبور هدایت شود
                    return Json(new { success = true, isRegistered = true, redirectUrl = Url.Action("Password") });
                }
                else
                {
                    // کاربر جدید است، ابتدا ثبت‌نام کنیم
                    var registerResult = await _accountService.RegisterUserAsync(phoneNumber);

                    if (registerResult.Succeeded)
                    {
                        // لاگ کردن ثبت‌نام موفق
                        await _loggingService.LogUserRegisterAsync(phoneNumber, true, null, _httpContextAccessor);

                        // دریافت IP Address
                        var ipAddress = GetClientIpAddress();

                        // بررسی محدودیت‌ها و ارسال کد تایید
                        var (canSend, message) = await _accountService.CanSendVerificationCodeWithIpCheckAsync(phoneNumber, ipAddress);
                        if (!canSend)
                        {
                            return Json(new { success = false, message = message });
                        }

                        // ارسال کد تایید
                        var sendResult = await _accountService.SendVerificationCodeAsync(phoneNumber, ipAddress);
                        if (!sendResult)
                        {
                            return Json(new { success = false, message = "خطا در ارسال کد تایید" });
                        }

                        // به صفحه فعال‌سازی هدایت شود
                        return Json(new { success = true, isRegistered = false, redirectUrl = Url.Action("Active") });
                    }
                    else
                    {
                        // لاگ کردن ثبت‌نام ناموفق
                        await _loggingService.LogUserRegisterAsync(phoneNumber, false, "خطا در ثبت‌نام کاربر", _httpContextAccessor);
                        return Json(new { success = false, message = "خطا در ثبت‌نام کاربر" });
                    }
                }
            }
            catch (Exception)
            {
                return Json(new { success = false, message = "خطای سیستمی رخ داده است" });
            }
        }

        public IActionResult Password()
        {
            // بررسی اینکه کاربر از مراحل قبلی آمده یا نه
            if (string.IsNullOrEmpty(HttpContext.Session.GetString("PhoneNumber")))
            {
                // اگر شماره تلفن در session نیست، به صفحه اصلی هدایت کن
                return RedirectToAction("Index");
            }

            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Login(string phoneNumber, string password)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber) || string.IsNullOrWhiteSpace(password))
            {
                return Json(new { success = false, message = "شماره تلفن و رمز عبور الزامی هستند" });
            }

            try
            {
                // یافتن کاربر
                var user = await _userManager.FindByNameAsync(phoneNumber);
                if (user == null)
                {
                    return Json(new { success = false, message = "کاربری با این شماره تلفن یافت نشد" });
                }

                // بررسی اینکه کاربر فعال است
                if (!user.IsActive)
                {
                    return Json(new { success = false, message = "حساب کاربری شما غیرفعال است" });
                }

                // بررسی اینکه شماره تلفن تایید شده
                if (!user.PhoneNumberConfirmed)
                {
                    return Json(new { success = false, message = "شماره تلفن شما تایید نشده است", needsVerification = true });
                }

                // تایید رمز عبور
                bool isPasswordValid = await _accountService.ValidateUserAsync(user, password);
                if (!isPasswordValid)
                {
                    // لاگ کردن ورود ناموفق
                    await _loggingService.LogUserLoginAsync(phoneNumber, false, "رمز عبور نادرست", _httpContextAccessor);
                    return Json(new { success = false, message = "رمز عبور نادرست است" });
                }

                // لاگین کاربر
                await _signInManager.SignInAsync(user, isPersistent: false);

                // لاگ کردن ورود موفق
                await _loggingService.LogUserLoginAsync(phoneNumber, true, null, _httpContextAccessor);

                // پاک کردن session
                HttpContext.Session.Clear();

                return Json(new {
                    success = true,
                    message = "ورود موفقیت‌آمیز",
                    redirectUrl = Url.Action("Index", "Home")
                });
            }
            catch (Exception)
            {
                return Json(new { success = false, message = "خطای سیستمی رخ داده است" });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ForgotPassword(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
            {
                return Json(new { success = false, message = "شماره تلفن وارد نشده است" });
            }

            try
            {
                // بررسی وجود کاربر
                bool isRegistered = await _accountService.IsPhoneNumberRegisteredAsync(phoneNumber);
                if (!isRegistered)
                {
                    return Json(new { success = false, message = "کاربری با این شماره تلفن یافت نشد" });
                }

                // دریافت IP Address
                var ipAddress = GetClientIpAddress();

                // بررسی محدودیت‌ها
                var (canSend, message) = await _accountService.CanSendVerificationCodeWithIpCheckAsync(phoneNumber, ipAddress);
                if (!canSend)
                {
                    return Json(new {
                        success = false,
                        message = message
                    });
                }

                // ارسال کد تایید
                bool result = await _accountService.SendVerificationCodeAsync(phoneNumber, ipAddress);

                if (result)
                {
                    return Json(new {
                        success = true,
                        message = "کد یکبار مصرف ارسال شد",
                        redirectUrl = Url.Action("Active")
                    });
                }
                else
                {
                    return Json(new { success = false, message = "خطا در ارسال کد تایید" });
                }
            }
            catch (Exception)
            {
                return Json(new { success = false, message = "خطای سیستمی رخ داده است" });
            }
        }

        public IActionResult Active()
        {
            // بررسی اینکه کاربر از مراحل قبلی آمده یا نه
            if (string.IsNullOrEmpty(HttpContext.Session.GetString("PhoneNumber")))
            {
                // اگر شماره تلفن در session نیست، به صفحه اصلی هدایت کن
                return RedirectToAction("Index");
            }

            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> VerifyCode(string phoneNumber, string verificationCode)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber) || string.IsNullOrWhiteSpace(verificationCode))
            {
                return Json(new { success = false, message = "شماره تلفن یا کد تایید وارد نشده است" });
            }

            try
            {
                // تایید کد
                bool isCodeValid = await _accountService.VerifyPhoneNumberCodeAsync(phoneNumber, verificationCode);

                if (isCodeValid)
                {
                    // تنظیم session برای تایید شماره تلفن
                    HttpContext.Session.SetString("PhoneVerified", "true");

                    // یافتن کاربر برای لاگین
                    var user = await _userManager.FindByNameAsync(phoneNumber);
                    if (user == null)
                    {
                        return Json(new { success = false, message = "کاربر یافت نشد" });
                    }

                    // بررسی اینکه کاربر پسورد دارد یا نه
                    bool hasPassword = await _accountService.UserHasPasswordAsync(phoneNumber);

                    // همیشه کاربر را لاگین کنیم (چه پسورد داشته باشد چه نداشته باشد)
                    await _signInManager.SignInAsync(user, isPersistent: false);

                    if (hasPassword)
                    {
                        // کاربر پسورد دارد، session را پاک کن و به صفحه اصلی هدایت کن
                        HttpContext.Session.Clear();
                        return Json(new { success = true, hasPassword = true, redirectUrl = Url.Action("Index", "Home") });
                    }
                    else
                    {
                        // کاربر پسورد ندارد، به صفحه تنظیم پسورد هدایت شود
                        return Json(new { success = true, hasPassword = false, redirectUrl = Url.Action("NewPassword") });
                    }
                }
                else
                {
                    return Json(new { success = false, message = "کد تایید نامعتبر یا منقضی شده است" });
                }
            }
            catch (Exception)
            {
                return Json(new { success = false, message = "خطای سیستمی رخ داده است" });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ResendCode(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
            {
                return Json(new { success = false, message = "شماره تلفن وارد نشده است" });
            }

            try
            {
                // دریافت IP Address
                var ipAddress = GetClientIpAddress();

                // بررسی محدودیت‌ها
                var (canSend, message) = await _accountService.CanSendVerificationCodeWithIpCheckAsync(phoneNumber, ipAddress);

                if (!canSend)
                {
                    return Json(new {
                        success = false,
                        message = message
                    });
                }

                // ارسال کد تایید
                bool result = await _accountService.SendVerificationCodeAsync(phoneNumber, ipAddress);

                if (result)
                {
                    return Json(new { success = true, message = "کد تایید مجدداً ارسال شد" });
                }
                else
                {
                    return Json(new { success = false, message = "خطا در ارسال کد تایید" });
                }
            }
            catch (Exception)
            {
                return Json(new { success = false, message = "خطای سیستمی رخ داده است" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> CheckCooldown(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
            {
                return Json(new { canSend = true, remainingSeconds = 0 });
            }

            try
            {
                var (canSend, remainingSeconds) = await _accountService.CanSendVerificationCodeAsync(phoneNumber);
                return Json(new { canSend, remainingSeconds });
            }
            catch (Exception)
            {
                return Json(new { canSend = false, remainingSeconds = 120 });
            }
        }

        public IActionResult NewPassword()
        {
            // بررسی اینکه کاربر از مراحل قبلی آمده یا نه
            if (string.IsNullOrEmpty(HttpContext.Session.GetString("PhoneNumber")))
            {
                // اگر شماره تلفن در session نیست، به صفحه اصلی هدایت کن
                return RedirectToAction("Index");
            }

            // بررسی اینکه کاربر کد تایید را وارد کرده یا نه
            if (HttpContext.Session.GetString("PhoneVerified") != "true")
            {
                // اگر شماره تایید نشده، به صفحه Active هدایت کن
                return RedirectToAction("Active");
            }

            return View();
        }

        /// <summary>
        /// دریافت IP Address کلاینت
        /// </summary>
        /// <returns>IP Address</returns>
        private string GetClientIpAddress()
        {
            try
            {
                // بررسی X-Forwarded-For header (برای proxy ها)
                var xForwardedFor = HttpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
                if (!string.IsNullOrEmpty(xForwardedFor))
                {
                    // اولین IP در لیست IP های X-Forwarded-For
                    return xForwardedFor.Split(',')[0].Trim();
                }

                // بررسی X-Real-IP header
                var xRealIp = HttpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
                if (!string.IsNullOrEmpty(xRealIp))
                {
                    return xRealIp;
                }

                // استفاده از RemoteIpAddress
                var remoteIp = HttpContext.Connection.RemoteIpAddress?.ToString();
                if (!string.IsNullOrEmpty(remoteIp))
                {
                    // تبدیل IPv6 loopback به IPv4
                    if (remoteIp == "::1")
                    {
                        return "127.0.0.1";
                    }
                    return remoteIp;
                }

                // پیش‌فرض
                return "127.0.0.1";
            }
            catch (Exception)
            {
                // در صورت خطا، IP پیش‌فرض برگردان
                return "127.0.0.1";
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SetNewPassword(string phoneNumber, string newPassword, string confirmPassword)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber) || string.IsNullOrWhiteSpace(newPassword) || string.IsNullOrWhiteSpace(confirmPassword))
            {
                return Json(new { success = false, message = "تمام فیلدها الزامی هستند" });
            }

            // اعتبارسنجی طول پسورد
            if (newPassword.Length < 6)
            {
                return Json(new { success = false, message = "رمز عبور باید حداقل ۶ کاراکتر باشد" });
            }

            // اعتبارسنجی تطابق پسوردها
            if (newPassword != confirmPassword)
            {
                return Json(new { success = false, message = "رمز عبور و تکرار آن یکسان نیستند" });
            }

            try
            {
                // تنظیم پسورد جدید
                bool result = await _accountService.AddPasswordAsync(phoneNumber, newPassword);

                if (result)
                {
                    // پسورد با موفقیت تنظیم شد، حالا کاربر را لاگین کنیم
                    var user = await _userManager.FindByNameAsync(phoneNumber);
                    if (user != null)
                    {
                        // لاگین کاربر
                        await _signInManager.SignInAsync(user, isPersistent: false);

                        // پاک کردن session
                        HttpContext.Session.Clear();

                        return Json(new {
                            success = true,
                            message = "رمز عبور با موفقیت تنظیم شد",
                            redirectUrl = Url.Action("Index", "Home")
                        });
                    }
                    else
                    {
                        return Json(new { success = false, message = "خطا در یافتن کاربر" });
                    }
                }
                else
                {
                    return Json(new { success = false, message = "خطا در تنظیم رمز عبور" });
                }
            }
            catch (Exception)
            {
                return Json(new { success = false, message = "خطای سیستمی رخ داده است" });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Logout()
        {
            await _signInManager.SignOutAsync();
            HttpContext.Session.Clear();
            return RedirectToAction("Index", "Home");
        }
    }
}
