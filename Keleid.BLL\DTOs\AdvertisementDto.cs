using Keleid.BLL.Helpers;

namespace Keleid.BLL.DTOs
{
    public class AdvertisementDto
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string Slug { get; set; }
        public string Description { get; set; }
        public long Price { get; set; }
        public bool IsPriceless { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsApproved { get; set; }
        public DateTime CreatedAt { get; set; }

        // Category Info
        public int CategoryId { get; set; }
        public string CategoryTitle { get; set; }
        public string CategorySlug { get; set; }

        // Location Info
        public int LocationId { get; set; }
        public string Province { get; set; }
        public string City { get; set; }
        public string Address { get; set; }

        // Contact Info
        public string ContactPhone { get; set; }
        public string ContactEmail { get; set; }

        // User Info
        public string UserId { get; set; }
        public string UserPhone { get; set; } = string.Empty;
        public string UserEmail { get; set; } = string.Empty;
        public DateTime UserRegistrationDate { get; set; }

        // Admin Info (for approved/rejected advertisements)
        public string? ApprovedByUserId { get; set; }
        public string? ApprovedByUserName { get; set; }

        // Images
        public List<AdImageDto> Images { get; set; } = new List<AdImageDto>();
        public string MainImageUrl { get; set; }

        // Features
        public List<AdvertisementFeatureDto> Features { get; set; } = new List<AdvertisementFeatureDto>();

        // Formatted Price
        public string FormattedPrice => IsPriceless ? "توافقی" : $"{Price:N0} تومان";

        // Location Display
        public string LocationDisplay => !string.IsNullOrEmpty(City) && !string.IsNullOrEmpty(Province)
            ? $"{Province}، {City}"
            : Province ?? City ?? "نامشخص";

        // Persian Date Display
        public string PersianCreatedAt => PersianDateHelper.ToPersianDate(CreatedAt);

        // Relative Time Display
        public string RelativeTime => PersianDateHelper.GetRelativeTime(CreatedAt);
    }

    public class AdvertisementPageDto
    {
        public List<AdvertisementDto> Advertisements { get; set; } = new();
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public int TotalCount { get; set; }
        public bool HasPreviousPage => CurrentPage > 1;
        public bool HasNextPage => CurrentPage < TotalPages;
    }
}
