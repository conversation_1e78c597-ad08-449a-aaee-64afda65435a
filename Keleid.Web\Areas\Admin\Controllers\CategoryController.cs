﻿using Keleid.BLL.DTOs;
using Keleid.BLL.Services;
using Keleid.BLL.Services.Interfaces;
using Keleid.DAL.Models;
using Keleid.Web.Extensions;
using Microsoft.AspNetCore.Mvc;

namespace Keleid.Web.Areas.Admin.Controllers
{
    public class CategoryController : BaseAdminController
    {
        private readonly ICategoryService _categoryService;
        private readonly LoggingService _loggingService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public CategoryController(ICategoryService categoryService, LoggingService loggingService, IHttpContextAccessor httpContextAccessor)
        {
            _categoryService = categoryService;
            _loggingService = loggingService;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<IActionResult> Index()
        {
            var categories = await _categoryService.GetMainCategoriesWithSubCategoriesAsync();
            return View(categories);
        }

        public IActionResult Add()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Add(string title, string slug)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(title) || string.IsNullOrWhiteSpace(slug))
                {
                    TempData["Error"] = "عنوان و نامک دسته بندی الزامی است.";
                    return View();
                }

                var result = await _categoryService.CreateMainCategoryAsync(title.Trim(), slug.Trim());

                if (result)
                {
                    // دریافت شناسه دسته‌بندی ایجاد شده
                    var createdCategory = await _categoryService.GetCategoryBySlugAsync(slug.Trim());
                    var adminUsername = User.Identity?.Name ?? "Unknown";

                    // لاگ کردن افزودن دسته‌بندی
                    await _loggingService.LogCategoryCreateAsync(createdCategory?.Id ?? 0, title.Trim(), adminUsername, false, _httpContextAccessor);

                    TempData["Success"] = $"دسته بندی '{title}' با موفقیت ایجاد شد.";
                    return RedirectToAction("Index");
                }
                else
                {
                    TempData["Error"] = "دسته بندی با این عنوان یا نامک قبلاً وجود دارد.";
                    return View();
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = "خطا در ایجاد دسته بندی.";
                return View();
            }
        }

        public async Task<IActionResult> AddSubcategory(int? parentId)
        {
            if (!parentId.HasValue)
            {
                TempData["Error"] = "شناسه دسته والد مشخص نشده است.";
                return RedirectToAction("Index");
            }

            try
            {
                var parentCategory = await _categoryService.GetCategoryByIdAsync(parentId.Value);
                if (parentCategory == null || parentCategory.ParentCategoryId != null)
                {
                    TempData["Error"] = "دسته والد یافت نشد یا دسته انتخابی زیردسته است.";
                    return RedirectToAction("Index");
                }

                ViewBag.ParentId = parentId.Value;
                ViewBag.ParentTitle = parentCategory.Title;
                return View();
            }
            catch (Exception)
            {
                TempData["Error"] = "خطا در بارگذاری صفحه.";
                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddSubcategory(int parentId, string title, string slug)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(title) || string.IsNullOrWhiteSpace(slug))
                {
                    TempData["Error"] = "عنوان و نامک زیردسته الزامی است.";
                    return RedirectToAction("AddSubcategory", new { parentId });
                }

                var result = await _categoryService.CreateSubCategoryAsync(parentId, title.Trim(), slug.Trim());

                if (result)
                {
                    // دریافت شناسه زیردسته‌بندی ایجاد شده
                    var createdCategory = await _categoryService.GetCategoryBySlugAsync(slug.Trim());
                    var adminUsername = User.Identity?.Name ?? "Unknown";

                    // لاگ کردن افزودن زیردسته‌بندی
                    await _loggingService.LogCategoryCreateAsync(createdCategory?.Id ?? 0, title.Trim(), adminUsername, true, _httpContextAccessor);

                    TempData["Success"] = $"زیردسته '{title}' با موفقیت ایجاد شد.";
                    return RedirectToAction("Index");
                }
                else
                {
                    TempData["Error"] = "زیردسته با این عنوان یا نامک قبلاً وجود دارد یا دسته والد معتبر نیست.";
                    return RedirectToAction("AddSubcategory", new { parentId });
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = "خطا در ایجاد زیردسته.";
                return RedirectToAction("AddSubcategory", new { parentId });
            }
        }

        public async Task<IActionResult> EditTitle(int id)
        {
            try
            {
                var category = await _categoryService.GetCategoryByIdAsync(id);
                if (category == null)
                {
                    TempData["Error"] = "دسته بندی یافت نشد.";
                    return RedirectToAction("Index");
                }

                ViewBag.CategoryId = id;
                ViewBag.CurrentTitle = category.Title;
                ViewBag.CurrentSlug = category.Slug;
                ViewBag.IsMainCategory = category.ParentCategoryId == null;

                // اگر زیردسته است، نام دسته والد را بگیر
                if (category.ParentCategoryId.HasValue)
                {
                    var parentCategory = await _categoryService.GetCategoryByIdAsync(category.ParentCategoryId.Value);
                    ViewBag.ParentTitle = parentCategory?.Title ?? "نامشخص";
                }
                else
                {
                    ViewBag.ParentTitle = null;
                }

                return View();
            }
            catch (Exception)
            {
                TempData["Error"] = "خطا در بارگذاری صفحه.";
                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditTitle(int id, string title, string slug)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(title) || string.IsNullOrWhiteSpace(slug))
                {
                    TempData["Error"] = "عنوان و نامک دسته بندی الزامی است.";
                    return RedirectToAction("EditTitle", new { id });
                }

                // دریافت اطلاعات قبلی برای لاگ
                var oldCategory = await _categoryService.GetCategoryByIdAsync(id);
                var oldTitle = oldCategory?.Title ?? "نامشخص";
                var isSubCategory = oldCategory?.ParentCategoryId != null;

                var result = await _categoryService.UpdateCategoryTitleAsync(id, title.Trim(), slug.Trim());

                if (result)
                {
                    var adminUsername = User.Identity?.Name ?? "Unknown";

                    // لاگ کردن ویرایش عنوان دسته‌بندی
                    await _loggingService.LogCategoryUpdateAsync(id, oldTitle, title.Trim(), adminUsername, isSubCategory, _httpContextAccessor);

                    TempData["Success"] = $"عنوان دسته بندی به '{title}' تغییر یافت.";
                    return RedirectToAction("Index");
                }
                else
                {
                    TempData["Error"] = "دسته بندی با این عنوان یا نامک قبلاً وجود دارد.";
                    return RedirectToAction("EditTitle", new { id });
                }
            }
            catch (Exception)
            {
                TempData["Error"] = "خطا در ویرایش عنوان.";
                return RedirectToAction("EditTitle", new { id });
            }
        }

        public async Task<IActionResult> EditFeatures(int id)
        {
            try
            {
                // دریافت اطلاعات دسته و ویژگی‌هایش
                var category = await _categoryService.GetCategoryByIdAsync(id);
                if (category == null)
                {
                    TempData["Error"] = "دسته‌بندی مورد نظر یافت نشد.";
                    return RedirectToAction("Index");
                }

                var features = await _categoryService.GetCategoryFeaturesAsync(id);

                ViewBag.CategoryId = id;
                ViewBag.CategoryTitle = category.Title;

                // دریافت نام دسته والد
                string parentTitle = "ندارد";
                if (category.ParentCategoryId.HasValue)
                {
                    var parentCategory = await _categoryService.GetCategoryByIdAsync(category.ParentCategoryId.Value);
                    parentTitle = parentCategory?.Title ?? "نامشخص";
                }
                ViewBag.ParentTitle = parentTitle;

                return View(features);
            }
            catch (Exception)
            {
                TempData["Error"] = "خطا در بارگذاری صفحه.";
                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditFeatures(int categoryId, List<UpdateCategoryFeatureDto> features)
        {
            try
            {
                // دریافت اطلاعات دسته‌بندی برای لاگ
                var category = await _categoryService.GetCategoryByIdAsync(categoryId);
                var adminUsername = User.Identity?.Name ?? "Unknown";

                // به‌روزرسانی ویژگی‌های دسته‌بندی
                var result = await _categoryService.UpdateCategoryFeaturesAsync(categoryId, features ?? new List<UpdateCategoryFeatureDto>());

                if (result)
                {
                    // لاگ کردن تغییر ویژگی‌های زیردسته‌بندی
                    await _loggingService.LogCategoryFeaturesUpdateAsync(categoryId, category?.Title ?? "نامشخص", adminUsername, new { Features = features }, _httpContextAccessor);

                    TempData["Success"] = "ویژگی‌های دسته بندی با موفقیت به‌روزرسانی شد.";
                }
                else
                {
                    TempData["Error"] = "خطا در به‌روزرسانی ویژگی‌ها.";
                }

                return RedirectToAction("EditFeatures", new { id = categoryId });
            }
            catch (Exception ex)
            {
                TempData["Error"] = "خطا در به‌روزرسانی ویژگی‌ها: " + ex.Message;
                return RedirectToAction("EditFeatures", new { id = categoryId });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                // دریافت اطلاعات دسته‌بندی قبل از حذف برای لاگ
                var category = await _categoryService.GetCategoryByIdAsync(id);
                var categoryTitle = category?.Title ?? "نامشخص";
                var isSubCategory = category?.ParentCategoryId != null;
                var adminUsername = User.Identity?.Name ?? "Unknown";

                var result = await _categoryService.DeleteCategoryAsync(id);

                if (result)
                {
                    // لاگ کردن حذف دسته‌بندی
                    await _loggingService.LogCategoryDeleteAsync(id, categoryTitle, adminUsername, isSubCategory, _httpContextAccessor);

                    TempData["Success"] = "دسته‌بندی با موفقیت حذف شد.";
                }
                else
                {
                    TempData["Error"] = "امکان حذف این دسته‌بندی وجود ندارد. ممکن است دارای زیردسته یا آگهی باشد.";
                }
            }
            catch (Exception)
            {
                TempData["Error"] = "خطا در حذف دسته‌بندی.";
            }

            return RedirectToAction("Index");
        }
    }
}
