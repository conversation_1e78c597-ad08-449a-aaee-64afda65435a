using Keleid.BLL.Helpers;

namespace Keleid.BLL.DTOs
{
    public class FavoriteDto
    {
        public int Id { get; set; }
        public string UserId { get; set; }
        public int AdId { get; set; }
        public DateTime CreatedAt { get; set; }

        // Advertisement Info
        public AdvertisementDto Advertisement { get; set; }

        // Helper methods
        public string GetFavoriteTime()
        {
            return PersianDateHelper.GetRelativeTime(CreatedAt);
        }
    }
}
