﻿namespace Keleid.DAL.Models
{
    public class Category
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string Slug { get; set; }

        public int? ParentCategoryId { get; set; }
        public Category ParentCategory { get; set; }

        public ICollection<Category> SubCategories { get; set; }

        public ICollection<CategoryFeature> CategoryFeatures { get; set; }
        public ICollection<Advertisement> Advertisements { get; set; }

    }
}
