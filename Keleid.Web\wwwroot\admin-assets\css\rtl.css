/* @import url("https://fonts.googleapis.com/css?family=Cairo:400,600,700"); */

.rtl {
    direction: rtl
}

.rtl ul {
    padding: 0
}

.rtl.ls-closed .sidebar {
    margin-right: -300px
}

.rtl.overlay-open .sidebar {
    margin-right: 0
}

.rtl.ls-toggle-menu .sidebar {
    margin-right: -300px
}

.rtl.ls-toggle-menu section.content {
    margin-right: 0px
}

.rtl section.content {
    margin: 60px 250px 0px 0px
}

.rtl .right-sidebar {
    left: -300px
}

.rtl .right-sidebar.open {
    left: 0;
    right: auto
}

.rtl .sidebar {
    right: 0;
    left: auto
}

.rtl .dropdown-menu .header {
    text-align: right
}

.rtl .dropdown-menu ul.menu.tasks h4 small {
    float: left
}

.rtl .dropdown-menu ul.menu .menu-info {
  
    margin-left: 0;
    text-align: right
}

.rtl .dropdown-menu ul.menu li a {
    text-align: right
}

/* .rtl .navbar-nav>li {
    float: right
} */

.rtl .navbar-nav>li>a {
    margin-left: 0
}

.rtl .navbar .navbar-header {
    float: right;
    padding-right: 15px;
    padding-left: 0
}

.rtl .navbar .navbar-header .navbar-brand {
    margin-right: 0px
}

.rtl .navbar .navbar-right {
    float: right !important
}

.rtl .navbar .navbar-right .dropdown-menu {
    right: auto;
    left: 0
}

.rtl .navbar .navbar-right .dropdown-menu .footer {
    text-align: center
}

.rtl .navbar .navbar-right .progress-container .progress .progress-value {
    left: 0;
    right: auto
}

.rtl .navbar .navbar-left .dropdown-menu {
    right: auto
}

.rtl .navbar .navbar-left .dropdown-menu .menu li a {
    text-align: right
}

.rtl .navbar .dropdown-menu:before {
    left: 10px;
    right: auto
}

.rtl .sidebar .menu .list .header:before {
    left: auto;
    right: 0
}

.rtl .sidebar .menu .list .menu-toggle:before,
.rtl .sidebar .menu .list .menu-toggle:after {
    left: 17px;
    right: auto
}

.rtl .sidebar .menu .list a span {
    margin: 0 12px 0 0
}

.rtl .sidebar .menu .list .ml-menu li a {
    padding-right: 40px;
    padding-left: 10px
}

.rtl .sidebar .menu .list .ml-menu li a::before {
    content: '\f30f';
    right: 14px;
    left: auto;
    transform: scale(-1, 1);
    -webkit-transform: scale(-1, 1);
    -ms-transform: scale(-1, 1)
}

.rtl .right-sidebar .right_chat ul .media .media-object {
    margin-left: 10px;
    margin-right: 0px
}

.rtl .right-sidebar .right_chat ul .media .status {
    right: 0;
    left: auto
}

.rtl .right-sidebar #settings .setting-list li .switch {
    left: 5px;
    right: auto
}

.rtl .card .header .header-dropdown {
    right: auto;
    left: 12px
}

.rtl .card .header .header-dropdown ul.dropdown-menu {
    left: 0;
    right: auto
}

.rtl .widget_2 li {
    border-left: 1px solid #eee;
    border-right: none
}

.rtl .widget_2 li .text-right {
    text-align: left !important
}

.rtl .widget_2 li .text-small {
    font-size: 14px
}

.rtl #line_chart {
    height: 375px !important
}

.rtl .table thead tr th,
.rtl .table thead tr td {
    text-align: right
}

.rtl .card .header h2::before {
    right: -20px;
    left: auto
}

.rtl .progress-container .progress .progress-value {
    right: auto;
    left: 6px
}

.rtl .activity li a i {
    float: right
}

.rtl .activity li a .info {
    margin-right: 50px;
    margin-left: 0px
}

.rtl .checkbox label,
.rtl .radio label {
    padding-right: 35px;
    padding-left: 0px
}

.rtl .checkbox label:before,
.rtl .checkbox label:after {
    right: 0;
    left: auto
}

.rtl .form-control {
    border-radius: 0 30px 30px 0 !important;
    border-left: transparent !important
}

.rtl .input-group-addon {
    border-radius: 30px 0 0 30px !important;
    border-right: transparent !important;
    padding: 10px 0px 10px 18px !important
}

.rtl .team-info li+li {
    z-index: 99;
    position: relative
}

@media screen and (max-width: 1169px) {
    .rtl section.content {
        margin-right: 0
    }
    .rtl .navbar .navbar-header .navbar-brand {
        margin-right: 30px;
        margin-left: 10px
    }
    .rtl.ls-closed .bars:after,
    .rtl.ls-closed .bars:before {
        right: 0px;
        left: auto
    }
    .rtl .navbar>.container .navbar-brand,
    .rtl .navbar>.container-fluid .navbar-brand {
        margin-right: 20px;
        margin-left: 0
    }
}

@media screen and (max-width: 767px) {
    .rtl.ls-closed .bars:after,
    .rtl.ls-closed .bars:before {
        margin-right: 0
    }
}

.body p{
    text-align:justify
}