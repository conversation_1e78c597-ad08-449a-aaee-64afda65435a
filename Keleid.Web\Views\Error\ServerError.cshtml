@{
    ViewBag.Title = "خطای سرور - 500";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<style>
    .error-container {
        min-height: 70vh;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 2rem;
    }
    
    .error-content {
        max-width: 600px;
        margin: 0 auto;
    }
    
    .error-code {
        font-size: 8rem;
        font-weight: bold;
        color: #e67e22;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    
    .error-title {
        font-size: 2rem;
        color: #2c3e50;
        margin-bottom: 1rem;
        font-weight: 600;
    }
    
    .error-description {
        font-size: 1.1rem;
        color: #7f8c8d;
        margin-bottom: 2rem;
        line-height: 1.6;
    }
    
    .error-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .btn-primary-custom {
        background: linear-gradient(135deg, #e67e22, #d35400);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        color: white;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .btn-primary-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(230, 126, 34, 0.3);
        color: white;
        text-decoration: none;
    }
    
    .btn-secondary-custom {
        background: transparent;
        border: 2px solid #95a5a6;
        padding: 10px 28px;
        border-radius: 25px;
        color: #7f8c8d;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .btn-secondary-custom:hover {
        border-color: #7f8c8d;
        color: #2c3e50;
        text-decoration: none;
        transform: translateY(-1px);
    }
    
    .error-icon {
        font-size: 4rem;
        color: #e67e22;
        margin-bottom: 1rem;
        opacity: 0.8;
    }
    
    .status-info {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 2rem 0;
        color: #856404;
    }
    
    .status-info h5 {
        color: #856404;
        margin-bottom: 1rem;
    }
    
    .retry-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 2rem 0;
        border-left: 4px solid #e67e22;
    }
    
    .retry-btn {
        background: #27ae60;
        border: none;
        padding: 10px 25px;
        border-radius: 25px;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .retry-btn:hover {
        background: #229954;
        transform: translateY(-1px);
    }
    
    .retry-btn:disabled {
        background: #95a5a6;
        cursor: not-allowed;
        transform: none;
    }
    
    .countdown {
        font-weight: bold;
        color: #e67e22;
    }
    
    @@media (max-width: 768px) {
        .error-code {
            font-size: 5rem;
        }
        
        .error-title {
            font-size: 1.5rem;
        }
        
        .error-actions {
            flex-direction: column;
            align-items: center;
        }
    }
</style>

<div class="error-container">
    <div class="error-content">
        <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        
        <div class="error-code">500</div>
        
        <h1 class="error-title">@ViewBag.ErrorMessage</h1>
        
        <p class="error-description">
            @ViewBag.ErrorDescription
        </p>
        
        @if (!string.IsNullOrEmpty(ViewBag.RequestId))
        {
            <div class="status-info">
                <h5><i class="fas fa-info-circle"></i> اطلاعات خطا</h5>
                <p class="mb-0">
                    <strong>شناسه درخواست:</strong> <code>@ViewBag.RequestId</code><br>
                    <small class="text-muted">این شناسه را هنگام تماس با پشتیبانی ارائه دهید.</small>
                </p>
            </div>
        }
        
        <div class="retry-section">
            <h5><i class="fas fa-redo"></i> تلاش مجدد</h5>
            <p class="mb-3">گاهی اوقات این خطا موقتی است. می‌توانید دوباره تلاش کنید:</p>
            <button onclick="retryPage()" class="retry-btn" id="retryBtn">
                <i class="fas fa-sync-alt"></i>
                <span id="retryText">تلاش مجدد</span>
            </button>
            <div class="mt-2">
                <small class="text-muted">
                    تلاش خودکار در <span class="countdown" id="countdown">30</span> ثانیه
                </small>
            </div>
        </div>
        
        <div class="error-actions">
            <a href="@Url.Action("Index", "Home")" class="btn-primary-custom">
                <i class="fas fa-home"></i>
                بازگشت به صفحه اصلی
            </a>
            
            <a href="javascript:history.back()" class="btn-secondary-custom">
                <i class="fas fa-arrow-right"></i>
                بازگشت به صفحه قبل
            </a>
        </div>
        
        <div class="mt-4">
            <p class="text-muted">
                <small>
                    اگر این مشکل ادامه داشت، لطفاً با 
                    <a href="mailto:<EMAIL>" class="text-primary">پشتیبانی</a> 
                    تماس بگیرید و شناسه درخواست را ارائه دهید.
                </small>
            </p>
        </div>
    </div>
</div>

<script>
    let countdownTimer;
    let retryAttempts = 0;
    const maxRetryAttempts = 3;
    
    function startCountdown() {
        let seconds = 30;
        const countdownElement = document.getElementById('countdown');
        
        countdownTimer = setInterval(() => {
            seconds--;
            countdownElement.textContent = seconds;
            
            if (seconds <= 0) {
                clearInterval(countdownTimer);
                if (retryAttempts < maxRetryAttempts) {
                    retryPage();
                } else {
                    countdownElement.textContent = '∞';
                    document.querySelector('.retry-section small').innerHTML = 
                        '<span class="text-warning">حداکثر تعداد تلاش‌های خودکار انجام شد</span>';
                }
            }
        }, 1000);
    }
    
    function retryPage() {
        retryAttempts++;
        const retryBtn = document.getElementById('retryBtn');
        const retryText = document.getElementById('retryText');
        
        // Disable button and show loading
        retryBtn.disabled = true;
        retryText.innerHTML = '<i class="fas fa-spinner fa-spin"></i> در حال تلاش...';
        
        // Clear countdown
        if (countdownTimer) {
            clearInterval(countdownTimer);
        }
        
        // Retry after 2 seconds
        setTimeout(() => {
            window.location.reload();
        }, 2000);
    }
    
    // Start countdown when page loads
    document.addEventListener('DOMContentLoaded', function() {
        startCountdown();
        
        // Add click handler for manual retry
        document.getElementById('retryBtn').addEventListener('click', function() {
            if (countdownTimer) {
                clearInterval(countdownTimer);
            }
            retryPage();
        });
    });
    
    // Report error to analytics (if available)
    if (typeof gtag !== 'undefined') {
        gtag('event', 'exception', {
            'description': 'Server Error 500',
            'fatal': false
        });
    }
</script>
