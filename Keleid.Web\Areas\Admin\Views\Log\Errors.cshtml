﻿@model Keleid.BLL.DTOs.ErrorLogPageDto
@{
    ViewBag.Title = "لاگ خطاها";
}

<!-- Left Sidebar -->
@await Html.PartialAsync("_LeftSidebar")

<!-- Main Content -->
<section class="content home">

    <!-- Breadcrumb -->
    @await Html.PartialAsync("_Breadcrumb")

            <div class="container-fluid">
            <!-- Basic Table -->
            <div class="row clearfix">
                <div class="col-lg-12 col-md-12 col-sm-12">
                    <div class="card">
                        <div class="header">
                            <h2>لاگ <strong>خطاها</strong></h2>
                            <ul class="header-dropdown m-r--5">
                                <li class="dropdown">
                                    <span class="badge badge-danger">@Model.TotalCount خطا</span>
                                </li>
                            </ul>
                        </div>
                        <div class="body table-responsive">
                            @if (Model.ErrorLogs.Any())
                            {
                                <table class="table table-striped m-b-0">
                                    <thead>
                                        <tr>
                                            <th>پیام خطا</th>
                                            <th>نوع Exception</th>
                                            <th>تاریخ</th>
                                            <th>جزئیات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var error in Model.ErrorLogs)
                                        {
                                            <tr>
                                                <td>
                                                    <span class="text-danger">
                                                        <i class="zmdi zmdi-alert-triangle"></i>
                                                        @error.Message
                                                    </span>
                                                    @if (!string.IsNullOrEmpty(error.Source))
                                                    {
                                                        <br><small class="text-muted">منبع: @error.Source</small>
                                                    }
                                                </td>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(error.Exception))
                                                    {
                                                        <code class="text-danger">@error.Exception</code>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">نامشخص</span>
                                                    }
                                                </td>
                                                <td>
                                                    <span>@error.RelativeTime</span>
                                                    <br><small class="text-muted">@error.FormattedDate</small>
                                                </td>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(error.StackTrace) || !string.IsNullOrEmpty(error.Properties))
                                                    {
                                                        <button class="btn btn-sm btn-outline-danger" type="button" data-toggle="collapse" data-target="#<EMAIL>()" aria-expanded="false">
                                                            <i class="zmdi zmdi-bug"></i> جزئیات
                                                        </button>
                                                        <div class="collapse mt-2" id="<EMAIL>()">
                                                            <div class="card card-body">
                                                                @if (!string.IsNullOrEmpty(error.StackTrace))
                                                                {
                                                                    <h6 class="text-danger">Stack Trace:</h6>
                                                                    <pre class="bg-light p-2 small">@error.StackTrace</pre>
                                                                }
                                                                @if (!string.IsNullOrEmpty(error.Properties))
                                                                {
                                                                    <h6 class="text-info mt-2">Properties:</h6>
                                                                    <code>@error.Properties</code>
                                                                }
                                                            </div>
                                                        </div>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">-</span>
                                                    }
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>

                                <!-- Pagination -->
                                @if (Model.TotalPages > 1)
                                {
                                    <div class="d-flex justify-content-between align-items-center mt-3">
                                        <div>
                                            <small class="text-muted">
                                                نمایش @((Model.CurrentPage - 1) * 20 + 1) تا @(Math.Min(Model.CurrentPage * 20, Model.TotalCount)) از @Model.TotalCount خطا
                                            </small>
                                        </div>
                                        <nav>
                                            <ul class="pagination pagination-sm">
                                                @if (Model.HasPreviousPage)
                                                {
                                                    <li class="page-item">
                                                        <a class="page-link" href="@Url.Action("Errors", new { page = Model.CurrentPage - 1 })">قبلی</a>
                                                    </li>
                                                }

                                                @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                                                {
                                                    <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                                        <a class="page-link" href="@Url.Action("Errors", new { page = i })">@i</a>
                                                    </li>
                                                }

                                                @if (Model.HasNextPage)
                                                {
                                                    <li class="page-item">
                                                        <a class="page-link" href="@Url.Action("Errors", new { page = Model.CurrentPage + 1 })">بعدی</a>
                                                    </li>
                                                }
                                            </ul>
                                        </nav>
                                    </div>
                                }
                            }
                            else
                            {
                                <div class="alert alert-success text-center">
                                    <i class="zmdi zmdi-check-circle"></i>
                                    هیچ خطایی یافت نشد! سیستم سالم است.
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>

</section>