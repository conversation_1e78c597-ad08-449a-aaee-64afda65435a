$(function(){$(".knob").knob({draw:function(){if("tron"==this.$.data("skin")){var t,i=this.angle(this.cv),s=this.startAngle,h=this.startAngle,r=h+i,e=!0;return this.g.lineWidth=this.lineWidth,this.o.cursor&&(h=r-.3)&&(r+=.3),this.o.displayPrevious&&(t=this.startAngle+this.angle(this.value),this.o.cursor&&(s=t-.3)&&(t+=.3),this.g.beginPath(),this.g.strokeStyle=this.previousColor,this.g.arc(this.xy,this.xy,this.radius-this.lineWidth,s,t,!1),this.g.stroke()),this.g.beginPath(),this.g.strokeStyle=e?this.o.fgColor:this.fgColor,this.g.arc(this.xy,this.xy,this.radius-this.lineWidth,h,r,!1),this.g.stroke(),this.g.lineWidth=2,this.g.beginPath(),this.g.strokeStyle=this.o.fgColor,this.g.arc(this.xy,this.xy,this.radius-this.lineWidth+1+2*this.lineWidth/3,0,2*Math.PI,!1),this.g.stroke(),!1}}})});