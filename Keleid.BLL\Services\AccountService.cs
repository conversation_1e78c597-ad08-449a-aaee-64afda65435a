﻿using Keleid.BLL.Services.Interfaces;
using Keleid.DAL;
using Keleid.DAL.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace Keleid.BLL.Services
{
    public class AccountService : IAccountService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ISmsService _smsService;

        public AccountService(UserManager<ApplicationUser> userManager, ApplicationDbContext dbContext, ISmsService smsService)
        {
            _userManager = userManager;
            _smsService = smsService;
        }

        public async Task<bool> IsPhoneNumberRegisteredAsync(string phoneNumber)
        {
            var user = await _userManager.Users
                .FirstOrDefaultAsync(u => u.PhoneNumber == phoneNumber);
            
            return user != null;
        }

        public async Task<bool> ValidateUserAsync(ApplicationUser user, string password)
        {
            return await _userManager.CheckPasswordAsync(user, password);
        }

        public async Task<bool> VerifyPhoneNumberCodeAsync(string phoneNumber, string code)
        {
            // استفاده از SmsService برای تایید کد
            var isCodeValid = await _smsService.VerifyCodeAsync(phoneNumber, code);

            if (!isCodeValid)
                return false;

            // اگر کد معتبر بود، کاربر را تایید کنیم
            var user = await _userManager.Users
                .FirstOrDefaultAsync(u => u.PhoneNumber == phoneNumber);

            if (user != null)
            {
                user.PhoneNumberConfirmed = true;
                await _userManager.UpdateAsync(user);
            }

            return true;
        }

        public async Task<IdentityResult> RegisterUserAsync(string phoneNumber)
        {
            var user = new ApplicationUser
            {
                UserName = phoneNumber,
                PhoneNumber = phoneNumber,
                RegisterDate = DateTime.UtcNow,
                IsActive = true,
                PhoneNumberConfirmed = false
            };

            return await _userManager.CreateAsync(user);
        }

        public async Task<bool> AddPasswordAsync(string phoneNumber, string password)
        {
            var user = await _userManager.Users.FirstOrDefaultAsync(u => u.PhoneNumber == phoneNumber);
            if (user == null) return false;

            var result = await _userManager.AddPasswordAsync(user, password);

            return result.Succeeded;
        }

        public async Task<bool> UserHasPasswordAsync(string phoneNumber)
        {
            var user = await _userManager.Users.FirstOrDefaultAsync(u => u.PhoneNumber == phoneNumber);
            if (user == null) return false;

            return await _userManager.HasPasswordAsync(user);
        }

        public async Task<bool> UserPhoneConfirmedAsync(string phoneNumber)
        {
            var user = await _userManager.Users.FirstOrDefaultAsync(u => u.PhoneNumber == phoneNumber);
            if (user == null) return false;

            return user.PhoneNumberConfirmed;
        }

        public async Task<bool> SendVerificationCodeAsync(string phoneNumber, string ipAddress)
        {
            return await _smsService.SendVerificationCodeAsync(phoneNumber, ipAddress);
        }

        public async Task<(bool canSend, int remainingSeconds)> CanSendVerificationCodeAsync(string phoneNumber)
        {
            try
            {
                var latestSms = await _smsService.GetLatestSmsAsync(phoneNumber);

                if (latestSms == null)
                {
                    // هیچ پیامکی ارسال نشده
                    return (true, 0);
                }

                var timeSinceLastSms = DateTime.UtcNow - latestSms.Date;
                var cooldownPeriod = TimeSpan.FromMinutes(2);

                if (timeSinceLastSms >= cooldownPeriod)
                {
                    // می‌توان ارسال کرد
                    return (true, 0);
                }
                else
                {
                    // نمی‌توان ارسال کرد
                    var remainingTime = cooldownPeriod - timeSinceLastSms;
                    return (false, (int)remainingTime.TotalSeconds);
                }
            }
            catch (Exception)
            {
                // در صورت خطا، اجازه ارسال ندهیم
                return (false, 120); // 2 دقیقه به عنوان پیش‌فرض
            }
        }

        public async Task<(bool canSend, string message)> CanSendVerificationCodeWithIpCheckAsync(string phoneNumber, string ipAddress)
        {
            try
            {
                // بررسی محدودیت IP
                var canSendFromIp = await _smsService.CanSendFromIpAsync(ipAddress);
                if (!canSendFromIp)
                {
                    var ipSmsCount = await _smsService.GetSmsCountFromIpInLastHourAsync(ipAddress);
                    return (false, $"شما در ساعت گذشته {ipSmsCount} پیامک دریافت کرده‌اید. لطفاً کمی صبر کنید");
                }

                // بررسی محدودیت شماره تلفن
                var (canSendToPhone, remainingSeconds) = await CanSendVerificationCodeAsync(phoneNumber);
                if (!canSendToPhone)
                {
                    var minutes = remainingSeconds / 60;
                    var seconds = remainingSeconds % 60;
                    var timeMessage = minutes > 0
                        ? $"{minutes} دقیقه و {seconds} ثانیه"
                        : $"{seconds} ثانیه";

                    return (false, $"لطفاً {timeMessage} صبر کنید");
                }

                return (true, "");
            }
            catch (Exception)
            {
                return (false, "خطای سیستمی رخ داده است");
            }
        }
    }
}