using Keleid.BLL.Services;
using Microsoft.AspNetCore.Mvc;

namespace Keleid.Web.Areas.Admin.ViewComponents
{
    public class TaskHeaderViewComponent : ViewComponent
    {
        private readonly UserTaskService _taskService;

        public TaskHeaderViewComponent(UserTaskService taskService)
        {
            _taskService = taskService;
        }

        public async Task<IViewComponentResult> InvokeAsync()
        {
            var tasks = await _taskService.GetPendingTasksAsync(5);
            var pendingCount = await _taskService.GetPendingTaskCountAsync();
            
            ViewBag.PendingCount = pendingCount;
            
            return View(tasks);
        }
    }
}
