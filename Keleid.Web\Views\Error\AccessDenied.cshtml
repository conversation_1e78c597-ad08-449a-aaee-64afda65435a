@{
    ViewBag.Title = "دسترسی مجاز نیست - 403";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<style>
    .error-container {
        min-height: 70vh;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 2rem;
    }
    
    .error-content {
        max-width: 600px;
        margin: 0 auto;
    }
    
    .error-code {
        font-size: 8rem;
        font-weight: bold;
        color: #e74c3c;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    
    .error-title {
        font-size: 2rem;
        color: #2c3e50;
        margin-bottom: 1rem;
        font-weight: 600;
    }
    
    .error-description {
        font-size: 1.1rem;
        color: #7f8c8d;
        margin-bottom: 2rem;
        line-height: 1.6;
    }
    
    .error-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .btn-primary-custom {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        color: white;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .btn-primary-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        color: white;
        text-decoration: none;
    }
    
    .btn-secondary-custom {
        background: transparent;
        border: 2px solid #95a5a6;
        padding: 10px 28px;
        border-radius: 25px;
        color: #7f8c8d;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .btn-secondary-custom:hover {
        border-color: #7f8c8d;
        color: #2c3e50;
        text-decoration: none;
        transform: translateY(-1px);
    }
    
    .error-icon {
        font-size: 4rem;
        color: #e74c3c;
        margin-bottom: 1rem;
        opacity: 0.8;
    }
    
    .login-suggestion {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 2rem 0;
        border-left: 4px solid #e74c3c;
    }
    
    .login-suggestion h5 {
        color: #2c3e50;
        margin-bottom: 1rem;
    }
    
    @@media (max-width: 768px) {
        .error-code {
            font-size: 5rem;
        }
        
        .error-title {
            font-size: 1.5rem;
        }
        
        .error-actions {
            flex-direction: column;
            align-items: center;
        }
    }
</style>

<div class="error-container">
    <div class="error-content">
        <div class="error-icon">
            <i class="fas fa-lock"></i>
        </div>
        
        <div class="error-code">403</div>
        
        <h1 class="error-title">@ViewBag.ErrorMessage</h1>
        
        <p class="error-description">
            @ViewBag.ErrorDescription
        </p>
        
        @if (!User.Identity.IsAuthenticated)
        {
            <div class="login-suggestion">
                <h5><i class="fas fa-sign-in-alt"></i> ورود به حساب کاربری</h5>
                <p class="mb-3">برای دسترسی به این بخش، ابتدا وارد حساب کاربری خود شوید:</p>
                <a href="@Url.Action("Index", "Account")" class="btn btn-success">
                    <i class="fas fa-user"></i>
                    ورود / ثبت‌نام
                </a>
            </div>
        }
        else
        {
            <div class="login-suggestion">
                <h5><i class="fas fa-user-shield"></i> سطح دسترسی</h5>
                <p class="mb-0">
                    شما با حساب کاربری <strong>@User.Identity.Name</strong> وارد شده‌اید، 
                    اما سطح دسترسی لازم برای مشاهده این صفحه را ندارید.
                </p>
            </div>
        }
        
        <div class="error-actions">
            <a href="@Url.Action("Index", "Home")" class="btn-primary-custom">
                <i class="fas fa-home"></i>
                بازگشت به صفحه اصلی
            </a>
            
            @if (User.Identity.IsAuthenticated)
            {
                <a href="@Url.Action("Index", "User")" class="btn-secondary-custom">
                    <i class="fas fa-user"></i>
                    پنل کاربری
                </a>
            }
            else
            {
                <a href="@Url.Action("Index", "Account")" class="btn-secondary-custom">
                    <i class="fas fa-sign-in-alt"></i>
                    ورود به حساب
                </a>
            }
        </div>
        
        <div class="mt-4">
            <p class="text-muted">
                <small>
                    اگر فکر می‌کنید باید به این صفحه دسترسی داشته باشید، لطفاً با 
                    <a href="mailto:<EMAIL>" class="text-primary">پشتیبانی</a> 
                    تماس بگیرید.
                </small>
            </p>
        </div>
    </div>
</div>
