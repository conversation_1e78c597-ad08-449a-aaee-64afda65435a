﻿@model UserSidebarViewModel
@using Keleid.Web.Components
@using Microsoft.AspNetCore.Mvc.Rendering

@{
    string currentAction = ViewContext.RouteData.Values["action"]?.ToString();

    string IsActive(string action) =>
        (action == currentAction) ? "active" : "";
}

<div class="col-lg-3">
    <div class="user-panel-sidebar bg-white rounded-3 shadow-sm p-3">
        <div class="user-info mb-4 text-center">
            <div class="user-avatar mb-3">
                <i class="fas fa-user-circle fa-4x text-muted"></i>
            </div>
            <h6 class="mb-1">کاربر کلید</h6>
            <p class="text-muted small mb-0">@Model.PhoneNumber</p>
        </div>
        <div class="panel-menu">
            <a asp-controller="User" asp-action="Index" class="panel-menu-item @IsActive("Index")">
                <i class="fas fa-user"></i>
                <span>کاربر کلید</span>
                <span class="text-muted small">@Model.PhoneNumber</span>
            </a>
            <a asp-controller="User" asp-action="MyAds" class="panel-menu-item @IsActive("MyAds")">
                <i class="fas fa-clipboard-list"></i>
                <span>آگهی‌های من</span>
            </a>
            <a asp-controller="User" asp-action="Favs" class="panel-menu-item @IsActive("Favs")">
                <i class="fas fa-bookmark"></i>
                <span>نشان‌ها</span>
            </a>
            <form asp-controller="User" asp-action="Logout" method="post" style="margin: 0;">
                @Html.AntiForgeryToken()
                <button type="submit" class="panel-menu-item" style="border: none; background: none; width: 100%; text-align: right; cursor: pointer;">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>خروج</span>
                </button>
            </form>
        </div>
    </div>
</div>