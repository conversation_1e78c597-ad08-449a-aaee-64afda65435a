using System.ComponentModel.DataAnnotations;
using Keleid.BLL.DTOs;

namespace Keleid.Web.ViewModels
{
    public class AdsLocationViewModel
    {
        public int CategoryId { get; set; }
        public string CategoryTitle { get; set; } = "";

        // Location Information
        [Required(ErrorMessage = "انتخاب استان الزامی است")]
        [Display(Name = "استان")]
        public string Province { get; set; } = "";

        [Required(ErrorMessage = "انتخاب شهر الزامی است")]
        [Display(Name = "شهر")]
        public string City { get; set; } = "";

        [Required(ErrorMessage = "آدرس الزامی است")]
        [StringLength(500, MinimumLength = 10, ErrorMessage = "آدرس باید بین 10 تا 500 کاراکتر باشد")]
        [Display(Name = "آدرس")]
        public string Address { get; set; } = "";

        // Contact Information
        [Required(ErrorMessage = "نام الزامی است")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "نام باید بین 2 تا 100 کاراکتر باشد")]
        [Display(Name = "نام")]
        public string ContactName { get; set; } = "";

        [Required(ErrorMessage = "شماره تماس الزامی است")]
        [RegularExpression(@"^09\d{9}$", ErrorMessage = "شماره تماس باید با 09 شروع شده و 11 رقم باشد")]
        [Display(Name = "شماره تماس")]
        public string ContactPhone { get; set; } = "";

        [EmailAddress(ErrorMessage = "فرمت ایمیل صحیح نیست")]
        [Display(Name = "ایمیل (اختیاری)")]
        public string? ContactEmail { get; set; } = "";

        // لیست استان‌ها و شهرها از دیتابیس
        public List<ProvinceDto> Provinces { get; set; } = new List<ProvinceDto>();
        public List<CityDto> Cities { get; set; } = new List<CityDto>();
    }
}
