@model List<Keleid.BLL.DTOs.UserTaskDto>

<li class="dropdown">
    <a href="javascript:void(0);" class="dropdown-toggle" data-toggle="dropdown" role="button">
        <i class="zmdi zmdi-flag"></i>
        @if (ViewBag.PendingCount > 0)
        {
            <div class="notify"><span class="heartbit"></span><span class="point"></span></div>
        }
    </a>
    <ul class="dropdown-menu dropdown-menu-right slideDown">
        <li class="header">وظایف (@ViewBag.PendingCount)</li>
        <li class="body">
            <ul class="menu tasks list-unstyled">
                @if (Model.Any())
                {
                    @foreach (var task in Model)
                    {
                        <li>
                            <a href="/Admin/Ads/Waiting"
                               @(task.Status ? "" : $"onclick=\"markTaskAsCompleted({task.Id})\"")>
                                <div class="icon-circle @(task.Status ? "bg-green" : "bg-light-blue")">
                                    <i class="zmdi zmdi-settings"></i>
                                </div>
                                <div class="menu-info">
                                    <h4>@task.Title</h4>
                                    <p>
                                        <i class="zmdi zmdi-time"></i> @task.RelativeTime
                                    </p>
                                </div>
                            </a>
                        </li>
                    }
                }
                else
                {
                    <li>
                        <div class="menu-info text-center">
                            <p>وظیفه جدیدی وجود ندارد</p>
                        </div>
                    </li>
                }
            </ul>
        </li>
        <li class="footer">
            <a asp-area="Admin" asp-controller="Home" asp-action="Tasks">نمایش همه</a>
        </li>
    </ul>
</li>

<script>
    function markTaskAsCompleted(taskId) {
        fetch('/Admin/Home/MarkTaskAsCompleted', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            body: JSON.stringify({ id: taskId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        });
    }
</script>
