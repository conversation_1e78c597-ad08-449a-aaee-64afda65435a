﻿using Keleid.DAL.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;

namespace Keleid.Web.Components
{
    public class UserSidebarViewComponent : ViewComponent
    {
        private readonly UserManager<ApplicationUser> _userManager;

        public UserSidebarViewComponent(UserManager<ApplicationUser> userManager)
        {
            _userManager = userManager;
        }

        public async Task<IViewComponentResult> InvokeAsync()
        {
            var user = await _userManager.GetUserAsync(HttpContext.User);
            var viewModel = new UserSidebarViewModel
            {
                PhoneNumber = user?.PhoneNumber ?? "نامشخص"
            };

            return View(viewModel);
        }
    }

    public class UserSidebarViewModel
    {
        public string PhoneNumber { get; set; }
    }
}
