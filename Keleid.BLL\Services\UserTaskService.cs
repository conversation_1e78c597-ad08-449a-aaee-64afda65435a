using Keleid.BLL.DTOs;
using Keleid.DAL;
using Keleid.DAL.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Keleid.BLL.Services
{
    public class UserTaskService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<UserTaskService> _logger;

        public UserTaskService(ApplicationDbContext context, ILogger<UserTaskService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<UserTaskDto>> GetRecentTasksAsync(int count = 5)
        {
            try
            {
                var tasks = await _context.Tasks
                    .OrderByDescending(t => t.Date)
                    .Take(count)
                    .ToListAsync();

                return tasks.Select(MapToDto).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recent tasks");
                return new List<UserTaskDto>();
            }
        }

        public async Task<List<UserTaskDto>> GetPendingTasksAsync(int count = 5)
        {
            try
            {
                var tasks = await _context.Tasks
                    .Where(t => !t.Status)
                    .OrderByDescending(t => t.Date)
                    .Take(count)
                    .ToListAsync();

                return tasks.Select(MapToDto).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending tasks");
                return new List<UserTaskDto>();
            }
        }

        public async Task<int> GetPendingTaskCountAsync()
        {
            try
            {
                return await _context.Tasks
                    .Where(t => !t.Status)
                    .CountAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending task count");
                return 0;
            }
        }

        public async Task<bool> MarkAsCompletedAsync(int taskId, string? userId = null)
        {
            try
            {
                var task = await _context.Tasks.FindAsync(taskId);
                if (task != null)
                {
                    task.Status = true;
                    if (!string.IsNullOrEmpty(userId))
                    {
                        task.UserId = userId;
                    }
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking task as completed: {TaskId}", taskId);
                return false;
            }
        }

        public async Task<(List<UserTaskDto> tasks, int totalCount, int totalPages)> GetTasksWithPaginationAsync(int pageNumber = 1, int pageSize = 10)
        {
            try
            {
                var query = _context.Tasks.OrderByDescending(t => t.Date);

                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                var tasks = await query
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var taskDtos = tasks.Select(MapToDto).ToList();

                return (taskDtos, totalCount, totalPages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting tasks with pagination");
                return (new List<UserTaskDto>(), 0, 0);
            }
        }

        public async Task<bool> CreateAdvertisementApprovalTaskAsync(int advertisementId, string categoryTitle)
        {
            try
            {
                var task = new UserTask
                {
                    Title = "تایید آگهی",
                    Body = $"آگهی جدید در دسته‌بندی {categoryTitle} در انتظار تایید می‌باشد",
                    Date = DateTime.Now,
                    Status = false,
                    UserId = null // برای همه ادمین‌ها
                };

                _context.Tasks.Add(task);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Advertisement approval task created for advertisement {AdvertisementId}", advertisementId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating advertisement approval task for advertisement {AdvertisementId}", advertisementId);
                return false;
            }
        }

        private UserTaskDto MapToDto(UserTask task)
        {
            return new UserTaskDto
            {
                Id = task.Id,
                Title = task.Title,
                Body = task.Body,
                Date = task.Date,
                Status = task.Status,
                UserId = task.UserId,
                RelativeTime = GetRelativeTime(task.Date)
            };
        }

        private string GetRelativeTime(DateTime date)
        {
            var timeSpan = DateTime.Now - date;

            if (timeSpan.TotalMinutes < 15)
                return "لحظاتی پیش";
            else if (timeSpan.TotalMinutes < 30)
                return "ربع ساعت پیش";
            else if (timeSpan.TotalMinutes < 60)
                return "نیم ساعت پیش";
            else if (timeSpan.TotalHours < 2)
                return "یک ساعت پیش";
            else if (date.Date == DateTime.Today)
                return "امروز";
            else if (date.Date == DateTime.Today.AddDays(-1))
                return "دیروز";
            else
                return date.ToString("dd MMMM yyyy", new System.Globalization.CultureInfo("fa-IR"));
        }
    }
}
