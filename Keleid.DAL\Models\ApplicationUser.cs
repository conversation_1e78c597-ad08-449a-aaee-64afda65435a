﻿using Microsoft.AspNetCore.Identity;

namespace Keleid.DAL.Models
{
    public class ApplicationUser : IdentityUser
    {
        public DateTime RegisterDate { get; set; } = DateTime.UtcNow;
        public bool IsActive { get; set; } = true;

        public ICollection<Advertisement> Advertisements { get; set; }
        public ICollection<Favorite> Favorites { get; set; }
        public ICollection<Notification> Notifications { get; set; }
        public ICollection<UserTask> UserTasks { get; set; }
    }
}
