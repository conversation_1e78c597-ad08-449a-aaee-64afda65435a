using Keleid.BLL.Services;
using Keleid.BLL.Services.Interfaces;
using Keleid.DAL;
using Keleid.DAL.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.EntityFrameworkCore;
using System.Text.Encodings.Web;
using System.Text.Unicode;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddControllersWithViews();

#region Session
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromHours(8);
    options.Cookie.HttpOnly = true;
    options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
});
#endregion

#region EntityFramework
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("cs"))
    .UseLoggerFactory(LoggerFactory.Create(builder => { }))
    .EnableSensitiveDataLogging(false));
#endregion

#region Identity
builder.Services.AddIdentity<ApplicationUser, IdentityRole>(options =>
{
    options.Password.RequireDigit = false;
    options.Password.RequiredLength = 6;
    options.Password.RequireNonAlphanumeric = false;
    options.Password.RequireUppercase = false;
    options.Password.RequireLowercase = false;
    options.Password.RequiredUniqueChars = 0;

    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(5);
    options.Lockout.MaxFailedAccessAttempts = 5;
    options.Lockout.AllowedForNewUsers = true;

    options.User.RequireUniqueEmail = false;
})
.AddEntityFrameworkStores<ApplicationDbContext>();

// تنظیم مسیرهای احراز هویت
builder.Services.ConfigureApplicationCookie(options =>
{
    options.LoginPath = "/Account/Index";
    options.AccessDeniedPath = "/User/Index";
    options.LogoutPath = "/Account/Logout";
    options.ExpireTimeSpan = TimeSpan.FromDays(30); // کوکی احراز هویت 30 روز معتبر باشد
    options.SlidingExpiration = true; // تمدید خودکار زمان انقضا با هر درخواست
});
#endregion

#region Servcies
builder.Services.AddScoped<ICategoryService, CategoryService>();
builder.Services.AddScoped<IAccountService, AccountService>();
builder.Services.AddScoped<ISmsService, SmsService>();
builder.Services.AddScoped<ILocationService, LocationService>();
builder.Services.AddScoped<Keleid.Web.Services.IFileService, Keleid.Web.Services.FileService>();
// ابتدا سرویس‌های Notification و Task را ثبت می‌کنیم
builder.Services.AddScoped<NotificationService>();
builder.Services.AddScoped<UserTaskService>();
builder.Services.AddScoped<DashboardService>();
builder.Services.AddScoped<UserService>();
builder.Services.AddScoped<LogService>();
builder.Services.AddScoped<LoggingService>();
// سپس AdvertisementService که به آن‌ها وابسته است
builder.Services.AddScoped<IAdvertisementService, AdvertisementService>();

// Sitemap services
builder.Services.AddScoped<ISitemapService, SitemapService>();
builder.Services.AddHostedService<Keleid.Web.Services.SitemapUpdateService>();

// Add HttpContextAccessor for logging
builder.Services.AddHttpContextAccessor();
#endregion

#region HtmlEncoder
var settings = new TextEncoderSettings();
settings.AllowRanges(UnicodeRanges.BasicLatin, UnicodeRanges.Arabic);
settings.AllowCharacters('\u200C'); // اجازه نمایش نیم فاصله به صورت حقیقی

builder.Services.AddSingleton<HtmlEncoder>(HtmlEncoder.Create(settings));
#endregion

var app = builder.Build();

if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    app.UseStatusCodePagesWithReExecute("/Error/{0}");
    app.UseHsts();
}
else
{
    app.UseDeveloperExceptionPage();
}

app.UseHttpsRedirection();
app.UseStaticFiles(new StaticFileOptions
{
    ContentTypeProvider = new FileExtensionContentTypeProvider
    {
        Mappings = { [".apk"] = "application/vnd.android.package-archive" }
    }
});
app.UseSession();
app.UseRouting();

// Add Global Exception Middleware
app.UseMiddleware<Keleid.Web.Middleware.GlobalExceptionMiddleware>();

app.UseAuthentication();
app.UseAuthorization();

// Middleware برای مدیریت دسترسی به بخش ادمین
app.Use(async (context, next) =>
{
    var path = context.Request.Path.Value?.ToLower();

    // بررسی دسترسی به بخش ادمین
    if (path != null && path.StartsWith("/admin"))
    {
        if (!context.User.Identity.IsAuthenticated)
        {
            context.Response.Redirect("/Account/Index");
            return;
        }
        else if (!context.User.IsInRole("Admin"))
        {
            context.Response.Redirect("/User/Index");
            return;
        }
    }

    await next();
});

app.MapControllerRoute(
    name: "areas",
    pattern: "{area:exists}/{controller}/{action}/{id?}");

// Route برای جستجو
app.MapControllerRoute(
    name: "search",
    pattern: "search/{searchTerm}",
    defaults: new { controller = "Home", action = "Search" });

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action}/{id?}");

using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    await Seed.SeedData(services);
}

// Setup event handlers for sitemap updates
var serviceProvider = app.Services;
var logger = serviceProvider.GetRequiredService<ILogger<Program>>();

AdvertisementService.OnAdvertisementStatusChanged += async () =>
{
    await Keleid.Web.Services.SitemapUpdateTrigger.TriggerUpdateAsync(serviceProvider, logger);
};

CategoryService.OnCategoryChanged += async () =>
{
    await Keleid.Web.Services.SitemapUpdateTrigger.TriggerUpdateAsync(serviceProvider, logger);
};

app.Run();