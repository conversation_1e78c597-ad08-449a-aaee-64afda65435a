﻿@model Keleid.BLL.DTOs.NotificationPageDto
@{
    ViewBag.Title = "اعلان ها";
}

<!-- Left Sidebar -->
@await Html.PartialAsync("_LeftSidebar")

<!-- Main Content -->
<section class="content home">

    <!-- Breadcrumb -->
    @await Html.PartialAsync("_Breadcrumb")

    <div class="container-fluid">
        <!-- Basic Table -->
        <div class="row clearfix">
            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="card">
                    <div class="header">
                        <h2>لیست <strong>اعلان ها</strong> (صفحه @Model.CurrentPage از @Model.TotalPages - مجموع @Model.TotalCount اعلان)</h2>
                    </div>
                    <div class="body table-responsive">
                        <table class="table table-striped m-b-0">
                            <thead>
                                <tr>
                                    <th>عنوان</th>
                                    <th data-breakpoints="xs">پیام</th>
                                    <th data-breakpoints="xs">تاریخ</th>
                                    <th data-breakpoints="xs">وضعیت</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model.Notifications.Any())
                                {
                                    @foreach (var notification in Model.Notifications)
                                    {
                                        <tr>
                                            <td>@notification.Title</td>
                                            <td>@notification.Body</td>
                                            <td>@notification.RelativeTime</td>
                                            <td>
                                                @if (notification.Status)
                                                {
                                                    <span class="badge badge-success">خوانده شده</span>
                                                }
                                                else
                                                {
                                                    <span class="badge badge-warning">خوانده نشده</span>
                                                    <a class="mx-2" href="#" onclick="markNotificationAsRead(@notification.Id)" title="علامت‌گذاری به عنوان خوانده شده">
                                                        <i class="zmdi zmdi-check-all text-success"></i>
                                                    </a>
                                                }
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="4" class="text-center">اعلانی وجود ندارد</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if (Model.TotalPages > 1)
                    {
                        <div class="d-flex justify-content-between align-items-center mt-3 px-3">
                            <div>
                                نمایش @((Model.CurrentPage - 1) * 10 + 1) تا @(Math.Min(Model.CurrentPage * 10, Model.TotalCount)) از @Model.TotalCount اعلان
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm">
                                    @if (Model.HasPreviousPage)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" asp-area="Admin" asp-controller="Home" asp-action="Notifications" asp-route-page="@(Model.CurrentPage - 1)">قبلی</a>
                                        </li>
                                    }

                                    @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                                    {
                                        <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                            <a class="page-link" asp-area="Admin" asp-controller="Home" asp-action="Notifications" asp-route-page="@i">@i</a>
                                        </li>
                                    }

                                    @if (Model.HasNextPage)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" asp-area="Admin" asp-controller="Home" asp-action="Notifications" asp-route-page="@(Model.CurrentPage + 1)">بعدی</a>
                                        </li>
                                    }
                                </ul>
                            </nav>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

</section>

<script>
    function markNotificationAsRead(notificationId) {
        fetch('/Admin/Home/MarkNotificationAsRead', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            body: JSON.stringify({ id: notificationId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        });
    }
</script>