using Keleid.BLL.Services;
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics;

namespace Keleid.Web.Controllers
{
    public class ErrorController : Controller
    {
        private readonly LoggingService _loggingService;

        public ErrorController(LoggingService loggingService)
        {
            _loggingService = loggingService;
        }

        [Route("Error/{statusCode}")]
        public async Task<IActionResult> HttpStatusCodeHandler(int statusCode)
        {
            var statusCodeResult = HttpContext.Features.Get<Microsoft.AspNetCore.Diagnostics.IStatusCodeReExecuteFeature>();

            // لاگ کردن خطا
            await _loggingService.LogWarningAsync(
                message: $"خطای HTTP {statusCode} - صفحه یافت نشد",
                source: "ErrorController",
                properties: new
                {
                    StatusCode = statusCode,
                    OriginalPath = statusCodeResult?.OriginalPath,
                    OriginalQueryString = statusCodeResult?.OriginalQueryString,
                    UserAgent = Request.Headers["User-Agent"].ToString(),
                    Referer = Request.Headers["Referer"].ToString()
                }
            );

            switch (statusCode)
            {
                case 404:
                    ViewBag.ErrorMessage = "صفحه مورد نظر یافت نشد";
                    ViewBag.ErrorDescription = "متأسفانه صفحه‌ای که به دنبال آن هستید وجود ندارد یا ممکن است حذف شده باشد.";
                    ViewBag.StatusCode = 404;
                    ViewBag.OriginalPath = statusCodeResult?.OriginalPath;
                    return View("NotFound");

                case 500:
                    ViewBag.ErrorMessage = "خطای داخلی سرور";
                    ViewBag.ErrorDescription = "متأسفانه خطای غیرمنتظره‌ای در سرور رخ داده است. لطفاً چند دقیقه دیگر دوباره تلاش کنید.";
                    ViewBag.StatusCode = 500;
                    return View("ServerError");

                case 403:
                    ViewBag.ErrorMessage = "دسترسی مجاز نیست";
                    ViewBag.ErrorDescription = "شما مجوز دسترسی به این صفحه را ندارید.";
                    ViewBag.StatusCode = 403;
                    return View("AccessDenied");

                default:
                    ViewBag.ErrorMessage = $"خطای {statusCode}";
                    ViewBag.ErrorDescription = "خطای غیرمنتظره‌ای رخ داده است.";
                    ViewBag.StatusCode = statusCode;
                    return View("GenericError");
            }
        }

        [Route("Error")]
        public async Task<IActionResult> Error()
        {
            var requestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier;
            var exceptionFeature = HttpContext.Features.Get<Microsoft.AspNetCore.Diagnostics.IExceptionHandlerFeature>();

            if (exceptionFeature != null)
            {
                // لاگ کردن خطای غیرمنتظره
                await _loggingService.LogErrorAsync(
                    message: "خطای غیرمنتظره در اپلیکیشن",
                    exception: exceptionFeature.Error,
                    source: "ErrorController",
                    properties: new
                    {
                        RequestId = requestId,
                        Path = exceptionFeature.Path,
                        UserAgent = Request.Headers["User-Agent"].ToString(),
                        Referer = Request.Headers["Referer"].ToString()
                    }
                );
            }

            ViewBag.RequestId = requestId;
            ViewBag.ErrorMessage = "خطای غیرمنتظره";
            ViewBag.ErrorDescription = "متأسفانه خطای غیرمنتظره‌ای رخ داده است. اگر این مشکل ادامه داشت، لطفاً با پشتیبانی تماس بگیرید.";
            ViewBag.StatusCode = 500;

            return View("ServerError");
        }
    }
}
