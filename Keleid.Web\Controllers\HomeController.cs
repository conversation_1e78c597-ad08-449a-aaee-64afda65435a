using Keleid.BLL.DTOs;
using Keleid.BLL.Services;
using Keleid.BLL.Services.Interfaces;
using Keleid.DAL.Models;
using Keleid.Web.ViewModels;
using Keleid.Web.Extensions;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;

namespace Keleid.Web.Controllers
{
    public class HomeController : BaseController
    {
        private readonly ICategoryService _categoryService;
        private readonly IAdvertisementService _advertisementService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly LoggingService _loggingService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public HomeController(ICategoryService categoryService, IAdvertisementService advertisementService, UserManager<ApplicationUser> userManager, LoggingService loggingService, IHttpContextAccessor httpContextAccessor)
        {
            _categoryService = categoryService;
            _advertisementService = advertisementService;
            _userManager = userManager;
            _loggingService = loggingService;
            _httpContextAccessor = httpContextAccessor;
        }

        [Route("{slug?}")]
        public async Task<IActionResult> Index(string? slug, string? search)
        {
            // خواندن استان انتخابی از کوکی
            var selectedProvince = Request.Cookies["selectedProvince"] ?? "کل ایران";

            var categories = await _categoryService.GetMainCategoriesWithSubCategoriesAsync();
            var (latestAds, totalCount, hasMore) = await _advertisementService.GetLatestAdvertisementsWithPaginationAsync(1, 6, slug, selectedProvince, search);

            CategoryDto? selectedCategory = null;
            if (!string.IsNullOrEmpty(slug))
            {
                selectedCategory = await _categoryService.GetCategoryBySlugAsync(slug);
            }

            var viewModel = new HomeIndexViewModel
            {
                Categories = categories,
                LatestAdvertisements = latestAds,
                SelectedCategory = selectedCategory,
                SelectedSlug = slug,
                SearchTerm = search,
                HasMoreAds = hasMore,
                CurrentPage = 1
            };

            // ViewBag.SelectedProvince از BaseController تنظیم می‌شود

            return View(viewModel);
        }

        [HttpPost]
        [Route("LoadMoreAds")]
        public async Task<IActionResult> LoadMoreAds(int page, string? slug = null, string? search = null)
        {
            try
            {
                // خواندن استان انتخابی از کوکی
                var selectedProvince = Request.Cookies["selectedProvince"] ?? "کل ایران";

                var (advertisements, totalCount, hasMore) = await _advertisementService.GetLatestAdvertisementsWithPaginationAsync(page, 6, slug, selectedProvince, search);

                var result = new
                {
                    success = true,
                    advertisements = advertisements.Select(ad => new
                    {
                        id = ad.Id,
                        title = ad.Title,
                        slug = ad.Slug,
                        formattedPrice = ad.FormattedPrice,
                        locationDisplay = ad.LocationDisplay,
                        mainImageUrl = ad.MainImageUrl
                    }),
                    hasMore = hasMore
                };

                return Json(result);
            }
            catch (Exception)
            {
                return Json(new { success = false, message = "خطا در بارگذاری آگهی‌ها" });
            }
        }

        [Route("search/{searchTerm}")]
        public async Task<IActionResult> Search(string searchTerm)
        {
            // اگر searchTerm خالی باشد، به صفحه اصلی برگردان
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return RedirectToAction("Index");
            }

            // خواندن استان انتخابی از کوکی
            var selectedProvince = Request.Cookies["selectedProvince"] ?? "کل ایران";

            var categories = await _categoryService.GetMainCategoriesWithSubCategoriesAsync();
            var (searchResults, totalCount, hasMore) = await _advertisementService.GetLatestAdvertisementsWithPaginationAsync(1, 6, null, selectedProvince, searchTerm);

            // لاگ کردن جستجو
            var userId = User.Identity?.IsAuthenticated == true ? User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value : null;
            await _loggingService.LogSearchAsync(searchTerm, searchResults.Count, userId, _httpContextAccessor);

            var viewModel = new HomeIndexViewModel
            {
                Categories = categories,
                LatestAdvertisements = searchResults,
                SelectedCategory = null,
                SelectedSlug = null,
                SearchTerm = searchTerm,
                HasMoreAds = hasMore,
                CurrentPage = 1
            };

            return View("Index", viewModel);
        }

        [Route("{id:int}/{slug}")]
        public async Task<IActionResult> Ads(int id, string slug)
        {
            var advertisement = await _advertisementService.GetAdvertisementByIdAndSlugAsync(id, slug);

            if (advertisement == null)
            {
                return NotFound();
            }

            // بررسی وضعیت نشان کردن برای کاربر لاگین شده
            bool isFavorited = false;
            if (User.Identity.IsAuthenticated)
            {
                var user = await _userManager.GetUserAsync(User);
                if (user != null)
                {
                    isFavorited = await _advertisementService.IsAdvertisementFavoritedAsync(id, user.Id);
                }
            }

            ViewBag.IsFavorited = isFavorited;
            ViewBag.AdvertisementId = id;

            return View(advertisement);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ToggleFavorite(int id)
        {
            if (!User.Identity.IsAuthenticated)
            {
                return Json(new { success = false, message = "برای نشان کردن آگهی باید وارد حساب کاربری خود شوید", requireLogin = true });
            }

            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "کاربر یافت نشد", requireLogin = true });
            }

            var isFavorited = await _advertisementService.ToggleFavoriteAsync(id, user.Id);

            var message = isFavorited ? "آگهی به نشان شده‌ها اضافه شد" : "آگهی از نشان شده‌ها حذف شد";

            return Json(new {
                success = true,
                message = message,
                isFavorited = isFavorited
            });
        }
    }
}
