using System.Globalization;

namespace Keleid.BLL.Helpers
{
    public static class PersianDateHelper
    {
        private static readonly PersianCalendar PersianCalendar = new PersianCalendar();
        
        private static readonly string[] PersianMonthNames = {
            "فروردین", "اردیبهش<PERSON>", "خ<PERSON><PERSON><PERSON>", "تیر", "مرد<PERSON>", "شهریور",
            "مهر", "آب<PERSON>", "آذر", "دی", "بهمن", "اسفند"
        };

        /// <summary>
        /// تبدیل تاریخ میلادی به شمسی
        /// </summary>
        /// <param name="gregorianDate">تاریخ میلادی</param>
        /// <returns>تاریخ شمسی به صورت رشته</returns>
        public static string ToPersianDate(DateTime gregorianDate)
        {
            var year = PersianCalendar.GetYear(gregorianDate);
            var month = PersianCalendar.GetMonth(gregorianDate);
            var day = PersianCalendar.GetDayOfMonth(gregorianDate);
            
            return $"{day} {PersianMonthNames[month - 1]} {year}";
        }

        /// <summary>
        /// محاسبه زمان نسبی به فارسی
        /// </summary>
        /// <param name="dateTime">تاریخ مورد نظر</param>
        /// <returns>زمان نسبی به فارسی</returns>
        public static string GetRelativeTime(DateTime dateTime)
        {
            var now = DateTime.Now;
            var timeSpan = now - dateTime;
            
            // اگر زیر یک ربع بود
            if (timeSpan.TotalMinutes < 15)
            {
                return "لحظاتی پیش";
            }
            
            // بین یک ربع تا زیر نیم ساعت
            if (timeSpan.TotalMinutes >= 15 && timeSpan.TotalMinutes < 30)
            {
                return "یک ربع پیش";
            }
            
            // بین نیم ساعت تا زیر یک ساعت
            if (timeSpan.TotalMinutes >= 30 && timeSpan.TotalHours < 1)
            {
                return "نیم ساعت پیش";
            }
            
            // بین یک ساعت تا دو ساعت
            if (timeSpan.TotalHours >= 1 && timeSpan.TotalHours < 2)
            {
                return "یک ساعت پیش";
            }
            
            // بالای دو ساعت تا پایان امروز
            if (timeSpan.TotalHours >= 2 && dateTime.Date == now.Date)
            {
                return "امروز";
            }
            
            // دیروز
            if (dateTime.Date == now.Date.AddDays(-1))
            {
                return "دیروز";
            }
            
            // قبل از دیروز - نمایش تاریخ شمسی
            return ToPersianDate(dateTime);
        }

        /// <summary>
        /// تبدیل تاریخ میلادی به شمسی با فرمت کوتاه
        /// </summary>
        /// <param name="gregorianDate">تاریخ میلادی</param>
        /// <returns>تاریخ شمسی کوتاه مثل 1403/08/13</returns>
        public static string ToPersianDateShort(DateTime gregorianDate)
        {
            var year = PersianCalendar.GetYear(gregorianDate);
            var month = PersianCalendar.GetMonth(gregorianDate);
            var day = PersianCalendar.GetDayOfMonth(gregorianDate);
            
            return $"{year:0000}/{month:00}/{day:00}";
        }

        /// <summary>
        /// دریافت نام ماه شمسی
        /// </summary>
        /// <param name="monthNumber">شماره ماه (1-12)</param>
        /// <returns>نام ماه به فارسی</returns>
        public static string GetPersianMonthName(int monthNumber)
        {
            if (monthNumber < 1 || monthNumber > 12)
                return "";
                
            return PersianMonthNames[monthNumber - 1];
        }
    }
}
