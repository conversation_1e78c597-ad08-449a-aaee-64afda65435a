@{
    ViewBag.Title = "صفحه یافت نشد - 404";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<style>
    .error-container {
        min-height: 70vh;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 2rem;
    }
    
    .error-content {
        max-width: 600px;
        margin: 0 auto;
    }
    
    .error-code {
        font-size: 8rem;
        font-weight: bold;
        color: #e74c3c;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    
    .error-title {
        font-size: 2rem;
        color: #2c3e50;
        margin-bottom: 1rem;
        font-weight: 600;
    }
    
    .error-description {
        font-size: 1.1rem;
        color: #7f8c8d;
        margin-bottom: 2rem;
        line-height: 1.6;
    }
    
    .error-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .btn-primary-custom {
        background: linear-gradient(135deg, #3498db, #2980b9);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        color: white;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .btn-primary-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        color: white;
        text-decoration: none;
    }
    
    .btn-secondary-custom {
        background: transparent;
        border: 2px solid #95a5a6;
        padding: 10px 28px;
        border-radius: 25px;
        color: #7f8c8d;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .btn-secondary-custom:hover {
        border-color: #7f8c8d;
        color: #2c3e50;
        text-decoration: none;
        transform: translateY(-1px);
    }
    
    .error-icon {
        font-size: 4rem;
        color: #e74c3c;
        margin-bottom: 1rem;
        opacity: 0.8;
    }
    
    .search-suggestion {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 2rem 0;
        border-left: 4px solid #3498db;
    }
    
    .search-suggestion h5 {
        color: #2c3e50;
        margin-bottom: 1rem;
    }
    
    .search-form {
        display: flex;
        gap: 10px;
        max-width: 400px;
        margin: 0 auto;
    }
    
    .search-input {
        flex: 1;
        padding: 10px 15px;
        border: 2px solid #ecf0f1;
        border-radius: 25px;
        outline: none;
        transition: border-color 0.3s ease;
    }
    
    .search-input:focus {
        border-color: #3498db;
    }
    
    .search-btn {
        background: #3498db;
        border: none;
        padding: 10px 20px;
        border-radius: 25px;
        color: white;
        cursor: pointer;
        transition: background 0.3s ease;
    }
    
    .search-btn:hover {
        background: #2980b9;
    }
    
    @@media (max-width: 768px) {
        .error-code {
            font-size: 5rem;
        }
        
        .error-title {
            font-size: 1.5rem;
        }
        
        .error-actions {
            flex-direction: column;
            align-items: center;
        }
        
        .search-form {
            flex-direction: column;
        }
    }
</style>

<div class="error-container">
    <div class="error-content">
        <div class="error-icon">
            <i class="fas fa-search"></i>
        </div>
        
        <div class="error-code">404</div>
        
        <h1 class="error-title">@ViewBag.ErrorMessage</h1>
        
        <p class="error-description">
            @ViewBag.ErrorDescription
        </p>
        
        @if (!string.IsNullOrEmpty(ViewBag.OriginalPath))
        {
            <div class="alert alert-info">
                <small><strong>مسیر درخواستی:</strong> @ViewBag.OriginalPath</small>
            </div>
        }

        <div class="error-actions">
            <a href="@Url.Action("Index", "Home")" class="btn-primary-custom">
                <i class="fas fa-home"></i>
                بازگشت به صفحه اصلی
            </a>
            
            <a href="javascript:history.back()" class="btn-secondary-custom">
                <i class="fas fa-arrow-right"></i>
                بازگشت به صفحه قبل
            </a>
        </div>
        
        <div class="mt-4">
            <p class="text-muted">
                <small>
                    اگر فکر می‌کنید این یک خطا است، لطفاً با 
                    <a href="mailto:<EMAIL>" class="text-primary">پشتیبانی</a> 
                    تماس بگیرید.
                </small>
            </p>
        </div>
    </div>
</div>

<script>
    // Auto focus on search input
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.focus();
        }
    });
</script>
