﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Keleid.DAL.Migrations
{
    /// <inheritdoc />
    public partial class AddIpAddressToSms : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "IpAddress",
                table: "Sms<PERSON>",
                type: "nvarchar(max)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IpAdd<PERSON>",
                table: "Sms<PERSON>");
        }
    }
}
