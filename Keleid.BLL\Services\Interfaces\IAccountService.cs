using Keleid.DAL.Models;
using Microsoft.AspNetCore.Identity;

namespace Keleid.BLL.Services.Interfaces
{
    public interface IAccountService
    {
        Task<bool> IsPhoneNumberRegisteredAsync(string phoneNumber);
        Task<bool> ValidateUserAsync(ApplicationUser user, string password);
        Task<bool> VerifyPhoneNumberCodeAsync(string phoneNumber, string code);
        Task<IdentityResult> RegisterUserAsync(string phoneNumber);
        Task<bool> AddPasswordAsync(string phoneNumber, string password);
        Task<bool> UserHasPasswordAsync(string phoneNumber);
        Task<bool> UserPhoneConfirmedAsync(string phoneNumber);
        Task<bool> SendVerificationCodeAsync(string phoneNumber, string ipAddress);
        Task<(bool canSend, int remainingSeconds)> CanSendVerificationCodeAsync(string phoneNumber);
        Task<(bool canSend, string message)> CanSendVerificationCodeWithIpCheckAsync(string phoneNumber, string ipAddress);
    }
}
