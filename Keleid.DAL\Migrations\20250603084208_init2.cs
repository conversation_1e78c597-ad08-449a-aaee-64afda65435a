﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Keleid.DAL.Migrations
{
    /// <inheritdoc />
    public partial class init2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "IsForRent",
                table: "Advertisements",
                newName: "IsPriceless");

            migrationBuilder.AddColumn<string>(
                name: "Addresss",
                table: "Locations",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Addresss",
                table: "Locations");

            migrationBuilder.RenameColumn(
                name: "IsPriceless",
                table: "Advertisements",
                newName: "IsForRent");
        }
    }
}
