﻿@model List<Keleid.BLL.DTOs.CategoryWithSubCategoriesDto>
@{
    ViewBag.Title = "لیست دسته بندی ها";
}

<!-- Left Sidebar -->
@await Html.PartialAsync("_LeftSidebar")

<!-- Main Content -->
<section class="content home">

    <!-- Breadcrumb -->
    @await Html.PartialAsync("_Breadcrumb")

    <div class="container-fluid">
        <!-- Basic Table -->
        <div class="row clearfix">
            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="card">
                    <div class="header">
                        <h2>لیست <strong>دسته بندی ها</strong></h2>
                        <ul class="header-dropdown m-r--5">
                            <li>
                                <a asp-area="Admin" asp-controller="Category" asp-action="Add" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i> افزودن دسته اصلی
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="body table-responsive">
                        <table class="table table-striped m-b-0">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-hashtag"></i> شناسه</th>
                                    <th><i class="fas fa-folder"></i> دسته اصلی</th>
                                    <th><i class="fas fa-folder-open"></i> زیرمجموعه</th>
                                    <th><i class="fas fa-list"></i> تعداد زیردسته</th>
                                    <th><i class="fas fa-cogs"></i> عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model != null && Model.Any())
                                {
                                    @foreach (var category in Model)
                                    {
                                        <!-- دسته اصلی -->
                                        <tr class="table-primary">
                                            <td><strong>#@category.Id</strong></td>
                                            <td>
                                                <i class="@category.IconClass text-primary me-2"></i>
                                                <strong>@category.Title</strong>
                                            </td>
                                            <td>-</td>
                                            <td>
                                                <span class="badge badge-info">@category.SubCategories.Count زیردسته</span>
                                            </td>
                                            <td>
                                                <a asp-area="Admin" asp-controller="Category" asp-action="AddSubcategory" asp-route-parentId="@category.Id"
                                                   class="btn btn-sm btn-primary" title="افزودن زیرمجموعه">
                                                    <i class="fas fa-plus"></i> زیرمجموعه
                                                </a>
                                                <a asp-area="Admin" asp-controller="Category" asp-action="EditTitle" asp-route-id="@category.Id"
                                                   class="btn btn-sm btn-warning" title="ویرایش عنوان">
                                                    <i class="fas fa-edit"></i> ویرایش
                                                </a>
                                                <button type="button" class="btn btn-sm btn-danger"
                                                        onclick="confirmDelete(@category.Id, '@category.Title', 'main')" title="حذف">
                                                    <i class="fas fa-trash"></i> حذف
                                                </button>
                                            </td>
                                        </tr>

                                        <!-- زیردسته‌ها -->
                                        @foreach (var subCategory in category.SubCategories)
                                        {
                                            <tr>
                                                <td><span class="text-muted">#@subCategory.Id</span></td>
                                                <td><span class="text-muted">└─</span></td>
                                                <td>
                                                    <i class="fas fa-folder-open text-secondary me-2"></i>
                                                    @subCategory.Title
                                                </td>
                                                <td>-</td>
                                                <td>
                                                    <a asp-area="Admin" asp-controller="Category" asp-action="EditFeatures" asp-route-id="@subCategory.Id"
                                                       class="btn btn-sm btn-info" title="ویرایش ویژگی‌ها">
                                                        <i class="fas fa-list"></i> ویژگی‌ها
                                                    </a>
                                                    <a asp-area="Admin" asp-controller="Category" asp-action="EditTitle" asp-route-id="@subCategory.Id"
                                                       class="btn btn-sm btn-warning" title="ویرایش عنوان">
                                                        <i class="fas fa-edit"></i> ویرایش
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger"
                                                            onclick="confirmDelete(@subCategory.Id, '@subCategory.Title', 'sub')" title="حذف">
                                                        <i class="fas fa-trash"></i> حذف
                                                    </button>
                                                </td>
                                            </tr>
                                        }
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="5" class="text-center text-muted">
                                            <i class="fas fa-folder-open fa-3x mb-3"></i>
                                            <p>هیچ دسته‌بندی یافت نشد</p>
                                            <a asp-area="Admin" asp-controller="Category" asp-action="Add" class="btn btn-primary">
                                                <i class="fas fa-plus"></i> افزودن اولین دسته‌بندی
                                            </a>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

</section>

<script>
function confirmDelete(categoryId, categoryTitle, type) {
    const typeText = type === 'main' ? 'دسته اصلی' : 'زیردسته';
    const message = `آیا از حذف ${typeText} "${categoryTitle}" اطمینان دارید؟`;

    if (confirm(message)) {
        // ایجاد فرم برای ارسال درخواست حذف
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '@Url.Action("Delete", "Category", new { area = "Admin" })';

        // اضافه کردن CSRF token
        const csrfToken = document.querySelector('input[name="__RequestVerificationToken"]');
        if (csrfToken) {
            const tokenInput = document.createElement('input');
            tokenInput.type = 'hidden';
            tokenInput.name = '__RequestVerificationToken';
            tokenInput.value = csrfToken.value;
            form.appendChild(tokenInput);
        }

        // اضافه کردن شناسه دسته‌بندی
        const idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'id';
        idInput.value = categoryId;
        form.appendChild(idInput);

        // ارسال فرم
        document.body.appendChild(form);
        form.submit();
    }
}
</script>