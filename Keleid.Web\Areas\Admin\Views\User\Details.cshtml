﻿@model Keleid.BLL.DTOs.UserDetailDto
@{
    ViewBag.Title = "جزئیات کاربر";
}

<!-- Left Sidebar -->
@await Html.PartialAsync("_LeftSidebar")

<!-- Main Content -->
<section class="content home">

    <!-- Breadcrumb -->
    @await Html.PartialAsync("_Breadcrumb")

    <div class="container-fluid">
        <!-- User Info Card -->
        <div class="row clearfix">
            <div class="col-lg-4 col-md-6 col-sm-12">
                <div class="card">
                    <div class="header bg-primary text-white">
                        <h2 style="color: white !important;"><i class="zmdi zmdi-account"></i> <strong style="color: white !important;">اطلاعات کاربر</strong></h2>
                    </div>
                    <div class="body">
                        <div class="user-info">
                            <div class="info-item mb-3">
                                <strong><i class="zmdi zmdi-phone text-primary"></i> شماره تماس:</strong>
                                <span class="d-block mt-1">@Model.PhoneNumber</span>
                            </div>

                            @if (!string.IsNullOrEmpty(Model.Email))
                            {
                                <div class="info-item mb-3">
                                    <strong><i class="zmdi zmdi-email text-info"></i> ایمیل:</strong>
                                    <span class="d-block mt-1">@Model.Email</span>
                                </div>
                            }

                            <div class="info-item mb-3">
                                <strong><i class="zmdi zmdi-calendar text-success"></i> تاریخ ثبت‌نام:</strong>
                                <span class="d-block mt-1">@Model.RelativeRegisterTime</span>
                                <small class="text-muted">@Model.RegisterDate.ToString("yyyy/MM/dd HH:mm")</small>
                            </div>

                            <div class="info-item mb-3">
                                <strong><i class="zmdi zmdi-power text-warning"></i> وضعیت:</strong>
                                <span class="badge @Model.StatusClass mt-1">@Model.StatusText</span>
                            </div>

                            <div class="info-item mb-3">
                                <strong><i class="zmdi zmdi-check-circle text-success"></i> تایید شماره:</strong>
                                <span class="badge @Model.PhoneConfirmedClass mt-1">@Model.PhoneConfirmedText</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Card -->
            <div class="col-lg-8 col-md-6 col-sm-12">
                <div class="card">
                    <div class="header bg-info text-white">
                        <h2 style="color: white !important;"><i class="zmdi zmdi-chart"></i> <strong style="color: white !important;">آمار فعالیت</strong></h2>
                    </div>
                    <div class="body">
                        <div class="row">
                            <div class="col-lg-3 col-md-6 col-sm-6">
                                <div class="info-box bg-blue hover-expand-effect">
                                    <div class="icon">
                                        <i class="zmdi zmdi-speaker"></i>
                                    </div>
                                    <div class="content">
                                        <div class="text" style="white-space: nowrap;">کل آگهی‌ها</div>
                                        <div class="number">@Model.TotalAdvertisements</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 col-sm-6">
                                <div class="info-box bg-green hover-expand-effect">
                                    <div class="icon">
                                        <i class="zmdi zmdi-check"></i>
                                    </div>
                                    <div class="content">
                                        <div class="text" style="white-space: nowrap;">تایید شده</div>
                                        <div class="number">@Model.ApprovedAdvertisements</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 col-sm-6">
                                <div class="info-box bg-orange hover-expand-effect">
                                    <div class="icon">
                                        <i class="zmdi zmdi-time"></i>
                                    </div>
                                    <div class="content">
                                        <div class="text" style="white-space: nowrap;">در انتظار</div>
                                        <div class="number">@Model.PendingAdvertisements</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 col-sm-6">
                                <div class="info-box bg-red hover-expand-effect">
                                    <div class="icon">
                                        <i class="zmdi zmdi-close"></i>
                                    </div>
                                    <div class="content">
                                        <div class="text" style="white-space: nowrap;">رد شده</div>
                                        <div class="number">@Model.RejectedAdvertisements</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-lg-6 col-md-12">
                                <div class="info-box bg-purple hover-expand-effect">
                                    <div class="icon">
                                        <i class="zmdi zmdi-favorite"></i>
                                    </div>
                                    <div class="content">
                                        <div class="text" style="white-space: nowrap;">علاقه‌مندی‌ها</div>
                                        <div class="number">@Model.TotalFavorites</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-12">
                                <div class="text-center">
                                    <a href="@Url.Action("Index")" class="btn btn-secondary">
                                        <i class="zmdi zmdi-arrow-left"></i> بازگشت به لیست
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="row clearfix">
            <!-- Recent Advertisements -->
            <div class="col-lg-6 col-md-12">
                <div class="card">
                    <div class="header bg-success text-white">
                        <h2 style="color: white !important;"><i class="zmdi zmdi-speaker"></i> <strong style="color: white !important;">آخرین آگهی‌ها</strong></h2>
                    </div>
                    <div class="body">
                        @if (Model.RecentAdvertisements.Any())
                        {
                            @foreach (var ad in Model.RecentAdvertisements)
                            {
                                <div class="media mb-3">
                                    <img src="@ad.MainImageUrl" alt="@ad.Title" class="media-object rounded" style="width: 60px; height: 60px; object-fit: cover;">
                                    <div class="media-body mr-3">
                                        <h6 class="media-heading">
                                            <strong>@ad.Title</strong>
                                            <span class="badge @(ad.IsApproved ? "badge-success" : ad.IsDeleted ? "badge-danger" : "badge-warning") float-left">
                                                @(ad.IsApproved ? "تایید شده" : ad.IsDeleted ? "رد شده" : "در انتظار")
                                            </span>
                                        </h6>
                                        <p class="mb-1">
                                            <i class="zmdi zmdi-label text-muted"></i> @ad.CategoryTitle
                                            <span class="mx-2">|</span>
                                            <i class="zmdi zmdi-pin text-muted"></i> @ad.Province، @ad.City
                                        </p>
                                        <small class="text-muted">
                                            <i class="zmdi zmdi-time"></i> @ad.RelativeTime
                                            @if (!ad.IsPriceless)
                                            {
                                                <span class="mx-2">|</span>
                                                <i class="zmdi zmdi-money text-success"></i> @ad.FormattedPrice
                                            }
                                        </small>
                                    </div>
                                </div>
                                <hr>
                            }
                        }
                        else
                        {
                            <div class="alert alert-info text-center">
                                <i class="zmdi zmdi-info"></i>
                                هیچ آگهی‌ای یافت نشد.
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Recent Favorites -->
            <div class="col-lg-6 col-md-12">
                <div class="card">
                    <div class="header bg-warning text-white">
                        <h2 style="color: white !important;"><i class="zmdi zmdi-favorite"></i> <strong style="color: white !important;">آخرین علاقه‌مندی‌ها</strong></h2>
                    </div>
                    <div class="body">
                        @if (Model.RecentFavorites.Any())
                        {
                            @foreach (var favorite in Model.RecentFavorites)
                            {
                                <div class="media mb-3">
                                    <img src="@favorite.Advertisement.MainImageUrl" alt="@favorite.Advertisement.Title" class="media-object rounded" style="width: 60px; height: 60px; object-fit: cover;">
                                    <div class="media-body mr-3">
                                        <h6 class="media-heading">
                                            <strong>@favorite.Advertisement.Title</strong>
                                        </h6>
                                        <p class="mb-1">
                                            <i class="zmdi zmdi-label text-muted"></i> @favorite.Advertisement.CategoryTitle
                                            <span class="mx-2">|</span>
                                            <i class="zmdi zmdi-pin text-muted"></i> @favorite.Advertisement.Province، @favorite.Advertisement.City
                                        </p>
                                        <small class="text-muted">
                                            <i class="zmdi zmdi-favorite text-danger"></i> @favorite.GetFavoriteTime()
                                            @if (!favorite.Advertisement.IsPriceless)
                                            {
                                                <span class="mx-2">|</span>
                                                <i class="zmdi zmdi-money text-success"></i> @favorite.Advertisement.FormattedPrice
                                            }
                                        </small>
                                    </div>
                                </div>
                                <hr>
                            }
                        }
                        else
                        {
                            <div class="alert alert-info text-center">
                                <i class="zmdi zmdi-info"></i>
                                هیچ علاقه‌مندی‌ای یافت نشد.
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

</section>