// PWA Installation and Service Worker Registration
// Use global variable to avoid conflicts
window.deferredPrompt = null;

// Simple approach: always show notifications, only hide when manually dismissed
// Reset on every page load/navigation
window.notificationsDismissed = {
  android: false,
  ios: false
};

// Register Service Worker
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then(registration => {
        console.log('SW registered: ', registration);

        // Listen for updates
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                // New service worker is available
                showUpdateNotification();
              }
            });
          }
        });
      })
      .catch(registrationError => {
        console.log('SW registration failed: ', registrationError);
      });
  });

  // Listen for messages from service worker
  navigator.serviceWorker.addEventListener('message', event => {
    if (event.data && event.data.type === 'CACHE_UPDATED') {
      console.log('Cache updated from:', event.data.oldCaches, 'to:', event.data.newCache);
      showUpdateNotification();
    }
  });
}

// Device detection functions
function isDesktop() {
  return !/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) &&
         window.innerWidth > 768;
}

function isAndroid() {
  return /Android/i.test(navigator.userAgent);
}

function isIOS() {
  return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
}

function isInWebView() {
  // Check if running inside APK WebView
  const userAgent = window.navigator.userAgent;

  // Android WebView detection
  const isAndroidWebView = userAgent.includes('wv') || // WebView indicator
                          userAgent.includes('Version/') && userAgent.includes('Chrome') && userAgent.includes('Mobile') ||
                          window.AndroidInterface !== undefined; // Custom interface from Android app

  // iOS WebView detection
  const isIOSWebView = window.webkit?.messageHandlers !== undefined ||
                      (userAgent.includes('iPhone') || userAgent.includes('iPad')) && !userAgent.includes('Safari');

  // Additional checks for common WebView patterns
  const hasWebViewPattern = userAgent.includes('WebView') ||
                           userAgent.includes('AppWebView') ||
                           userAgent.includes('KeleidApp'); // Custom app identifier if you add one

  return isAndroidWebView || isIOSWebView || hasWebViewPattern;
}

// Handle PWA install prompt
window.addEventListener('beforeinstallprompt', (e) => {
  // Prevent the mini-infobar from appearing on mobile
  e.preventDefault();
  // Remove any existing pwa-install-btn
  removePWAInstallButton();
  // Stash the event so it can be triggered later
  window.deferredPrompt = e;
  // Show appropriate notification based on device
  showDeviceSpecificNotification();
});

// Show device-specific notification
function showDeviceSpecificNotification() {
  // Don't show any notification if in WebView (APK app)
  if (isInWebView()) {
    return;
  }

  // Don't show any notification on desktop
  if (isDesktop()) {
    return;
  }

  // Show Android notification
  if (isAndroid()) {
    showAndroidNotification();
  }

  // Show iOS notification
  if (isIOS()) {
    showIOSNotification();
  }
}

// Show Android install notification
function showAndroidNotification() {
  // Don't show if dismissed in this page session
  if (window.notificationsDismissed.android) {
    return;
  }

  // Remove existing notification first
  const existingNotification = document.getElementById('android-install-notification');
  if (existingNotification) {
    existingNotification.remove();
  }

  // Always create new notification
  const notification = document.createElement('div');
  notification.id = 'android-install-notification';
  notification.className = 'alert alert-success position-fixed';
  notification.style.cssText = `
    top: 20px;
    left: 20px;
    right: 20px;
    z-index: 1060;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    animation: slideDown 0.3s ease;
  `;
  notification.innerHTML = `
    <div class="d-flex align-items-center justify-content-between">
      <div class="d-flex align-items-center">
        <i class="fab fa-android text-success me-2" style="font-size: 1.5rem;"></i>
        <div>
          <strong>نصب اپلیکیشن کلید</strong><br>
          <small>اپلیکیشن را روی گوشی خود نصب کنید</small>
        </div>
      </div>
      <div class="d-flex align-items-center">
        <a href="/Info/Download" class="btn btn-success btn-sm me-2">
          <i class="fas fa-download me-1"></i>نصب
        </a>
        <button type="button" class="btn-close" onclick="dismissAndroidNotification()"></button>
      </div>
    </div>
  `;
  document.body.appendChild(notification);
}

// Show iOS install notification
function showIOSNotification() {
  // Don't show if dismissed in this page session
  if (window.notificationsDismissed.ios) {
    return;
  }

  // Remove existing notification first
  const existingNotification = document.getElementById('ios-install-notification');
  if (existingNotification) {
    existingNotification.remove();
  }

  // Always create new notification
  const notification = document.createElement('div');
  notification.id = 'ios-install-notification';
  notification.className = 'alert alert-info position-fixed';
  notification.style.cssText = `
    top: 20px;
    left: 20px;
    right: 20px;
    z-index: 1060;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    animation: slideDown 0.3s ease;
  `;
  notification.innerHTML = `
    <div class="d-flex align-items-start justify-content-between">
      <div class="d-flex align-items-start">
        <i class="fab fa-apple text-primary me-2" style="font-size: 1.5rem;"></i>
        <div>
          <strong>نصب اپلیکیشن کلید</strong><br>
          <small>
            برای نصب: دکمه اشتراک‌گذاری <i class="fas fa-share"></i> را فشار دهید<br>
            سپس "Add to Home Screen" را انتخاب کنید
          </small>
        </div>
      </div>
      <button type="button" class="btn-close" onclick="dismissIOSNotification()"></button>
    </div>
  `;
  document.body.appendChild(notification);
}

// Dismiss Android notification
function dismissAndroidNotification() {
  const notification = document.getElementById('android-install-notification');
  if (notification) {
    notification.style.animation = 'slideUp 0.3s ease';
    setTimeout(() => {
      notification.remove();
    }, 300);
    // Mark as dismissed for this page session only
    window.notificationsDismissed.android = true;
  }
}

// Dismiss iOS notification
function dismissIOSNotification() {
  const notification = document.getElementById('ios-install-notification');
  if (notification) {
    notification.style.animation = 'slideUp 0.3s ease';
    setTimeout(() => {
      notification.remove();
    }, 300);
    // Mark as dismissed for this page session only
    window.notificationsDismissed.ios = true;
  }
}

// Go to download page
function goToDownloadPage() {
  window.location.href = '/Info/Download';
}

// Install PWA (kept for download page usage)
async function installPWA() {
  if (!window.deferredPrompt) return;

  // Show the install prompt
  window.deferredPrompt.prompt();

  // Wait for the user to respond to the prompt
  const { outcome } = await window.deferredPrompt.userChoice;

  if (outcome === 'accepted') {
    console.log('User accepted the install prompt');
    hideInstallButton();
  } else {
    console.log('User dismissed the install prompt');
  }

  // Clear the deferredPrompt
  window.deferredPrompt = null;
}

// Hide install button (kept for compatibility)
function hideInstallButton() {
  // No longer needed but kept for compatibility
}

// Handle app installed event
window.addEventListener('appinstalled', () => {
  console.log('PWA was installed');
  hideInstallButton();
  
  // Show success message
  if (typeof bootstrap !== 'undefined') {
    const toast = document.createElement('div');
    toast.className = 'toast position-fixed';
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 1060;';
    toast.innerHTML = `
      <div class="toast-header">
        <i class="fas fa-check-circle text-success me-2"></i>
        <strong class="me-auto">کلید</strong>
        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
      </div>
      <div class="toast-body">
        اپلیکیشن با موفقیت نصب شد!
      </div>
    `;
    document.body.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
      document.body.removeChild(toast);
    });
  }
});

// Check if app is running in standalone mode
function isStandalone() {
  return window.matchMedia('(display-mode: standalone)').matches ||
         window.navigator.standalone === true;
}

// Show update notification
function showUpdateNotification() {
  // Don't show update notification if in WebView (APK app)
  if (isInWebView()) {
    console.log('Update notification skipped - running in WebView');
    return;
  }

  // Remove existing update notification first
  const existingNotification = document.getElementById('update-notification');
  if (existingNotification) {
    existingNotification.remove();
  }

  // Create update notification
  const notification = document.createElement('div');
  notification.id = 'update-notification';
  notification.className = 'alert alert-warning position-fixed';
  notification.style.cssText = `
    top: 20px;
    left: 20px;
    right: 20px;
    z-index: 1070;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    animation: slideDown 0.3s ease;
    border: 2px solid #ffc107;
  `;
  notification.innerHTML = `
    <div class="d-flex align-items-center justify-content-between">
      <div class="d-flex align-items-center">
        <i class="fas fa-sync-alt text-warning me-2" style="font-size: 1.5rem;"></i>
        <div>
          <strong>بروزرسانی جدید موجود است</strong><br>
          <small>برای دریافت آخرین تغییرات، صفحه را بروزرسانی کنید</small>
        </div>
      </div>
      <div class="d-flex align-items-center">
        <button type="button" class="btn btn-warning btn-sm me-2" onclick="reloadPage()">
          <i class="fas fa-redo me-1"></i>بروزرسانی
        </button>
        <button type="button" class="btn-close" onclick="dismissUpdateNotification()"></button>
      </div>
    </div>
  `;
  document.body.appendChild(notification);

  // Auto dismiss after 10 seconds if user doesn't interact
  setTimeout(() => {
    if (document.getElementById('update-notification')) {
      dismissUpdateNotification();
    }
  }, 10000);
}

// Dismiss update notification
function dismissUpdateNotification() {
  const notification = document.getElementById('update-notification');
  if (notification) {
    notification.style.animation = 'slideUp 0.3s ease';
    setTimeout(() => {
      notification.remove();
    }, 300);
  }
}

// Reload page to get updates
function reloadPage() {
  // Clear all caches and reload
  if ('caches' in window) {
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      );
    }).then(() => {
      window.location.reload(true);
    });
  } else {
    window.location.reload(true);
  }
}

// Make functions globally accessible
window.dismissAndroidNotification = dismissAndroidNotification;
window.dismissIOSNotification = dismissIOSNotification;
window.dismissUpdateNotification = dismissUpdateNotification;
window.reloadPage = reloadPage;

// Remove any existing pwa-install-btn if it exists
function removePWAInstallButton() {
  const existingBtn = document.getElementById('pwa-install-btn');
  if (existingBtn) {
    existingBtn.remove();
    console.log('Removed pwa-install-btn');
  }
}

// Set up mutation observer to automatically remove pwa-install-btn if it gets created
function setupPWAButtonRemovalObserver() {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          // Check if the added node is the pwa-install-btn
          if (node.id === 'pwa-install-btn') {
            node.remove();
            console.log('Auto-removed pwa-install-btn');
          }
          // Check if any child elements have the pwa-install-btn id
          const pwaBtn = node.querySelector && node.querySelector('#pwa-install-btn');
          if (pwaBtn) {
            pwaBtn.remove();
            console.log('Auto-removed child pwa-install-btn');
          }
        }
      });
    });
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
}

// Initialize notifications on page load
window.addEventListener('load', () => {
  // Log environment detection for debugging
  console.log('Environment detection:', {
    isWebView: isInWebView(),
    isStandalone: isStandalone(),
    isAndroid: isAndroid(),
    isIOS: isIOS(),
    isDesktop: isDesktop(),
    userAgent: navigator.userAgent
  });

  // Set up observer to automatically remove pwa-install-btn
  setupPWAButtonRemovalObserver();

  // Remove any existing pwa-install-btn
  removePWAInstallButton();

  // Reset dismissal flags on every page load
  window.notificationsDismissed.android = false;
  window.notificationsDismissed.ios = false;

  // Don't show notifications if already in standalone mode
  if (isStandalone()) {
    return;
  }

  // Show notification with delay
  setTimeout(() => {
    showDeviceSpecificNotification();
  }, 2000);
});

// Also reset on page visibility change (when user comes back to tab)
document.addEventListener('visibilitychange', () => {
  if (!document.hidden) {
    // Remove any existing pwa-install-btn
    removePWAInstallButton();

    // Reset dismissal flags when page becomes visible
    window.notificationsDismissed.android = false;
    window.notificationsDismissed.ios = false;

    // Show notifications again if not in standalone mode
    if (!isStandalone()) {
      setTimeout(() => {
        showDeviceSpecificNotification();
      }, 1000);
    }
  }
});
