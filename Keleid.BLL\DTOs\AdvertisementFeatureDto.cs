namespace Keleid.BLL.DTOs
{
    public class AdvertisementFeatureDto
    {
        public int Id { get; set; }
        public int AdvertisementId { get; set; }
        public int CategoryFeatureId { get; set; }
        public string FeatureTitle { get; set; }
        public string Value { get; set; }
        public string InputType { get; set; }
        public string? Options { get; set; }

        /// <summary>
        /// گزینه‌های انتخابی به صورت لیست
        /// </summary>
        public List<string> OptionsList =>
            string.IsNullOrEmpty(Options) ? new List<string>() : Options.Split('|').Select(o => o.Trim()).ToList();
    }
}
