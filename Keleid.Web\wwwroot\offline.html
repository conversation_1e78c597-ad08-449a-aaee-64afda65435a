<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>آفلاین - کلید</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #a62626 0%, #d63384 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .offline-container {
            text-align: center;
            padding: 2rem;
            max-width: 500px;
        }
        
        .offline-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }
        
        .offline-title {
            font-size: 2rem;
            margin-bottom: 1rem;
            font-weight: bold;
        }
        
        .offline-message {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .retry-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .retry-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        .offline-tips {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            font-size: 0.9rem;
        }
        
        .offline-tips h4 {
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }
        
        .offline-tips ul {
            text-align: right;
            margin: 0;
            padding-right: 1.5rem;
        }
        
        .offline-tips li {
            margin-bottom: 0.5rem;
        }
        
        @keyframes pulse {
            0% { opacity: 0.8; }
            50% { opacity: 1; }
            100% { opacity: 0.8; }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @media (max-width: 768px) {
            .offline-container {
                padding: 1rem;
            }
            
            .offline-title {
                font-size: 1.5rem;
            }
            
            .offline-message {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon pulse">📱</div>
        <h1 class="offline-title">شما آفلاین هستید</h1>
        <p class="offline-message">
            متأسفانه اتصال اینترنت شما قطع شده است. لطفاً اتصال خود را بررسی کنید و دوباره تلاش کنید.
        </p>
        
        <button class="retry-btn" onclick="window.location.reload()">
            🔄 تلاش مجدد
        </button>
        
        <div class="offline-tips">
            <h4>💡 نکات مفید:</h4>
            <ul>
                <li>اتصال Wi-Fi یا داده موبایل خود را بررسی کنید</li>
                <li>ممکن است برخی از صفحات قبلی در حافظه موجود باشند</li>
                <li>پس از برقراری اتصال، صفحه به‌طور خودکار به‌روزرسانی می‌شود</li>
            </ul>
        </div>
    </div>
    
    <script>
        // Check for connection and auto-reload
        function checkConnection() {
            if (navigator.onLine) {
                window.location.reload();
            }
        }
        
        // Listen for online event
        window.addEventListener('online', checkConnection);
        
        // Check connection every 5 seconds
        setInterval(checkConnection, 5000);
        
        // Update retry button text when checking
        function updateRetryButton() {
            const btn = document.querySelector('.retry-btn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '⏳ در حال بررسی...';
            btn.disabled = true;
            
            setTimeout(() => {
                if (!navigator.onLine) {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                }
            }, 2000);
        }
        
        document.querySelector('.retry-btn').addEventListener('click', updateRetryButton);
    </script>
</body>
</html>
