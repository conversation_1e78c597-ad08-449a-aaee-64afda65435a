﻿@await Html.PartialAsync("_Account")

<div class="login-container">
    <div class="login-box">
        <div class="login-header">
            <div class="logo">
                <a asp-controller="Home" asp-action="Index"><img src="/assets/img/logo.png" alt="لوگو" class="logo-img"></a>
            </div>
            <a asp-controller="Home" asp-action="Index" class="back-btn">
                <i class="fa fa-times"></i>
            </a>
            <h1>ورود | ثبت‌نام</h1>
            <p class="subtitle text-start">سلام!</p>
            <p class="description text-start">لطفا شماره موبایل خود را وارد کنید</p>
        </div>

        <form id="phoneForm" class="login-form">
            @Html.AntiForgeryToken()
            <div class="mb-3">
                <div class="phone-input">
                    <input type="tel" id="phone" name="phone" placeholder="***********" maxlength="11" required>
                </div>
                <div class="error-message" id="phoneError"></div>
            </div>

            <button type="submit" class="btn btn-danger w-100" id="continueBtn">
                <span id="btnText">ادامه</span>
                <i class="bi bi-arrow-left"></i>
            </button>
        </form>

        <div class="terms">
            <p>ورود شما به معنای پذیرش <a href="#">شرایط و قوانین</a> است</p>
        </div>
    </div>
</div>

@section js {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const phoneForm = document.getElementById('phoneForm');
            const phoneInput = document.getElementById('phone');
            const phoneError = document.getElementById('phoneError');
            const continueBtn = document.getElementById('continueBtn');
            const btnText = document.getElementById('btnText');

            phoneForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Clear previous errors
                phoneError.textContent = '';
                phoneError.classList.remove('show');

                const phoneNumber = phoneInput.value.trim();

                // Basic validation
                if (!phoneNumber) {
                    phoneError.textContent = 'لطفا شماره تلفن خود را وارد کنید';
                    phoneError.classList.add('show');
                    return;
                }

                if (phoneNumber.length !== 11 || !phoneNumber.startsWith('09')) {
                    phoneError.textContent = 'فرمت شماره تلفن صحیح نیست';
                    phoneError.classList.add('show');
                    return;
                }

                // Disable button and show loading
                continueBtn.disabled = true;
                btnText.textContent = 'در حال بررسی...';

                // Send AJAX request
                fetch('@Url.Action("CheckPhoneNumber", "Account")', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                    },
                    body: 'phoneNumber=' + encodeURIComponent(phoneNumber)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Redirect based on registration status
                        window.location.href = data.redirectUrl;
                    } else {
                        phoneError.textContent = data.message || 'خطایی رخ داده است';
                        phoneError.classList.add('show');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    phoneError.textContent = 'خطای ارتباط با سرور';
                    phoneError.classList.add('show');
                })
                .finally(() => {
                    // Re-enable button
                    continueBtn.disabled = false;
                    btnText.textContent = 'ادامه';
                });
            });
        });
    </script>
}