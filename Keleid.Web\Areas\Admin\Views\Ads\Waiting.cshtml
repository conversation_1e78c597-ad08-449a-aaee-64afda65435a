﻿@model Keleid.BLL.DTOs.AdvertisementPageDto
@{
    ViewBag.Title = "آگهی های در انتظار تایید";
}

<!-- Left Sidebar -->
@await Html.PartialAsync("_LeftSidebar")

<!-- Main Content -->
<section class="content home">

    <!-- Breadcrumb -->
    @await Html.PartialAsync("_Breadcrumb")

    <div class="container-fluid">
        <!-- Basic Table -->
        <div class="row clearfix">
            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="card">
                    <div class="header">
                        <h2>آگهی های <strong>در انتظار تایید</strong> (صفحه @Model.CurrentPage از @Model.TotalPages - مجموع @Model.TotalCount آگهی)</h2>
                    </div>
                    <div class="body table-responsive">
                        <table class="table table-striped m-b-0">
                            <thead>
                                <tr>
                                    <th>شماره</th>
                                    <th data-breakpoints="xs">عنوان</th>
                                    <th data-breakpoints="xs">کاربر</th>
                                    <th data-breakpoints="xs">دسته بندی</th>
                                    <th data-breakpoints="xs">تاریخ</th>
                                    <th data-breakpoints="xs">وضعیت</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model.Advertisements.Any())
                                {
                                    @foreach (var ad in Model.Advertisements)
                                    {
                                        <tr>
                                            <td>#@ad.Id</td>
                                            <td>@ad.Title</td>
                                            <td>@ad.UserPhone</td>
                                            <td>@ad.CategoryTitle</td>
                                            <td>@ad.RelativeTime</td>
                                            <td>
                                                <span class="badge badge-warning">در انتظار تایید</span>
                                            </td>
                                            <td>
                                                <a asp-area="Admin" asp-controller="Ads" asp-action="Details" asp-route-id="@ad.Id"
                                                   class="btn btn-primary btn-sm">جزئیات</a>

                                                <!-- آگهی در انتظار تایید دارای دکمه تایید است -->
                                                <form asp-area="Admin" asp-controller="Ads" asp-action="Approve" asp-route-id="@ad.Id"
                                                      method="post" style="display: inline;">
                                                    <input type="hidden" name="returnUrl" value="@@context.Request.Path@@context.Request.QueryString" />
                                                    <button type="submit" class="btn btn-success btn-sm"
                                                            onclick="return confirm('آیا از تایید این آگهی اطمینان دارید؟')">
                                                        تایید
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="7" class="text-center">آگهی در انتظار تایید وجود ندارد</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if (Model.TotalPages > 1)
                    {
                        <div class="d-flex justify-content-between align-items-center mt-3 px-3">
                            <div>
                                نمایش @((Model.CurrentPage - 1) * 10 + 1) تا @(Math.Min(Model.CurrentPage * 10, Model.TotalCount)) از @Model.TotalCount آگهی
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm">
                                    @if (Model.HasPreviousPage)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" asp-area="Admin" asp-controller="Ads" asp-action="Waiting" asp-route-page="@(Model.CurrentPage - 1)">قبلی</a>
                                        </li>
                                    }

                                    @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                                    {
                                        <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                            <a class="page-link" asp-area="Admin" asp-controller="Ads" asp-action="Waiting" asp-route-page="@i">@i</a>
                                        </li>
                                    }

                                    @if (Model.HasNextPage)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" asp-area="Admin" asp-controller="Ads" asp-action="Waiting" asp-route-page="@(Model.CurrentPage + 1)">بعدی</a>
                                        </li>
                                    }
                                </ul>
                            </nav>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

</section>