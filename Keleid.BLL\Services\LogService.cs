using Keleid.BLL.DTOs;
using Keleid.DAL;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace Keleid.BLL.Services
{
    public class LogService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<LogService> _logger;

        public LogService(ApplicationDbContext context, ILogger<LogService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<(List<SmsLogDto> smsLogs, int totalCount, int totalPages)> GetSmsLogsWithPaginationAsync(int pageNumber = 1, int pageSize = 20)
        {
            try
            {
                var query = _context.Smses.AsQueryable();

                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                var smsLogs = await query
                    .OrderByDescending(s => s.Date)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var smsLogDtos = smsLogs.Select(s => new SmsLogDto
                {
                    Id = s.Id,
                    Phone = s.Phone,
                    Body = s.Body,
                    Date = s.Date,
                    IpAddress = s.IpAddress,
                    RelativeTime = GetRelativeTime(s.Date),
                    FormattedDate = s.Date.ToString("yyyy/MM/dd HH:mm:ss")
                }).ToList();

                return (smsLogDtos, totalCount, totalPages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting SMS logs with pagination");
                return (new List<SmsLogDto>(), 0, 0);
            }
        }

        public async Task<(List<GeneralLogDto> generalLogs, int totalCount, int totalPages)> GetGeneralLogsAsync(int pageNumber = 1, int pageSize = 20)
        {
            try
            {
                var query = _context.GeneralLogs.AsQueryable();

                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                var generalLogs = await query
                    .OrderByDescending(g => g.Timestamp)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var generalLogDtos = generalLogs.Select(g => new GeneralLogDto
                {
                    Level = g.Level,
                    Message = g.Message,
                    Timestamp = g.Timestamp,
                    Exception = g.Exception,
                    Properties = g.Properties,
                    RelativeTime = GetRelativeTime(g.Timestamp),
                    FormattedDate = g.Timestamp.ToString("yyyy/MM/dd HH:mm:ss")
                }).ToList();

                return (generalLogDtos, totalCount, totalPages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting general logs");
                return (new List<GeneralLogDto>(), 0, 0);
            }
        }

        public async Task<(List<ErrorLogDto> errorLogs, int totalCount, int totalPages)> GetErrorLogsAsync(int pageNumber = 1, int pageSize = 20)
        {
            try
            {
                var query = _context.ErrorLogs.AsQueryable();

                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                var errorLogs = await query
                    .OrderByDescending(e => e.Timestamp)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var errorLogDtos = errorLogs.Select(e => new ErrorLogDto
                {
                    Message = e.Message,
                    Exception = e.Exception,
                    StackTrace = e.StackTrace,
                    Timestamp = e.Timestamp,
                    Source = e.Source,
                    Properties = e.Properties,
                    RelativeTime = GetRelativeTime(e.Timestamp),
                    FormattedDate = e.Timestamp.ToString("yyyy/MM/dd HH:mm:ss")
                }).ToList();

                return (errorLogDtos, totalCount, totalPages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting error logs");
                return (new List<ErrorLogDto>(), 0, 0);
            }
        }



        private string GetRelativeTime(DateTime dateTime)
        {
            var timeSpan = DateTime.Now - dateTime;

            if (timeSpan.TotalMinutes < 15)
                return "لحظاتی پیش";
            if (timeSpan.TotalMinutes < 30)
                return "ربع ساعت پیش";
            if (timeSpan.TotalMinutes < 60)
                return "نیم ساعت پیش";
            if (timeSpan.TotalHours < 2)
                return "یک ساعت پیش";
            if (timeSpan.TotalHours < 24)
                return "امروز";
            if (timeSpan.TotalDays < 2)
                return "دیروز";

            return dateTime.ToString("dd MMMM yyyy", new System.Globalization.CultureInfo("fa-IR"));
        }
    }
}
