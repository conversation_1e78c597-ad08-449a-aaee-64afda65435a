﻿using Keleid.DAL.Models;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace Keleid.DAL
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options) { }

        public DbSet<Category> Categories { get; set; }
        public DbSet<Location> Locations { get; set; }
        public DbSet<Advertisement> Advertisements { get; set; }
        public DbSet<AdImage> AdImages { get; set; }
        public DbSet<ContactInfo> ContactInfos { get; set; }
        public DbSet<Favorite> Favorites { get; set; }
        public DbSet<AdvertisementFeature> AdvertisementFeatures { get; set; }
        public DbSet<CategoryFeature> CategoryFeatures { get; set; }
        public DbSet<Sms> Smses { get; set; }
        public DbSet<Notification> Notifications { get; set; }
        public DbSet<UserTask> Tasks { get; set; }
        public DbSet<Province> Provinces { get; set; }
        public DbSet<City> Cities { get; set; }
        public DbSet<GeneralLog> GeneralLogs { get; set; }
        public DbSet<ErrorLog> ErrorLogs { get; set; }
        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            builder.Entity<Category>()
                .HasOne(c => c.ParentCategory)
                .WithMany(c => c.SubCategories)
                .HasForeignKey(c => c.ParentCategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Favorite>()
                .HasIndex(f => new { f.UserId, f.AdId })
            .IsUnique();

            builder.Entity<AdvertisementFeature>()
                .HasOne(af => af.CategoryFeature)
                .WithMany()
                .HasForeignKey(af => af.CategoryFeatureId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Advertisement>()
                .HasOne(a => a.ContactInfo)
                .WithOne(c => c.Advertisement)
                .HasForeignKey<ContactInfo>(c => c.AdId);

            builder.Entity<Favorite>()
                .HasOne(f => f.User)
                .WithMany(u => u.Favorites)
                .HasForeignKey(f => f.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Favorite>()
                .HasOne(f => f.Advertisement)
                .WithMany(a => a.Favorites)
                .HasForeignKey(f => f.AdId);

            // Configure relationship for Advertisement.ApprovedByUser
            builder.Entity<Advertisement>()
                .HasOne(a => a.ApprovedByUser)
                .WithMany()
                .HasForeignKey(a => a.ApprovedByUserId)
                .OnDelete(DeleteBehavior.NoAction);

        }
    }

}
