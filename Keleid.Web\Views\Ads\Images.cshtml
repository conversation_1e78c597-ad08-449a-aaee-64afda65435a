﻿@model Keleid.Web.ViewModels.AdsImagesViewModel
@{
    ViewData["Title"] = "تصاویر آگهی";
}

<!-- Header -->
@await Html.PartialAsync("_Se<PERSON><PERSON>eader", "ثبت آگهی")

<div class="container my-5">
    <!-- Step Indicator -->
    <div class="step-indicator mb-4">
        <div class="step-line"></div>
        <div class="step">
            <div class="step-circle">1</div>
            <div class="step-title">دسته‌بندی</div>
        </div>
        <div class="step">
            <div class="step-circle">2</div>
            <div class="step-title">مشخصات آگهی</div>
        </div>
        <div class="step active">
            <div class="step-circle">3</div>
            <div class="step-title">تصاویر</div>
        </div>
        <div class="step">
            <div class="step-circle">4</div>
            <div class="step-title">موقعیت و تماس</div>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <h5 class="card-title mb-4">تصاویر آگهی - @Model.CategoryTitle</h5>

            @if (TempData["Error"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    @TempData["Error"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <div class="alert alert-info">
                <i class="fas fa-info-circle ml-2"></i>
                حداکثر ۱۰ تصویر می‌توانید آپلود کنید. فرمت‌های مجاز: JPG، PNG، GIF - حداکثر حجم هر تصویر: ۱۰ مگابایت
            </div>

            <form id="imagesForm" method="post" enctype="multipart/form-data">
                <input type="hidden" name="categoryId" value="@Model.CategoryId" />
                <input type="hidden" name="mainImageIndex" id="mainImageIndex" value="0" />
                @Html.AntiForgeryToken()

                <!-- آپلود تصویر -->
                <div class="image-upload-container" id="uploadContainer">
                    <input type="file" id="imageInput" name="images" hidden multiple accept="image/*">
                    <div class="image-upload-icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <div class="mb-2">برای آپلود تصاویر کلیک کنید</div>
                    <div class="text-muted">یا تصاویر را اینجا رها کنید</div>
                </div>

                <!-- پیش‌نمایش تصاویر -->
                <div class="image-preview" id="imagePreview">
                    @foreach (var image in Model.ExistingImages)
                    {
                        <div class="image-preview-item" data-image-id="@image.Id">
                            <img src="/@image.ImageUrl" alt="تصویر آگهی">
                            <div class="remove-image">
                                <i class="fas fa-times"></i>
                            </div>
                            @if (image.IsMain)
                            {
                                <div class="main-image">تصویر اصلی</div>
                            }
                            <div class="set-main-image" title="انتخاب به عنوان تصویر اصلی">
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    }
                </div>

                <!-- دکمه‌های کنترلی -->
                <div class="d-flex justify-content-between mt-4">
                    <button type="button" class="btn btn-outline-secondary px-4" onclick="window.location.href='/Ads/Details?categoryId=@Model.CategoryId'">
                        <i class="fas fa-arrow-right ml-1"></i>
                        مرحله قبل
                    </button>
                    <button type="submit" class="btn btn-danger px-4" id="nextButton" disabled>
                        مرحله بعد
                        <i class="fas fa-arrow-left mr-1"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const imageInput = document.getElementById('imageInput');
        const imagePreview = document.getElementById('imagePreview');
        const uploadContainer = document.getElementById('uploadContainer');
        const nextButton = document.getElementById('nextButton');
        const mainImageIndex = document.getElementById('mainImageIndex');
        const form = document.getElementById('imagesForm');
        let uploadedImages = [];

        // بررسی وضعیت اولیه
        updateNextButton();

        // کلیک روی container برای انتخاب فایل
        uploadContainer.addEventListener('click', function() {
            if (getTotalImagesCount() < 10) {
                imageInput.click();
            }
        });

        // Drag and Drop
        uploadContainer.addEventListener('dragover', function(e) {
            e.preventDefault();
            if (getTotalImagesCount() < 10) {
                this.style.borderColor = '#dc3545';
                this.style.backgroundColor = '#f8f9fa';
            }
        });

        uploadContainer.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.style.borderColor = '#dee2e6';
            this.style.backgroundColor = 'transparent';
        });

        uploadContainer.addEventListener('drop', function(e) {
            e.preventDefault();
            this.style.borderColor = '#dee2e6';
            this.style.backgroundColor = 'transparent';

            if (getTotalImagesCount() < 10) {
                const files = e.dataTransfer.files;
                handleFiles(files);
            }
        });

        // File Input Change
        imageInput.addEventListener('change', function() {
            handleFiles(this.files);
        });

        function getTotalImagesCount() {
            return imagePreview.children.length;
        }

        function updateNextButton() {
            const hasImages = getTotalImagesCount() > 0;
            nextButton.disabled = !hasImages;

            // بروزرسانی وضعیت upload container
            if (getTotalImagesCount() >= 10) {
                uploadContainer.style.display = 'none';
            } else {
                uploadContainer.style.display = 'block';
            }
        }

        function handleFiles(files) {
            const currentCount = getTotalImagesCount();
            const maxAllowed = 10 - currentCount;

            if (files.length > maxAllowed) {
                alert(`حداکثر ${maxAllowed} تصویر دیگر می‌توانید اضافه کنید`);
                return;
            }

            Array.from(files).forEach((file, index) => {
                if (!file.type.startsWith('image/')) {
                    alert(`فایل ${file.name} یک تصویر معتبر نیست`);
                    return;
                }

                if (file.size > 10 * 1024 * 1024) {
                    alert(`حجم فایل ${file.name} بیش از 10 مگابایت است`);
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    const imageIndex = getTotalImagesCount();
                    const isFirstImage = imageIndex === 0;

                    const imageItem = document.createElement('div');
                    imageItem.className = 'image-preview-item';
                    imageItem.dataset.imageIndex = imageIndex;
                    imageItem.innerHTML = `
                        <img src="${e.target.result}" alt="تصویر آگهی">
                        <div class="remove-image" title="حذف تصویر">
                            <i class="fas fa-times"></i>
                        </div>
                        <div class="set-main-image" title="انتخاب به عنوان تصویر اصلی">
                            <i class="fas fa-star"></i>
                        </div>
                        ${isFirstImage ? '<div class="main-image">تصویر اصلی</div>' : ''}
                    `;

                    imagePreview.appendChild(imageItem);
                    uploadedImages.push(file);

                    // حذف تصویر
                    imageItem.querySelector('.remove-image').addEventListener('click', function(e) {
                        e.stopPropagation();
                        removeImage(imageItem);
                    });

                    // انتخاب تصویر اصلی
                    imageItem.querySelector('.set-main-image').addEventListener('click', function(e) {
                        e.stopPropagation();
                        setMainImage(imageItem);
                    });

                    updateNextButton();
                };
                reader.readAsDataURL(file);
            });
        }

        function removeImage(imageItem) {
            const index = parseInt(imageItem.dataset.imageIndex);
            uploadedImages.splice(index, 1);
            imageItem.remove();

            // بروزرسانی index های باقی مانده
            Array.from(imagePreview.children).forEach((item, newIndex) => {
                item.dataset.imageIndex = newIndex;
            });

            // اگر تصویر اصلی حذف شد، اولین تصویر را اصلی کن
            if (imageItem.querySelector('.main-image') && imagePreview.children.length > 0) {
                setMainImage(imagePreview.children[0]);
            }

            updateNextButton();
        }

        function setMainImage(imageItem) {
            // حذف تصویر اصلی قبلی
            imagePreview.querySelectorAll('.main-image').forEach(el => el.remove());

            // اضافه کردن به تصویر جدید
            const mainImageDiv = document.createElement('div');
            mainImageDiv.className = 'main-image';
            mainImageDiv.textContent = 'تصویر اصلی';
            imageItem.appendChild(mainImageDiv);

            // بروزرسانی hidden field
            mainImageIndex.value = imageItem.dataset.imageIndex;
        }

        // اعتبارسنجی فرم
        form.addEventListener('submit', function(e) {
            if (getTotalImagesCount() === 0) {
                e.preventDefault();
                alert('لطفاً حداقل یک تصویر آپلود کنید');
                return;
            }

            // اطمینان از اینکه تصویر اصلی انتخاب شده
            if (!imagePreview.querySelector('.main-image')) {
                setMainImage(imagePreview.children[0]);
            }
        });

        // مدیریت تصاویر موجود (اگر در حال ویرایش باشیم)
        imagePreview.querySelectorAll('.image-preview-item').forEach((item, index) => {
            item.dataset.imageIndex = index;

            // حذف تصویر موجود
            const removeBtn = item.querySelector('.remove-image');
            if (removeBtn) {
                removeBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    removeImage(item);
                });
            }

            // انتخاب تصویر اصلی
            const setMainBtn = item.querySelector('.set-main-image');
            if (setMainBtn) {
                setMainBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    setMainImage(item);
                });
            }
        });

        updateNextButton();
    });
</script>

<style>
    .image-upload-container {
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        padding: 40px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }

    .image-upload-container:hover {
        border-color: #dc3545;
        background-color: #f8f9fa;
    }

    .image-upload-icon {
        font-size: 48px;
        color: #6c757d;
        margin-bottom: 15px;
    }

    .image-preview {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }

    .image-preview-item {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: transform 0.2s ease;
    }

    .image-preview-item:hover {
        transform: translateY(-2px);
    }

    .image-preview-item img {
        width: 100%;
        height: 150px;
        object-fit: cover;
    }

    .remove-image {
        position: absolute;
        top: 5px;
        right: 5px;
        background: rgba(220, 53, 69, 0.8);
        color: white;
        border-radius: 50%;
        width: 25px;
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 12px;
        transition: background-color 0.2s ease;
    }

    .remove-image:hover {
        background: rgba(220, 53, 69, 1);
    }

    .set-main-image {
        position: absolute;
        top: 5px;
        left: 5px;
        background: rgba(255, 193, 7, 0.8);
        color: white;
        border-radius: 50%;
        width: 25px;
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 12px;
        transition: background-color 0.2s ease;
    }

    .set-main-image:hover {
        background: rgba(255, 193, 7, 1);
    }

    .main-image {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(220, 53, 69, 0.9);
        color: white;
        text-align: center;
        padding: 5px;
        font-size: 12px;
        font-weight: bold;
    }

    .image-preview:empty::after {
        content: "هنوز تصویری آپلود نشده است";
        display: block;
        text-align: center;
        color: #6c757d;
        font-style: italic;
        padding: 40px;
        border: 1px dashed #dee2e6;
        border-radius: 8px;
        grid-column: 1 / -1;
    }
</style>