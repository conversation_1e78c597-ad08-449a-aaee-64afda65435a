﻿@model Keleid.Web.ViewModels.UserIndexViewModel

<!-- Main Content -->
<div class="container py-4">
    <div class="row">
        <!-- Sidebar -->
        @await Component.InvokeAsync("UserSidebar")

        <!-- Main Content Area -->
        <div class="col-lg-9">
            <div class="bg-white rounded-3 shadow-sm p-4">
                <h5 class="mb-4">اطلاعات کاربری</h5>
                <div class="user-info-list">
                    <!-- شماره تماس -->
                    <div class="info-item">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <div class="info-label text-muted mb-2">شماره تماس</div>
                                <div class="d-flex align-items-center">
                                    <span class="info-value">@Model.PhoneNumber</span>
                                    <span class="badge @Model.GetPhoneStatusBadgeClass() me-2">@Model.GetPhoneStatusText()</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تاریخ ثبت‌نام -->
                    <div class="info-item">
                        <div class="info-label text-muted mb-2">تاریخ ثبت‌نام</div>
                        <div class="info-value">@Model.GetPersianRegisterDate()</div>
                    </div>

                    <!-- وضعیت حساب -->
                    <div class="info-item">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <div class="info-label text-muted mb-2">وضعیت حساب کاربری</div>
                                <div class="d-flex align-items-center">
                                    <span class="info-value">@Model.GetAccountStatus()</span>
                                    <span class="badge @Model.GetAccountStatusBadgeClass() me-2">@Model.GetAccountStatus()</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
@await Html.PartialAsync("_Footer")
