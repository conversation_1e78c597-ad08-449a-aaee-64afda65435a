namespace Keleid.BLL.DTOs
{
    public class LocationDto
    {
        public int Id { get; set; }
        public string Province { get; set; }
        public string City { get; set; }
        public string Address { get; set; }

        public string DisplayName => !string.IsNullOrEmpty(City) && !string.IsNullOrEmpty(Province) 
            ? $"{Province}، {City}" 
            : Province ?? City ?? "نامشخص";
    }
}
