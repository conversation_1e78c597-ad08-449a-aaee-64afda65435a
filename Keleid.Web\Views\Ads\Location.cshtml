﻿@model Keleid.Web.ViewModels.AdsLocationViewModel
@{
    ViewData["Title"] = "موقعیت و اطلاعات تماس";
}

<!-- Header -->
@await Html.PartialAsync("_<PERSON><PERSON><PERSON><PERSON>er", "ثبت آگهی")

<div class="container my-5">
    <!-- Step Indicator -->
    <div class="step-indicator mb-4">
        <div class="step-line"></div>
        <div class="step">
            <div class="step-circle">1</div>
            <div class="step-title">دسته‌بندی</div>
        </div>
        <div class="step">
            <div class="step-circle">2</div>
            <div class="step-title">مشخصات آگهی</div>
        </div>
        <div class="step">
            <div class="step-circle">3</div>
            <div class="step-title">تصاویر</div>
        </div>
        <div class="step active">
            <div class="step-circle">4</div>
            <div class="step-title">موقعیت و تماس</div>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <h5 class="card-title mb-4">موقعیت مکانی و اطلاعات تماس - @Model.CategoryTitle</h5>

            @if (TempData["Error"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    @TempData["Error"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <form id="locationForm" method="post" asp-action="Location">
                <input type="hidden" asp-for="CategoryId" />
                @Html.AntiForgeryToken()
                <!-- موقعیت مکانی -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="mb-3"><i class="fas fa-map-marker-alt text-danger ml-2"></i>موقعیت مکانی</h6>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label asp-for="Province" class="form-label required">استان</label>
                        <select asp-for="Province" class="form-select" id="provinceSelect" required>
                            <option value="">انتخاب استان</option>
                            @foreach (var province in Model.Provinces)
                            {
                                <option value="@province.Title" selected="@(Model.Province == province.Title)">@province.Title</option>
                            }
                        </select>
                        <span asp-validation-for="Province" class="text-danger"></span>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label asp-for="City" class="form-label required">شهر</label>
                        <select asp-for="City" class="form-select" id="citySelect" required>
                            <option value="">ابتدا استان را انتخاب کنید</option>
                            @foreach (var city in Model.Cities)
                            {
                                <option value="@city.Title" selected="@(Model.City == city.Title)">@city.Title</option>
                            }
                        </select>
                        <span asp-validation-for="City" class="text-danger"></span>
                    </div>

                    <div class="col-12 mb-3">
                        <label asp-for="Address" class="form-label required">آدرس</label>
                        <textarea asp-for="Address" class="form-control" rows="3" required minlength="10" maxlength="500"
                                  placeholder="آدرس دقیق محل آگهی را وارد کنید..."></textarea>
                        <div class="form-text">آدرس باید بین 10 تا 500 کاراکتر باشد</div>
                        <span asp-validation-for="Address" class="text-danger"></span>
                    </div>
                </div>

                <!-- اطلاعات تماس -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="mb-3"><i class="fas fa-phone text-danger ml-2"></i>اطلاعات تماس</h6>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label asp-for="ContactName" class="form-label required">نام</label>
                        <input asp-for="ContactName" type="text" class="form-control" required minlength="2" maxlength="100"
                               placeholder="نام خود را وارد کنید">
                        <span asp-validation-for="ContactName" class="text-danger"></span>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label asp-for="ContactPhone" class="form-label required">شماره تماس</label>
                        <input asp-for="ContactPhone" type="tel" class="form-control" required pattern="^09\d{9}$"
                               placeholder="09123456789" maxlength="11">
                        <div class="form-text">شماره تماس باید با 09 شروع شده و 11 رقم باشد</div>
                        <span asp-validation-for="ContactPhone" class="text-danger"></span>
                    </div>

                    <div class="col-12 mb-3">
                        <label asp-for="ContactEmail" class="form-label">ایمیل (اختیاری)</label>
                        <input asp-for="ContactEmail" type="email" class="form-control"
                               placeholder="<EMAIL>">
                        <span asp-validation-for="ContactEmail" class="text-danger"></span>
                    </div>
                </div>

                <!-- دکمه‌های کنترلی -->
                <div class="d-flex justify-content-between mt-4">
                    <button type="button" class="btn btn-outline-secondary px-4" onclick="window.location.href='/Ads/Images?categoryId=@Model.CategoryId'">
                        <i class="fas fa-arrow-right ml-1"></i>
                        مرحله قبل
                    </button>
                    <button type="submit" class="btn btn-danger px-4" id="submitButton">
                        <i class="fas fa-check ml-1"></i>
                        ثبت آگهی
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const provinceSelect = document.getElementById('provinceSelect');
        const citySelect = document.getElementById('citySelect');
        const form = document.getElementById('locationForm');
        const submitButton = document.getElementById('submitButton');

        // مدیریت تغییر استان
        provinceSelect.addEventListener('change', async function() {
            const selectedProvince = this.value;
            citySelect.innerHTML = '<option value="">در حال بارگذاری...</option>';
            citySelect.disabled = true;

            if (selectedProvince) {
                try {
                    const response = await fetch(`/Ads/GetCitiesByProvince?provinceName=${encodeURIComponent(selectedProvince)}`);
                    const data = await response.json();

                    citySelect.innerHTML = '<option value="">انتخاب شهر</option>';

                    if (data.success && data.cities) {
                        data.cities.forEach(city => {
                            const option = document.createElement('option');
                            option.value = city.title;
                            option.textContent = city.title;
                            if (city.title === '@Model.City') {
                                option.selected = true;
                            }
                            citySelect.appendChild(option);
                        });
                        citySelect.disabled = false;
                    } else {
                        citySelect.innerHTML = '<option value="">خطا در بارگذاری شهرها</option>';
                    }
                } catch (error) {
                    console.error('Error loading cities:', error);
                    citySelect.innerHTML = '<option value="">خطا در بارگذاری شهرها</option>';
                }
            } else {
                citySelect.innerHTML = '<option value="">ابتدا استان را انتخاب کنید</option>';
            }
        });

        // بارگذاری اولیه شهرها
        if (provinceSelect.value) {
            provinceSelect.dispatchEvent(new Event('change'));
        }

        // اعتبارسنجی فرم
        form.addEventListener('submit', function(e) {
            let isValid = true;

            // بررسی استان
            if (!provinceSelect.value) {
                provinceSelect.classList.add('is-invalid');
                isValid = false;
            } else {
                provinceSelect.classList.remove('is-invalid');
            }

            // بررسی شهر
            if (!citySelect.value) {
                citySelect.classList.add('is-invalid');
                isValid = false;
            } else {
                citySelect.classList.remove('is-invalid');
            }

            // بررسی آدرس
            const addressField = document.querySelector('textarea[name="Address"]');
            if (!addressField.value || addressField.value.length < 10) {
                addressField.classList.add('is-invalid');
                isValid = false;
            } else {
                addressField.classList.remove('is-invalid');
            }

            // بررسی نام
            const nameField = document.querySelector('input[name="ContactName"]');
            if (!nameField.value || nameField.value.length < 2) {
                nameField.classList.add('is-invalid');
                isValid = false;
            } else {
                nameField.classList.remove('is-invalid');
            }

            // بررسی شماره تماس
            const phoneField = document.querySelector('input[name="ContactPhone"]');
            const phonePattern = /^09\d{9}$/;
            if (!phoneField.value || !phonePattern.test(phoneField.value)) {
                phoneField.classList.add('is-invalid');
                isValid = false;
            } else {
                phoneField.classList.remove('is-invalid');
            }

            if (!isValid) {
                e.preventDefault();

                // اسکرول به اولین فیلد نامعتبر
                const firstInvalid = form.querySelector('.is-invalid');
                if (firstInvalid) {
                    firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    firstInvalid.focus();
                }
            } else {
                // نمایش loading
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i> در حال ثبت...';
            }
        });

        // اعتبارسنجی زنده
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.checkValidity()) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } else {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            });
        });
    });
</script>

<style>
    .subcategory-list {
        display: none;
    }

    .category-item {
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        margin-bottom: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

        .category-item:hover {
            background-color: #f8f9fa;
            border-color: #dc3545;
        }

        .category-item.selected {
            background-color: #dc3545;
            color: white;
            border-color: #dc3545;
        }

        .category-item i {
            margin-left: 10px;
        }

    .back-button {
        display: none;
    }

        .back-button.show {
            display: inline-block;
        }
</style>