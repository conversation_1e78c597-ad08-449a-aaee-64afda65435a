﻿@model Keleid.BLL.DTOs.AdvertisementDto
@{
    ViewBag.Title = "جزئیات آگهی";
}

<style>
    .thumbnail-img {
        width: 80px !important;
        height: 80px !important;
        object-fit: cover;
        border-radius: 6px;
        border: 3px solid transparent;
        transition: all 0.3s ease;
        cursor: pointer;
        display: block;
    }

    .preview-thumbnail .nav-link.active .thumbnail-img {
        border-color: #007bff;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    }

    .preview-thumbnail .nav-link:hover .thumbnail-img {
        border-color: #6c757d;
        transform: scale(1.05);
    }

    .preview-thumbnail {
        margin-top: 15px;
    }

    .thumbnail-item {
        flex-shrink: 0;
    }

    .preview-pic img {
        max-height: 400px;
        width: 100%;
        object-fit: cover;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .preview-pic {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 10px;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tabs for image gallery
    const thumbnailLinks = document.querySelectorAll('.preview-thumbnail .nav-link');

    thumbnailLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Remove active class from all thumbnails
            thumbnailLinks.forEach(function(l) {
                l.classList.remove('active');
            });

            // Add active class to clicked thumbnail
            this.classList.add('active');

            // Hide all tab panes
            const tabPanes = document.querySelectorAll('.preview-pic .tab-pane');
            tabPanes.forEach(function(pane) {
                pane.classList.remove('active', 'show');
            });

            // Show target tab pane
            const targetId = this.getAttribute('href');
            const targetPane = document.querySelector(targetId);
            if (targetPane) {
                targetPane.classList.add('active', 'show');
            }
        });
    });
});
</script>

<!-- Left Sidebar -->
@await Html.PartialAsync("_LeftSidebar")

<!-- Main Content -->
<section class="content home">

    <!-- Breadcrumb -->
    @await Html.PartialAsync("_Breadcrumb")

    <div class="container-fluid">
        <div class="row clearfix">
            <div class="col-lg-12">
                <div class="card">
                    <div class="body">
                        <div class="row">
                            <div class="preview col-lg-4 col-md-12">
                                @if (Model.Images.Any())
                                {
                                    <div class="preview-pic tab-content">
                                        @for (int i = 0; i < Model.Images.Count; i++)
                                        {
                                            <div class="tab-pane @(i == 0 ? "active show" : "")" id="product_@(i + 1)">
                                                <img src="@Model.Images[i].ImageUrl" class="img-fluid" alt="@Model.Title" />
                                            </div>
                                        }
                                    </div>
                                    @if (Model.Images.Count > 1)
                                    {
                                        <div class="preview-thumbnail d-flex flex-wrap mt-2">
                                            @for (int i = 0; i < Model.Images.Count; i++)
                                            {
                                                <div class="thumbnail-item me-2 mb-2">
                                                    <a class="nav-link p-0 @(i == 0 ? "active" : "")" data-bs-toggle="tab" href="#product_@(i + 1)" role="tab">
                                                        <img src="@Model.Images[i].ImageUrl" alt="@Model.Title" class="thumbnail-img" />
                                                    </a>
                                                </div>
                                            }
                                        </div>
                                    }
                                }
                                else
                                {
                                    <div class="preview-pic">
                                        <img src="/assets/img/placeholder.png" class="img-fluid" alt="بدون تصویر" />
                                    </div>
                                }
                            </div>
                            <div class="details col-lg-8 col-md-12">
                                <h3 class="product-title m-b-0">@Model.Title</h3>
                                <h4 class="price m-t-0">قیمت: <span class="text-muted">@Model.FormattedPrice</span></h4>
                                <p class="price m-t-0"><span class="text-muted">@Model.RelativeTime</span></p>
                                <hr>
                                <p class="product-description text-justify">@Model.Description</p>
                                <p class="vote">شماره آگهی: <strong>#@Model.Id</strong></p>
                                <p class="vote">
                                    وضعیت آگهی:
                                    @if (Model.IsDeleted)
                                    {
                                        <strong><span class="badge badge-danger">حذف شده</span></strong>
                                    }
                                    else if (Model.IsApproved)
                                    {
                                        <strong><span class="badge badge-success">تایید شده</span></strong>
                                    }
                                    else
                                    {
                                        <strong><span class="badge badge-warning">در انتظار تایید</span></strong>
                                    }
                                </p>
                                <p class="colors">
                                    دسته بندی:
                                    <strong>@Model.CategoryTitle</strong>
                                </p>
                                @if (!string.IsNullOrEmpty(Model.ApprovedByUserName))
                                {
                                    <p class="vote">
                                        تایید/رد شده توسط: <strong>@Model.ApprovedByUserName</strong>
                                    </p>
                                }
                                <hr>
                                <div class="action">
                                    @if (Model.IsDeleted)
                                    {
                                        <!-- آگهی حذف شده فقط دکمه بازگشت دارد -->
                                        <a asp-area="Admin" asp-controller="Ads" asp-action="Index" class="btn btn-secondary btn-round waves-effect">
                                            بازگشت به لیست
                                        </a>
                                    }
                                    else if (Model.IsApproved)
                                    {
                                        <!-- آگهی تایید شده دارای دکمه عدم تایید است -->
                                        <form asp-area="Admin" asp-controller="Ads" asp-action="Reject" asp-route-id="@Model.Id"
                                              method="post" style="display: inline;">
                                            <button type="submit" class="btn btn-danger btn-round waves-effect"
                                                    onclick="return confirm('آیا از عدم تایید این آگهی اطمینان دارید؟')">
                                                عدم تایید
                                            </button>
                                        </form>
                                        <a asp-area="Admin" asp-controller="Ads" asp-action="Index" class="btn btn-secondary btn-round waves-effect">
                                            بازگشت به لیست
                                        </a>
                                    }
                                    else
                                    {
                                        <!-- آگهی در انتظار تایید دارای دکمه تایید است -->
                                        <form asp-area="Admin" asp-controller="Ads" asp-action="Approve" asp-route-id="@Model.Id"
                                              method="post" style="display: inline;">
                                            <button type="submit" class="btn btn-success btn-round waves-effect"
                                                    onclick="return confirm('آیا از تایید این آگهی اطمینان دارید؟')">
                                                تایید آگهی
                                            </button>
                                        </form>
                                        <a asp-area="Admin" asp-controller="Ads" asp-action="Waiting" class="btn btn-secondary btn-round waves-effect">
                                            بازگشت به لیست انتظار
                                        </a>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-12">
                <div class="card">
                    <ul class="nav nav-tabs">
                        <li class="nav-item"><a class="nav-link active" data-toggle="tab" href="#about">کاربر</a></li>
                        <li class="nav-item"><a class="nav-link " data-toggle="tab" href="#description">موقعیت</a></li>
                        <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#review">اطلاعات تماس</a></li>
                        @if (Model.Features.Any())
                        {
                            <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#features">ویژگی‌ها</a></li>
                        }
                    </ul>
                </div>
                <div class="card">
                    <div class="body">
                        <div class="tab-content">
                            <div class="tab-pane active" id="about">
                                <div class="d-flex flex-column" style="gap:1rem">
                                    <div>شماره تماس: <strong class="fw-bold">@Model.UserPhone</strong></div>
                                    <div>ایمیل: <strong class="fw-bold">@Model.UserEmail</strong></div>
                                    <div>تاریخ ثبت‌نام: <strong class="fw-bold">@Model.UserRegistrationDate.ToString("yyyy/MM/dd")</strong></div>
                                    <div>شناسه کاربر: <strong class="fw-bold">@Model.UserId</strong></div>
                                </div>
                            </div>
                            <div class="tab-pane " id="description">
                                <div class="d-flex flex-column" style="gap:1rem">
                                    <div>استان: <strong class="fw-bold">@Model.Province</strong></div>
                                    <div>شهرستان: <strong class="fw-bold">@Model.City</strong></div>
                                    <div>آدرس: <strong class="fw-bold">@Model.Address</strong></div>
                                </div>
                            </div>
                            <div class="tab-pane" id="review">
                                <div class="d-flex flex-column" style="gap:1rem">
                                    <div>شماره تماس: <strong class="fw-bold">@Model.ContactPhone</strong></div>
                                    <div>ایمیل: <strong class="fw-bold">@Model.ContactEmail</strong></div>
                                </div>
                            </div>
                            @if (Model.Features.Any())
                            {
                                <div class="tab-pane" id="features">
                                    <div class="d-flex flex-column" style="gap:1rem">
                                        @foreach (var feature in Model.Features)
                                        {
                                            <div>@feature.FeatureTitle: <strong class="fw-bold">@feature.Value</strong></div>
                                        }
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</section>