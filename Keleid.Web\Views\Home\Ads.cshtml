﻿@model Keleid.BLL.DTOs.AdvertisementDto
@{
    ViewData["Title"] = Model.Title;
    var isFavorited = ViewBag.IsFavorited as bool? ?? false;
    var advertisementId = ViewBag.AdvertisementId as int? ?? 0;
}

@Html.AntiForgeryToken()

<!-- Header -->
@await Html.PartialAsync("_SecHeader", Model.Title)

<!-- Main Content -->
<div class="container mt-4">
    <div class="row">
        <!-- Right Column - Details -->
        <div class="col-lg-6 order-2 order-lg-1">
            <div class="p-3">
                <!-- Breadcrumb -->
                <div class="breadcrumb-wrapper">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="/">خانه</a></li>
                            <li class="breadcrumb-item">
                                @if (!string.IsNullOrEmpty(Model.CategorySlug))
                                {
                                    <a href="/@Model.CategorySlug">@Model.CategoryTitle</a>
                                }
                                else
                                {
                                    <span>@Model.CategoryTitle</span>
                                }
                            </li>
                            <li class="breadcrumb-item active">@Model.Title</li>
                        </ol>
                    </nav>
                </div>

                <!-- Title and Date -->
                <h1 class="h4 mt-3 mb-2 hidden-overlow-text">@Model.Title</h1>
                <p class="text-muted small mb-3">@Model.RelativeTime در @Model.LocationDisplay</p>

                <!-- Contact Button and Action Icons -->
                <div class="d-flex align-items-center justify-content-between gap-2 mb-3">
                    <button class="btn btn-danger" style="width: 160px;" onclick="showContactInfo()">اطلاعات تماس</button>
                    <div class="d-flex gap-3">
                        <a href="javascript:void(0)" class="action-icon" onclick="shareAd()" title="اشتراک‌گذاری">
                            <i class="fas fa-share-alt"></i>
                        </a>
                        <a href="javascript:void(0)" class="action-icon @(isFavorited ? "active" : "")" onclick="toggleBookmark(this)" title="@(isFavorited ? "حذف از نشان شده‌ها" : "نشان کردن")" data-ad-id="@advertisementId">
                            <i class="@(isFavorited ? "fas" : "far") fa-bookmark"></i>
                        </a>
                    </div>
                </div>

                @if (Model.Features.Any())
                {
                    <!-- Property Features -->
                    @foreach (var feature in Model.Features.Take(3))
                    {
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-muted">@feature.FeatureTitle</span>
                            <span>@feature.Value</span>
                        </div>
                        <div class="feature-divider"></div>
                    }

                    <hr>
                }

                <!-- Price -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span class="text-muted">قیمت</span>
                    <span class="h5 mb-0">@Model.FormattedPrice</span>
                </div>

                <hr>

                <!-- Description -->
                <div>
                    <h6 class="mb-2">توضیحات</h6>
                    <p class="text-justify mb-0 hidden-overlow-text">
                        @Model.Description
                    </p>
                </div>

                @if (Model.Features.Count > 3)
                {
                    <hr>
                    <div>
                        <h6 class="mb-2">سایر ویژگی‌ها</h6>
                        @foreach (var feature in Model.Features.Skip(3))
                        {
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">@feature.FeatureTitle</span>
                                <span>@feature.Value</span>
                            </div>
                            <div class="feature-divider"></div>
                        }
                    </div>
                }
            </div>
        </div>

        <!-- Left Column - Gallery -->
        <div class="col-lg-6 order-1 order-lg-2">
            <div class="gallery">
                @if (Model.Images.Any())
                {
                    
                    var mainImage = Model.Images.FirstOrDefault(img => img.IsMain) ?? Model.Images.First();
                    var sortedImages = Model.Images.OrderByDescending(img => img.IsMain).ToList();
                    
                    <img src="@mainImage.ImageUrl" alt="@Model.Title" class="gallery-image" id="mainImage">
                    <div class="d-flex mt-2">
                        @for (int i = 0; i < Math.Min(sortedImages.Count, 3); i++)
                        {
                            var image = sortedImages[i];
                            <img src="@image.ImageUrl" alt="@Model.Title"
                                 class="thumbnail-image @(image.IsMain ? "active" : "")"
                                 onclick="changeImage(this)">
                        }
                        @if (sortedImages.Count > 3)
                        {
                            <a href="#" class="more-images" data-fancybox-trigger="gallery">
                                +@(sortedImages.Count - 3)
                            </a>
                        }
                    </div>
                }
                else
                {
                    <img src="@Model.MainImageUrl" alt="@Model.Title" class="gallery-image" id="mainImage">
                }
            </div>
        </div>
    </div>
</div>

<!-- Hidden gallery for Fancybox -->
@if (Model.Images.Any())
{
    <div style="display: none;">
        @foreach (var image in Model.Images)
        {
            <a href="@image.ImageUrl" data-fancybox="gallery">
                <img src="@image.ImageUrl" alt="@Model.Title">
            </a>
        }
    </div>
}

<!-- Footer -->
@await Html.PartialAsync("_Footer")

<!-- Bottom Sheet Modal -->
<div class="bottom-sheet-backdrop" onclick="hideContactInfo()"></div>
<div class="bottom-sheet">
    <h6 class="mb-3">اطلاعات تماس</h6>
    <hr class="mb-3">
    @if(User.Identity.IsAuthenticated)
    {
        <a href="tel:@Model.ContactPhone" class="contact-option mb-2">
            <i class="fas fa-phone"></i>
            <span class="contact-label">تماس تلفنی با</span>
            <span class="contact-value">@Model.ContactPhone</span>
        </a>
        <a href="sms:@Model.ContactPhone" class="contact-option">
            <i class="fas fa-comment"></i>
            <span class="contact-label">ارسال پیامک به</span>
            <span class="contact-value">@Model.ContactPhone</span>
        </a>
    }else{
        <p class="contact-option mb-2">
            <span class="contact-label">جهت مشاهده اطلاعات تماس فروشنده ابتدا وارد حساب کاربری خود شوید</span>
            <a class="btn btn-danger" asp-controller="Account" asp-action="Index">ورود</a>
        </p>
    }
</div>

@section structuredData {
    <script type="application/ld+json">
    {
        "@@context": "https://schema.org",
        "@@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@@type": "ListItem",
                "position": 1,
                "name": "خانه",
                "item": "https://keleid.ir/"
            },
            {
                "@@type": "ListItem",
                "position": 2,
                "name": "@Model.CategoryTitle",
                "item": "https://keleid.ir/@Model.CategorySlug"
            },
            {
                "@@type": "ListItem",
                "position": 3,
                "name": "@Model.Title",
                "item": "https://keleid.ir/@Model.Id/@Model.Slug"
            }
        ]
    }
    </script>

    <script type="application/ld+json">
    {
        "@@context": "https://schema.org",
        "@@type": "Product",
        "name": "@Model.Title",
        "description": "@Html.Raw(Html.Encode(Model.Description).Replace("\n", " ").Replace("\r", ""))",
        "image": [
            @if (Model.Images.Any())
            {
                @string.Join(",", Model.Images.Select(img => $"\"{img.ImageUrl}\""))
            }
            else
            {
                @:["@Model.MainImageUrl"]
            }
        ],
        "url": "https://keleid.ir/@Model.Id/@Model.Slug",
        "category": "@Model.CategoryTitle",
        "brand": {
            "@@type": "Brand",
            "name": "کلید"
        },
        "offers": {
            "@@type": "Offer",
            "price": "@Model.Price",
            "priceCurrency": "IRR",
            "availability": "https://schema.org/InStock",
            "seller": {
                "@@type": "Organization",
                "name": "کلید"
            },
            "url": "https://keleid.ir/@Model.Id/@Model.Slug"
        },
        "aggregateRating": {
            "@@type": "AggregateRating",
            "ratingValue": "4.5",
            "reviewCount": "1"
        }
    }
    </script>
}

@section css {
    <!-- Fancybox CSS -->
    <link rel="stylesheet" href="~/assets/libs/fancybox/fancybox.css">
}

@section js {

    <!-- Fancybox JS -->
    <script src="~/assets/libs/fancybox/fancybox.umd.js"></script>

    <script>
        function changeImage(element) {
            document.getElementById('mainImage').src = element.src;
            document.querySelectorAll('.thumbnail-image').forEach(thumb => {
                thumb.classList.remove('active');
            });
            element.classList.add('active');
        }

        // Initialize Fancybox
        Fancybox.bind('[data-fancybox="gallery"]', {
            startIndex: 0,
            Carousel: {
                preload: 0
            }
        });

        function showContactInfo() {
            document.querySelector('.bottom-sheet-backdrop').classList.add('show');
            document.querySelector('.bottom-sheet').classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function hideContactInfo() {
            document.querySelector('.bottom-sheet-backdrop').classList.remove('show');
            document.querySelector('.bottom-sheet').classList.remove('show');
            document.body.style.overflow = '';
        }

        function toggleBookmark(element) {
            const adId = element.getAttribute('data-ad-id');
            const icon = element.querySelector('i');

            // Show loading state
            const originalIcon = icon.className;
            icon.className = 'fas fa-spinner fa-spin';
            element.style.pointerEvents = 'none';

            // Create form data
            const formData = new FormData();
            formData.append('id', adId);
            formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

            // Send toggle request
            fetch('@Url.Action("ToggleFavorite", "Home")', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update icon based on new state
                    if (data.isFavorited) {
                        icon.className = 'fas fa-bookmark';
                        element.classList.add('active');
                        element.title = 'حذف از نشان شده‌ها';
                    } else {
                        icon.className = 'far fa-bookmark';
                        element.classList.remove('active');
                        element.title = 'نشان کردن';
                    }

                    // Show success message
                    showToast('success', data.message);
                } else {
                    // Reset icon
                    icon.className = originalIcon;

                    // Show error message or redirect to login
                    if (data.requireLogin) {
                        showToast('warning', data.message);
                        setTimeout(() => {
                            window.location.href = '@Url.Action("Index", "Account")';
                        }, 2000);
                    } else {
                        showToast('error', data.message);
                    }
                }

                // Reset pointer events
                element.style.pointerEvents = '';
            })
            .catch(error => {
                console.error('Error:', error);

                // Reset icon and pointer events
                icon.className = originalIcon;
                element.style.pointerEvents = '';

                showToast('error', 'خطا در ارتباط با سرور');
            });
        }

        function shareAd() {
            if (navigator.share) {
                navigator.share({
                    title: '@Model.Title',
                    text: 'مشاهده آگهی در کلید',
                    url: window.location.href
                })
                .catch(console.error);
            } else {
                // کپی کردن لینک در کلیپبورد
                const dummy = document.createElement('input');
                document.body.appendChild(dummy);
                dummy.value = window.location.href;
                dummy.select();
                document.execCommand('copy');
                document.body.removeChild(dummy);

                // نمایش پیام موفقیت
                alert('لینک آگهی در کلیپبورد کپی شد');
            }
        }

        // Toast notification function
        function showToast(type, message) {
            // Create toast element
            const toastHtml = `
                <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'danger'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'exclamation-circle'} me-2"></i>
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;

            // Add toast to container
            let toastContainer = document.querySelector('.toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '1055';
                document.body.appendChild(toastContainer);
            }

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);

            // Show toast
            const toastElement = toastContainer.lastElementChild;
            const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
            toast.show();

            // Remove toast element after it's hidden
            toastElement.addEventListener('hidden.bs.toast', function() {
                this.remove();
            });
        }
    </script>
}