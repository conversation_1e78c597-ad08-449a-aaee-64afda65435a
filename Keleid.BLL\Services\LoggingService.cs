using Keleid.DAL;
using Keleid.DAL.Models;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace Keleid.BLL.Services
{
    public class LoggingService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<LoggingService> _logger;

        public LoggingService(ApplicationDbContext context, ILogger<LoggingService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task LogGeneralAsync(string level, string message, string? source = null, string? userId = null, object? properties = null, string? ipAddress = null, string? userAgent = null, string? requestPath = null)
        {
            try
            {
                var generalLog = new GeneralLog
                {
                    Level = level,
                    Message = message,
                    Timestamp = DateTime.UtcNow,
                    Source = source ?? "Unknown",
                    UserId = userId,
                    Properties = properties != null ? JsonSerializer.Serialize(properties) : null,
                    IpAddress = ipAddress,
                    UserAgent = userAgent,
                    RequestPath = requestPath
                };

                _context.GeneralLogs.Add(generalLog);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                // اگر خطا در ثبت لاگ باشد، فقط در console لاگ می‌کنیم
                _logger.LogError(ex, "Error saving general log: {Message}", message);
            }
        }

        public async Task LogErrorAsync(string message, Exception? exception = null, string? source = null, string? userId = null, object? properties = null, string? ipAddress = null, string? userAgent = null, string? requestPath = null, string? requestMethod = null, int? statusCode = null)
        {
            try
            {
                var errorLog = new ErrorLog
                {
                    Message = message,
                    Exception = exception?.GetType().FullName,
                    StackTrace = exception?.StackTrace,
                    InnerException = exception?.InnerException?.Message,
                    Timestamp = DateTime.UtcNow,
                    Source = source ?? "Unknown",
                    UserId = userId,
                    Properties = properties != null ? JsonSerializer.Serialize(properties) : null,
                    IpAddress = ipAddress,
                    UserAgent = userAgent,
                    RequestPath = requestPath,
                    RequestMethod = requestMethod,
                    StatusCode = statusCode,
                    Severity = GetSeverityFromException(exception)
                };

                _context.ErrorLogs.Add(errorLog);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                // اگر خطا در ثبت لاگ باشد، فقط در console لاگ می‌کنیم
                _logger.LogError(ex, "Error saving error log: {Message}", message);
            }
        }

        public async Task LogInformationAsync(string message, string? source = null, string? userId = null, object? properties = null)
        {
            await LogGeneralAsync("Information", message, source, userId, properties);
        }

        public async Task LogWarningAsync(string message, string? source = null, string? userId = null, object? properties = null)
        {
            await LogGeneralAsync("Warning", message, source, userId, properties);
        }

        public async Task LogDebugAsync(string message, string? source = null, string? userId = null, object? properties = null)
        {
            await LogGeneralAsync("Debug", message, source, userId, properties);
        }

        private string GetSeverityFromException(Exception? exception)
        {
            if (exception == null) return "Error";

            return exception switch
            {
                OutOfMemoryException => "Fatal",
                StackOverflowException => "Fatal",
                AccessViolationException => "Fatal",
                UnauthorizedAccessException => "Warning",
                ArgumentException => "Warning",
                FileNotFoundException => "Warning",
                DirectoryNotFoundException => "Warning",
                _ => "Error"
            };
        }
    }
}
