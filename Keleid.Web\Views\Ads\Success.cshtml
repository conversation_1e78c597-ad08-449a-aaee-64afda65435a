@{
    ViewData["Title"] = "آگهی با موفقیت ثبت شد";
}

<!-- Header -->
@await Html.PartialAsync("_SecHeader", "ثبت آگهی")

<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card text-center">
                <div class="card-body py-5">
                    <!-- آیکون موفقیت -->
                    <div class="success-icon mb-4">
                        <i class="fas fa-check-circle"></i>
                    </div>

                    <!-- پیام موفقیت -->
                    <h3 class="text-success mb-3">آگهی شما با موفقیت ثبت شد!</h3>
                    
                    <p class="text-muted mb-4">
                        آگهی شما با شناسه <strong>#@ViewBag.AdvertisementId</strong> در سیستم ثبت شد.
                        <br>
                        پس از بررسی و تایید توسط مدیران سایت، آگهی شما منتشر خواهد شد.
                    </p>

                    <!-- اطلاعات اضافی -->
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle ml-2"></i>
                        <strong>توجه:</strong> معمولاً فرآیند بررسی و تایید آگهی‌ها حداکثر 24 ساعت زمان می‌برد.
                    </div>

                    <!-- دکمه‌های عملیات -->
                    <div class="d-grid gap-2 d-md-block">
                        <a href="/" class="btn btn-danger px-4">
                            <i class="fas fa-home ml-1"></i>
                            بازگشت به صفحه اصلی
                        </a>
                        <a href="/Ads/Index" class="btn btn-outline-secondary px-4">
                            <i class="fas fa-plus ml-1"></i>
                            ثبت آگهی جدید
                        </a>
                    </div>

                    <!-- لینک‌های مفید -->
                    <div class="mt-4 pt-3 border-top">
                        <p class="text-muted mb-2">
                            <small>می‌توانید وضعیت آگهی خود را در پنل کاربری پیگیری کنید</small>
                        </p>
                        <a href="/User/MyAds" class="btn btn-link btn-sm">
                            <i class="fas fa-list ml-1"></i>
                            مشاهده آگهی‌های من
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .success-icon {
        font-size: 4rem;
        color: #198754;
    }

    .card {
        border: none;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .btn-link {
        text-decoration: none;
    }

    .btn-link:hover {
        text-decoration: underline;
    }

    @@media (max-width: 768px) {
        .success-icon {
            font-size: 3rem;
        }
        
        .card-body {
            padding: 2rem 1rem !important;
        }
    }
</style>

<script>
    // نمایش پیام موفقیت از TempData
    @if (TempData["Success"] != null)
    {
        <text>
        document.addEventListener('DOMContentLoaded', function() {
            // می‌توانید اینجا انیمیشن یا افکت خاصی اضافه کنید
            console.log('Advertisement created successfully');
        });
        </text>
    }
</script>
