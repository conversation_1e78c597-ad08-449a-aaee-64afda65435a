using Keleid.BLL.Services.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Keleid.Web.Services
{
    /// <summary>
    /// سرویس پس‌زمینه برای به‌روزرسانی sitemap
    /// </summary>
    public class SitemapUpdateService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<SitemapUpdateService> _logger;
        private readonly TimeSpan _checkInterval = TimeSpan.FromMinutes(30); // بررسی هر 30 دقیقه

        public SitemapUpdateService(IServiceProvider serviceProvider, ILogger<SitemapUpdateService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Sitemap update service started");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    using (var scope = _serviceProvider.CreateScope())
                    {
                        var sitemapService = scope.ServiceProvider.GetRequiredService<ISitemapService>();
                        
                        var needsUpdate = await sitemapService.NeedsUpdateAsync();
                        if (needsUpdate)
                        {
                            _logger.LogInformation("Sitemap needs update, refreshing...");
                            var result = await sitemapService.RefreshSitemapAsync();
                            
                            if (result)
                            {
                                _logger.LogInformation("Sitemap updated successfully");
                            }
                            else
                            {
                                _logger.LogWarning("Failed to update sitemap");
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in sitemap update service");
                }

                await Task.Delay(_checkInterval, stoppingToken);
            }

            _logger.LogInformation("Sitemap update service stopped");
        }
    }

    /// <summary>
    /// کلاس کمکی برای trigger کردن به‌روزرسانی sitemap
    /// </summary>
    public static class SitemapUpdateTrigger
    {
        private static readonly object _lockObject = new object();
        private static DateTime _lastTriggerTime = DateTime.MinValue;
        private static readonly TimeSpan _minInterval = TimeSpan.FromMinutes(5); // حداقل 5 دقیقه فاصله بین به‌روزرسانی‌ها

        /// <summary>
        /// trigger کردن به‌روزرسانی sitemap (با محدودیت زمانی)
        /// </summary>
        /// <param name="serviceProvider">Service provider برای دریافت سرویس‌ها</param>
        /// <param name="logger">Logger</param>
        /// <returns>true اگر trigger شد</returns>
        public static async Task<bool> TriggerUpdateAsync(IServiceProvider serviceProvider, ILogger logger)
        {
            lock (_lockObject)
            {
                var now = DateTime.UtcNow;
                if (now - _lastTriggerTime < _minInterval)
                {
                    return false; // خیلی زود است
                }
                _lastTriggerTime = now;
            }

            try
            {
                // اجرای به‌روزرسانی در background
                _ = Task.Run(async () =>
                {
                    try
                    {
                        using (var scope = serviceProvider.CreateScope())
                        {
                            var sitemapService = scope.ServiceProvider.GetRequiredService<ISitemapService>();
                            await sitemapService.RefreshSitemapAsync();
                            logger.LogInformation("Sitemap updated after data change");
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "Error updating sitemap after data change");
                    }
                });

                return true;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error triggering sitemap update");
                return false;
            }
        }
    }
}
