!function(a){"function"==typeof define&&define.amd?define(["jquery"],a):a("object"==typeof exports?require("jquery"):jQuery)}(function(a){function b(a,b){return a.toFixed(b.decimals)}var c=function(b,d){this.$element=a(b),this.options=a.extend({},c.DEFAULTS,this.dataOptions(),d),this.init()};c.DEFAULTS={from:0,to:0,speed:1e3,refreshInterval:100,decimals:0,formatter:b,onUpdate:null,onComplete:null},c.prototype.init=function(){this.value=this.options.from,this.loops=Math.ceil(this.options.speed/this.options.refreshInterval),this.loopCount=0,this.increment=(this.options.to-this.options.from)/this.loops},c.prototype.dataOptions=function(){var a={from:this.$element.data("from"),to:this.$element.data("to"),speed:this.$element.data("speed"),refreshInterval:this.$element.data("refresh-interval"),decimals:this.$element.data("decimals")},b=Object.keys(a);for(var c in b){var d=b[c];void 0===a[d]&&delete a[d]}return a},c.prototype.update=function(){this.value+=this.increment,this.loopCount++,this.render(),"function"==typeof this.options.onUpdate&&this.options.onUpdate.call(this.$element,this.value),this.loopCount>=this.loops&&(clearInterval(this.interval),this.value=this.options.to,"function"==typeof this.options.onComplete&&this.options.onComplete.call(this.$element,this.value))},c.prototype.render=function(){var a=this.options.formatter.call(this.$element,this.value,this.options);this.$element.text(a)},c.prototype.restart=function(){this.stop(),this.init(),this.start()},c.prototype.start=function(){this.stop(),this.render(),this.interval=setInterval(this.update.bind(this),this.options.refreshInterval)},c.prototype.stop=function(){this.interval&&clearInterval(this.interval)},c.prototype.toggle=function(){this.interval?this.stop():this.start()},a.fn.countTo=function(b){return this.each(function(){var d=a(this),e=d.data("countTo"),f=!e||"object"==typeof b,g="object"==typeof b?b:{},h="string"==typeof b?b:"start";f&&(e&&e.stop(),d.data("countTo",e=new c(this,g))),e[h].call(e)})}});