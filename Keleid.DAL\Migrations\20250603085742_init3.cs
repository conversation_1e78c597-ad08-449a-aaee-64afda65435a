﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Keleid.DAL.Migrations
{
    /// <inheritdoc />
    public partial class init3 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AdvertisementFeature_Advertisements_AdvertisementId",
                table: "AdvertisementFeature");

            migrationBuilder.DropForeignKey(
                name: "FK_AdvertisementFeature_CategoryFeature_CategoryFeatureId",
                table: "AdvertisementFeature");

            migrationBuilder.DropForeignKey(
                name: "FK_CategoryFeature_Categories_CategoryId",
                table: "CategoryFeature");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CategoryFeature",
                table: "CategoryFeature");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AdvertisementFeature",
                table: "AdvertisementFeature");

            migrationBuilder.RenameTable(
                name: "CategoryFeature",
                newName: "CategoryFeatures");

            migrationBuilder.RenameTable(
                name: "AdvertisementFeature",
                newName: "AdvertisementFeatures");

            migrationBuilder.RenameIndex(
                name: "IX_CategoryFeature_CategoryId",
                table: "CategoryFeatures",
                newName: "IX_CategoryFeatures_CategoryId");

            migrationBuilder.RenameIndex(
                name: "IX_AdvertisementFeature_CategoryFeatureId",
                table: "AdvertisementFeatures",
                newName: "IX_AdvertisementFeatures_CategoryFeatureId");

            migrationBuilder.RenameIndex(
                name: "IX_AdvertisementFeature_AdvertisementId",
                table: "AdvertisementFeatures",
                newName: "IX_AdvertisementFeatures_AdvertisementId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_CategoryFeatures",
                table: "CategoryFeatures",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_AdvertisementFeatures",
                table: "AdvertisementFeatures",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AdvertisementFeatures_Advertisements_AdvertisementId",
                table: "AdvertisementFeatures",
                column: "AdvertisementId",
                principalTable: "Advertisements",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AdvertisementFeatures_CategoryFeatures_CategoryFeatureId",
                table: "AdvertisementFeatures",
                column: "CategoryFeatureId",
                principalTable: "CategoryFeatures",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_CategoryFeatures_Categories_CategoryId",
                table: "CategoryFeatures",
                column: "CategoryId",
                principalTable: "Categories",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AdvertisementFeatures_Advertisements_AdvertisementId",
                table: "AdvertisementFeatures");

            migrationBuilder.DropForeignKey(
                name: "FK_AdvertisementFeatures_CategoryFeatures_CategoryFeatureId",
                table: "AdvertisementFeatures");

            migrationBuilder.DropForeignKey(
                name: "FK_CategoryFeatures_Categories_CategoryId",
                table: "CategoryFeatures");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CategoryFeatures",
                table: "CategoryFeatures");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AdvertisementFeatures",
                table: "AdvertisementFeatures");

            migrationBuilder.RenameTable(
                name: "CategoryFeatures",
                newName: "CategoryFeature");

            migrationBuilder.RenameTable(
                name: "AdvertisementFeatures",
                newName: "AdvertisementFeature");

            migrationBuilder.RenameIndex(
                name: "IX_CategoryFeatures_CategoryId",
                table: "CategoryFeature",
                newName: "IX_CategoryFeature_CategoryId");

            migrationBuilder.RenameIndex(
                name: "IX_AdvertisementFeatures_CategoryFeatureId",
                table: "AdvertisementFeature",
                newName: "IX_AdvertisementFeature_CategoryFeatureId");

            migrationBuilder.RenameIndex(
                name: "IX_AdvertisementFeatures_AdvertisementId",
                table: "AdvertisementFeature",
                newName: "IX_AdvertisementFeature_AdvertisementId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_CategoryFeature",
                table: "CategoryFeature",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_AdvertisementFeature",
                table: "AdvertisementFeature",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AdvertisementFeature_Advertisements_AdvertisementId",
                table: "AdvertisementFeature",
                column: "AdvertisementId",
                principalTable: "Advertisements",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AdvertisementFeature_CategoryFeature_CategoryFeatureId",
                table: "AdvertisementFeature",
                column: "CategoryFeatureId",
                principalTable: "CategoryFeature",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_CategoryFeature_Categories_CategoryId",
                table: "CategoryFeature",
                column: "CategoryId",
                principalTable: "Categories",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
