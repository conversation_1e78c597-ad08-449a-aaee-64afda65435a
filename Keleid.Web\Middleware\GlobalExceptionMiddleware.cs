using Keleid.BLL.Services;
using System.Net;
using System.Text.Json;

namespace Keleid.Web.Middleware
{
    public class GlobalExceptionMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<GlobalExceptionMiddleware> _logger;
        private readonly IServiceScopeFactory _serviceScopeFactory;

        public GlobalExceptionMiddleware(RequestDelegate next, ILogger<GlobalExceptionMiddleware> logger, IServiceScopeFactory serviceScopeFactory)
        {
            _next = next;
            _logger = logger;
            _serviceScopeFactory = serviceScopeFactory;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unhandled exception occurred");
                await HandleExceptionAsync(context, ex);
            }
        }

        private async Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            // ثبت خطا در دیتابیس
            await LogErrorToDatabase(context, exception);

            // تنظیم status code
            context.Response.StatusCode = exception switch
            {
                UnauthorizedAccessException => (int)HttpStatusCode.Unauthorized,
                FileNotFoundException or DirectoryNotFoundException => (int)HttpStatusCode.NotFound,
                ArgumentException or ArgumentNullException => (int)HttpStatusCode.BadRequest,
                _ => (int)HttpStatusCode.InternalServerError
            };

            // اگر درخواست AJAX است، JSON response برگردان
            if (IsAjaxRequest(context.Request))
            {
                context.Response.ContentType = "application/json";

                var response = new
                {
                    error = new
                    {
                        message = GetUserFriendlyMessage(exception),
                        details = exception.Message,
                        timestamp = DateTime.UtcNow,
                        statusCode = context.Response.StatusCode
                    }
                };

                var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                await context.Response.WriteAsync(jsonResponse);
            }
            // در غیر این صورت، به صفحه خطا هدایت کن
            else
            {
                context.Response.Redirect($"/Error/{context.Response.StatusCode}");
            }
        }

        private bool IsAjaxRequest(HttpRequest request)
        {
            return request.Headers["X-Requested-With"] == "XMLHttpRequest" ||
                   request.Headers["Content-Type"].ToString().Contains("application/json") ||
                   request.Path.StartsWithSegments("/api");
        }

        private async Task LogErrorToDatabase(HttpContext context, Exception exception)
        {
            try
            {
                using var scope = _serviceScopeFactory.CreateScope();
                var loggingService = scope.ServiceProvider.GetRequiredService<LoggingService>();

                var userId = context.User?.Identity?.Name;
                var source = $"{context.Request.Method} {context.Request.Path}";

                var properties = new
                {
                    RequestId = context.TraceIdentifier,
                    Method = context.Request.Method,
                    Path = context.Request.Path.ToString(),
                    QueryString = context.Request.QueryString.ToString(),
                    Headers = context.Request.Headers.ToDictionary(h => h.Key, h => h.Value.ToString()),
                    UserAgent = context.Request.Headers["User-Agent"].ToString(),
                    Referer = context.Request.Headers["Referer"].ToString()
                };

                await loggingService.LogErrorAsync(
                    message: exception.Message,
                    exception: exception,
                    source: source,
                    userId: userId,
                    properties: properties
                );
            }
            catch (Exception logEx)
            {
                // اگر خطا در ثبت لاگ باشد، فقط در console لاگ می‌کنیم
                _logger.LogError(logEx, "Failed to log error to database");
            }
        }

        private string GetUserFriendlyMessage(Exception exception)
        {
            return exception.GetType().Name switch
            {
                nameof(UnauthorizedAccessException) => "شما مجوز دسترسی به این بخش را ندارید.",
                nameof(FileNotFoundException) => "فایل مورد نظر یافت نشد.",
                nameof(DirectoryNotFoundException) => "پوشه مورد نظر یافت نشد.",
                nameof(ArgumentException) => "اطلاعات ارسالی نامعتبر است.",
                nameof(ArgumentNullException) => "اطلاعات الزامی ارسال نشده است.",
                nameof(TimeoutException) => "زمان انتظار تمام شد. لطفاً دوباره تلاش کنید.",
                nameof(OutOfMemoryException) => "سیستم با کمبود حافظه مواجه شده است.",
                _ => "خطای غیرمنتظره‌ای رخ داده است. لطفاً با پشتیبانی تماس بگیرید."
            };
        }
    }
}
