﻿@{
    // بررسی دسترسی کاربر به پنل ادمین
    if (!User.Identity.IsAuthenticated)
    {
        Context.Response.Redirect("/Account/Index");
        return;
    }
    else if (!User.IsInRole("Admin"))
    {
        Context.Response.Redirect("/User/Index");
        return;
    }
}

<!doctype html>
<html class="no-js " lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge">
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <meta name="description" content="پنل ادمین کلید">
    <title>پنل ادمین کلید | @ViewBag.Title</title>

    <link rel="shortcut icon" href="~/favicon.ico" />

    <link rel="stylesheet" href="~/admin-assets/plugins/bootstrap/css/bootstrap.min.css">
    <!-- Custom Css -->
    <link rel="stylesheet" href="~/admin-assets/css/main.css">
    <link rel="stylesheet" href="~/admin-assets/css/color_skins.css">
</head>

<body class="theme-cyan">
    <!-- Page Loader -->
    <div class="page-loader-wrapper">
        <div class="loader">
            <div class="m-t-30">
                <img class="zmdi-hc-spin" src="~/admin-assets/images/logo.png" width="48" height="48" alt="کلید">
            </div>
            <p>لطفا صبر کنید ...</p>
        </div>
    </div>

    <!-- Overlay For Sidebars -->
    <div class="overlay"></div>

    <!-- Top Bar -->
    <nav class="navbar">
        <div class="col-12">
            <div class="navbar-header">
                <a href="javascript:void(0);" class="bars"></a>
                <a class="navbar-brand" asp-area="Admin" asp-controller="Home" asp-action="Index">
                    <img src="~/admin-assets/images/logo.png" width="30" alt="کلید"><span class="m-r-10">کلید ادمین</span>
                </a>
            </div>
            <ul class="nav navbar-nav navbar-right">
                <li>
                    <a href="javascript:void(0);" class="ls-toggle-btn" data-close="true">
                        <i class="zmdi zmdi-swap"></i>
                    </a>
                </li>
            </ul>
            <ul class="nav navbar-nav navbar-left">
                @await Component.InvokeAsync("NotificationHeader")
                @await Component.InvokeAsync("TaskHeader")
                <li>
                    <form asp-area="Admin" asp-controller="Home" asp-action="Logout" method="post" style="margin: 0; display: inline;" onsubmit="return confirmLogout()">
                        @Html.AntiForgeryToken()
                        <button type="submit" class="mega-menu" data-close="true" style="border: none; background: none; color: inherit; cursor: pointer;" title="خروج از سیستم">
                            <i class="zmdi zmdi-power"></i>
                        </button>
                    </form>
                </li>
            </ul>
        </div>
    </nav>

    @RenderBody()

    <!-- Jquery Core Js -->
    <script src="~/admin-assets/bundles/libscripts.bundle.js"></script>

    <!-- Lib Scripts Plugin Js ( jquery.v3.2.1, Bootstrap4 js) -->
    <script src="~/admin-assets/bundles/vendorscripts.bundle.js"></script> <!-- slimscroll, waves Scripts Plugin Js -->

    <script src="~/admin-assets/bundles/mainscripts.bundle.js"></script>

    <script>
        function confirmLogout() {
            return confirm('آیا مطمئن هستید که می‌خواهید از پنل ادمین خارج شوید؟');
        }
    </script>
</body>

</html>