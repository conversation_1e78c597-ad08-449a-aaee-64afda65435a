﻿using Keleid.BLL.Services;
using Keleid.BLL.Services.Interfaces;
using Keleid.Web.ViewModels;
using Keleid.Web.Helpers;
using Keleid.Web.Services;
using Keleid.Web.Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Keleid.Web.Controllers
{
    [Authorize] // فقط کاربران لاگین شده می‌توانند آگهی ثبت کنند
    public class AdsController : BaseController
    {
        private readonly ICategoryService _categoryService;
        private readonly IFileService _fileService;
        private readonly IAdvertisementService _advertisementService;
        private readonly ILocationService _locationService;
        private readonly LoggingService _loggingService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public AdsController(ICategoryService categoryService, IFileService fileService, IAdvertisementService advertisementService, ILocationService locationService, LoggingService loggingService, IHttpContextAccessor httpContextAccessor)
        {
            _categoryService = categoryService;
            _fileService = fileService;
            _advertisementService = advertisementService;
            _locationService = locationService;
            _loggingService = loggingService;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<IActionResult> Index()
        {
            // پاک کردن session قبلی (شروع فرآیند جدید)
            SessionHelper.ClearCreateAdvertisementData(HttpContext.Session);

            // دریافت تمام دسته‌بندی‌ها (اصلی و زیردسته‌ها)
            var categories = await _categoryService.GetAllCategoriesAsync();
            return View(categories);
        }

        public async Task<IActionResult> Details(int? categoryId)
        {
            if (!categoryId.HasValue)
            {
                return RedirectToAction("Index");
            }

            // بررسی اینکه categoryId مربوط به زیردسته‌بندی باشد (نه دسته اصلی)
            var category = await _categoryService.GetCategoryByIdAsync(categoryId.Value);
            if (category == null || category.ParentCategoryId == null)
            {
                TempData["Error"] = "لطفاً یک زیردسته‌بندی انتخاب کنید.";
                return RedirectToAction("Index");
            }

            // ذخیره انتخاب دسته‌بندی در Session
            SessionHelper.UpdateCreateAdvertisementStep(HttpContext.Session, 2, model =>
            {
                model.CategoryId = categoryId.Value;
                model.CategoryTitle = category.Title;
            });

            // دریافت اطلاعات از Session
            var sessionData = SessionHelper.GetCreateAdvertisementData(HttpContext.Session);

            // دریافت ویژگی‌های دسته‌بندی
            var categoryFeatures = await _categoryService.GetCategoryFeaturesAsync(categoryId.Value);

            var viewModel = new AdsDetailsViewModel
            {
                CategoryId = categoryId.Value,
                CategoryTitle = category.Title,
                CategoryFeatures = categoryFeatures,
                Title = sessionData?.Title ?? "",
                Description = sessionData?.Description ?? "",
                Price = sessionData?.Price,
                IsPriceless = sessionData?.IsPriceless ?? false,
                FeatureValues = sessionData?.FeatureValues ?? new Dictionary<int, string>()
            };

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> Details(AdsDetailsViewModel model)
        {
            // خواندن فیلدهای اختصاصی از Request.Form
            model.FeatureValues = new Dictionary<int, string>();
            foreach (var key in Request.Form.Keys)
            {
                if (key.StartsWith("Features[") && key.EndsWith("]"))
                {
                    var idString = key.Substring(9, key.Length - 10);
                    if (int.TryParse(idString, out int id))
                    {
                        var value = Request.Form[key].ToString();
                        if (!string.IsNullOrEmpty(value))
                        {
                            model.FeatureValues[id] = value;
                        }
                    }
                }
            }

            // دریافت ویژگی‌های دسته‌بندی برای اعتبارسنجی
            var categoryFeatures = await _categoryService.GetCategoryFeaturesAsync(model.CategoryId);
            model.CategoryFeatures = categoryFeatures;

            // اعتبارسنجی سفارشی

            // 1. بررسی عنوان
            if (string.IsNullOrWhiteSpace(model.Title) || model.Title.Length < 10)
            {
                ModelState.AddModelError(nameof(model.Title), "عنوان آگهی باید حداقل 10 کاراکتر باشد");
            }

            // 2. بررسی توضیحات
            if (string.IsNullOrWhiteSpace(model.Description) || model.Description.Length < 30)
            {
                ModelState.AddModelError(nameof(model.Description), "توضیحات باید حداقل 30 کاراکتر باشد");
            }

            // 3. بررسی قیمت (فقط اگر توافقی انتخاب نشده باشد)
            if (!model.IsPriceless)
            {
                if (!model.Price.HasValue || model.Price.Value <= 0)
                {
                    ModelState.AddModelError(nameof(model.Price), "قیمت باید وارد شود یا گزینه توافقی را انتخاب کنید");
                }
            }

            // 4. بررسی فیلدهای اختصاصی اجباری
            foreach (var feature in categoryFeatures.Where(f => f.IsRequired))
            {
                if (!model.FeatureValues.ContainsKey(feature.Id) ||
                    string.IsNullOrWhiteSpace(model.FeatureValues[feature.Id]))
                {
                    ModelState.AddModelError($"Features[{feature.Id}]", $"{feature.Title} الزامی است");
                }
            }

            if (!ModelState.IsValid)
            {
                return View(model);
            }

            // ذخیره اطلاعات در Session
            SessionHelper.UpdateCreateAdvertisementStep(HttpContext.Session, 3, sessionModel =>
            {
                sessionModel.Title = model.Title;
                sessionModel.Description = model.Description;
                sessionModel.Price = model.Price;
                sessionModel.IsPriceless = model.IsPriceless;
                sessionModel.FeatureValues = model.FeatureValues;
            });

            // انتقال به مرحله بعد
            return RedirectToAction("Images", new { categoryId = model.CategoryId });
        }

        public async Task<IActionResult> Images(int? categoryId)
        {
            if (!categoryId.HasValue)
            {
                return RedirectToAction("Index");
            }

            // بررسی اینکه کاربر از مراحل قبلی عبور کرده باشد
            if (!SessionHelper.CanAccessStep(HttpContext.Session, 3))
            {
                return RedirectToAction("Index");
            }

            // دریافت اطلاعات از Session
            var sessionData = SessionHelper.GetCreateAdvertisementData(HttpContext.Session);
            if (sessionData == null || sessionData.CategoryId != categoryId.Value)
            {
                return RedirectToAction("Index");
            }

            // دریافت اطلاعات دسته‌بندی
            var category = await _categoryService.GetCategoryByIdAsync(categoryId.Value);
            if (category == null)
            {
                return RedirectToAction("Index");
            }

            var viewModel = new AdsImagesViewModel
            {
                CategoryId = categoryId.Value,
                CategoryTitle = category.Title,
                ExistingImages = sessionData.ImageUrls?.Select((url, index) => new BLL.DTOs.AdImageDto
                {
                    Id = index + 1, // موقت
                    ImageUrl = url,
                    IsMain = index == (sessionData.MainImageIndex ?? 0)
                }).ToList() ?? new List<BLL.DTOs.AdImageDto>()
            };

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> Images(int categoryId, List<IFormFile> images, int? mainImageIndex)
        {
            try
            {
                // بررسی دسترسی
                if (!SessionHelper.CanAccessStep(HttpContext.Session, 3))
                {
                    return RedirectToAction("Index");
                }

                var sessionData = SessionHelper.GetCreateAdvertisementData(HttpContext.Session);
                if (sessionData == null || sessionData.CategoryId != categoryId)
                {
                    return RedirectToAction("Index");
                }

                // اعتبارسنجی تصاویر
                if (images == null || !images.Any())
                {
                    TempData["Error"] = "لطفاً حداقل یک تصویر آپلود کنید.";
                    return await Images(categoryId);
                }

                if (images.Count > 10)
                {
                    TempData["Error"] = "حداکثر 10 تصویر می‌توانید آپلود کنید.";
                    return await Images(categoryId);
                }

                // بررسی معتبر بودن فایل‌ها
                foreach (var image in images)
                {
                    if (!_fileService.IsValidImageFile(image))
                    {
                        TempData["Error"] = $"فایل {image.FileName} معتبر نیست. فقط فایل‌های JPG، PNG و GIF با حداکثر حجم 10 مگابایت مجاز هستند.";
                        return await Images(categoryId);
                    }
                }

                // آپلود فایل‌ها به مکان موقت
                var tempImageUrls = new List<string>();
                var tempPath = Path.Combine("wwwroot", "temp");

                // ایجاد پوشه temp اگر وجود ندارد
                if (!Directory.Exists(tempPath))
                {
                    Directory.CreateDirectory(tempPath);
                }

                foreach (var image in images)
                {
                    var fileName = $"{Guid.NewGuid()}{Path.GetExtension(image.FileName)}";
                    var filePath = Path.Combine(tempPath, fileName);

                    using (var stream = new FileStream(filePath, FileMode.Create))
                    {
                        await image.CopyToAsync(stream);
                    }

                    tempImageUrls.Add($"temp/{fileName}");
                }

                // ذخیره اطلاعات تصاویر در Session
                SessionHelper.UpdateCreateAdvertisementStep(HttpContext.Session, 4, model =>
                {
                    model.ImageUrls = tempImageUrls;
                    model.MainImageIndex = mainImageIndex ?? 0;
                });

                // انتقال به مرحله بعد
                return RedirectToAction("Location", new { categoryId });
            }
            catch (Exception ex)
            {
                TempData["Error"] = "خطا در آپلود تصاویر. لطفاً دوباره تلاش کنید.";
                return await Images(categoryId);
            }
        }

        // API برای آپلود تصاویر به صورت AJAX
        [HttpPost]
        public async Task<IActionResult> UploadImage(int categoryId, IFormFile image)
        {
            try
            {
                if (!_fileService.IsValidImageFile(image))
                {
                    return Json(new { success = false, message = "فایل انتخاب شده معتبر نیست." });
                }

                // فعلاً فقط یک GUID موقت برمی‌گردانیم
                var tempId = Guid.NewGuid().ToString();
                var tempUrl = $"/temp/{tempId}_{image.FileName}";

                return Json(new {
                    success = true,
                    imageUrl = tempUrl,
                    fileName = image.FileName,
                    tempId = tempId
                });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "خطا در آپلود تصویر." });
            }
        }

        public async Task<IActionResult> Location(int? categoryId)
        {
            if (!categoryId.HasValue)
            {
                return RedirectToAction("Index");
            }

            // بررسی اینکه کاربر از مراحل قبلی عبور کرده باشد
            if (!SessionHelper.CanAccessStep(HttpContext.Session, 4))
            {
                return RedirectToAction("Index");
            }

            // دریافت اطلاعات از Session
            var sessionData = SessionHelper.GetCreateAdvertisementData(HttpContext.Session);
            if (sessionData == null || sessionData.CategoryId != categoryId.Value)
            {
                return RedirectToAction("Index");
            }

            // دریافت اطلاعات دسته‌بندی
            var category = await _categoryService.GetCategoryByIdAsync(categoryId.Value);
            if (category == null)
            {
                return RedirectToAction("Index");
            }

            // دریافت استان‌ها از دیتابیس
            var provinces = await _locationService.GetAllProvincesAsync();

            // دریافت شهرهای استان انتخاب شده (اگر موجود باشد)
            var cities = new List<BLL.DTOs.CityDto>();
            if (!string.IsNullOrEmpty(sessionData.Province))
            {
                cities = await _locationService.GetCitiesByProvinceNameAsync(sessionData.Province);
            }

            var viewModel = new AdsLocationViewModel
            {
                CategoryId = categoryId.Value,
                CategoryTitle = category.Title,
                // پر کردن فیلدها از Session اگر موجود باشد
                Province = sessionData.Province ?? "",
                City = sessionData.City ?? "",
                Address = sessionData.Address ?? "",
                ContactName = sessionData.ContactName ?? "",
                ContactPhone = sessionData.ContactPhone ?? "",
                ContactEmail = sessionData.ContactEmail ?? "",
                // داده‌های دیتابیس
                Provinces = provinces,
                Cities = cities
            };

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> Location(AdsLocationViewModel model)
        {
            try
            {
                // بررسی دسترسی
                if (!SessionHelper.CanAccessStep(HttpContext.Session, 4))
                {
                    return RedirectToAction("Index");
                }

                var sessionData = SessionHelper.GetCreateAdvertisementData(HttpContext.Session);
                if (sessionData == null || sessionData.CategoryId != model.CategoryId)
                {
                    return RedirectToAction("Index");
                }

                if (!ModelState.IsValid)
                {
                    // دریافت اطلاعات دسته‌بندی برای نمایش مجدد
                    var category = await _categoryService.GetCategoryByIdAsync(model.CategoryId);
                    model.CategoryTitle = category?.Title ?? "";
                    return View(model);
                }

                // ذخیره اطلاعات در Session
                SessionHelper.UpdateCreateAdvertisementStep(HttpContext.Session, 5, data =>
                {
                    data.Province = model.Province;
                    data.City = model.City;
                    data.Address = model.Address;
                    data.ContactName = model.ContactName;
                    data.ContactPhone = model.ContactPhone;
                    data.ContactEmail = model.ContactEmail;
                });

                // دریافت اطلاعات بروزرسانی شده از Session
                var updatedSessionData = SessionHelper.GetCreateAdvertisementData(HttpContext.Session);

                if (updatedSessionData == null)
                {
                    TempData["Error"] = "خطا در دریافت اطلاعات Session. لطفاً دوباره تلاش کنید.";
                    return View(model);
                }

                // ایجاد آگهی در دیتابیس
                var advertisementId = await CreateAdvertisementAsync(updatedSessionData);

                if (advertisementId > 0)
                {
                    // لاگ کردن ثبت آگهی موفق
                    var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                    await _loggingService.LogAdvertisementCreateAsync(advertisementId, userId, updatedSessionData.Title, _httpContextAccessor);

                    // پاک کردن Session
                    SessionHelper.ClearCreateAdvertisementData(HttpContext.Session);

                    // پاک کردن فایل‌های موقت قدیمی (اختیاری)
                    CleanupOldTempFiles();

                    // انتقال به صفحه موفقیت
                    TempData["Success"] = "آگهی شما با موفقیت ثبت شد و پس از تایید نمایش داده خواهد شد.";
                    return RedirectToAction("Success", new { id = advertisementId });
                }
                else
                {
                    TempData["Error"] = "خطا در ثبت آگهی. لطفاً دوباره تلاش کنید.";
                    return View(model);
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = "خطا در ثبت آگهی. لطفاً دوباره تلاش کنید.";
                return View(model);
            }
        }

        public IActionResult Success(int id)
        {
            ViewBag.AdvertisementId = id;
            return View();
        }

        // API برای دریافت شهرها بر اساس استان
        [HttpGet]
        public async Task<IActionResult> GetCitiesByProvince(string provinceName)
        {
            try
            {
                if (string.IsNullOrEmpty(provinceName))
                {
                    return Json(new { success = false, message = "نام استان الزامی است" });
                }

                var cities = await _locationService.GetCitiesByProvinceNameAsync(provinceName);
                return Json(new {
                    success = true,
                    cities = cities.Select(c => new { id = c.Id, title = c.Title }).ToList()
                });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "خطا در دریافت شهرها" });
            }
        }



        #region Private Methods

        private async Task<int> CreateAdvertisementAsync(CreateAdvertisementViewModel sessionData)
        {
            try
            {
                // ایجاد Location
                var location = new DAL.Models.Location
                {
                    Province = sessionData.Province ?? "",
                    City = sessionData.City ?? "",
                    Addresss = sessionData.Address ?? "" // توجه: در مدل Addresss با دو s نوشته شده
                };

                // ایجاد Advertisement
                var advertisement = new DAL.Models.Advertisement
                {
                    Title = sessionData.Title ?? "",
                    Slug = GenerateSlug(sessionData.Title ?? ""),
                    Description = sessionData.Description ?? "",
                    Price = sessionData.Price ?? 0,
                    IsPriceless = sessionData.IsPriceless,
                    CategoryId = sessionData.CategoryId,
                    Location = location,
                    UserId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? throw new InvalidOperationException("کاربر لاگین نیست"),
                    CreatedAt = DateTime.UtcNow,
                    IsApproved = false
                };

                // ایجاد ContactInfo
                var contactInfo = new DAL.Models.ContactInfo
                {
                    Name = sessionData.ContactName ?? "",
                    PhoneNumber = sessionData.ContactPhone ?? "",
                    Email = sessionData.ContactEmail ?? "",
                    Advertisement = advertisement
                };

                advertisement.ContactInfo = contactInfo;

                // ایجاد AdvertisementFeatures
                if (sessionData.FeatureValues?.Any() == true)
                {
                    advertisement.Features = sessionData.FeatureValues.Select(fv => new DAL.Models.AdvertisementFeature
                    {
                        CategoryFeatureId = fv.Key,
                        Value = fv.Value,
                        Advertisement = advertisement
                    }).ToList();
                }

                // ذخیره در دیتابیس
                var advertisementId = await _advertisementService.CreateAdvertisementAsync(advertisement);

                // آپلود تصاویر
                if (advertisementId > 0 && sessionData.ImageUrls?.Any() == true)
                {
                    await UploadAdvertisementImagesAsync(advertisementId, sessionData);
                }
                else if (sessionData.ImageUrls?.Any() == true)
                {
                    // اگر آگهی ایجاد نشد، فایل‌های موقت را پاک کن
                    CleanupTempImages(sessionData.ImageUrls);
                }

                return advertisementId;
            }
            catch (Exception ex)
            {
                return 0;
            }
        }

        private async Task UploadAdvertisementImagesAsync(int advertisementId, CreateAdvertisementViewModel sessionData)
        {
            try
            {
                if (sessionData.ImageUrls?.Any() == true)
                {
                    var images = new List<DAL.Models.AdImage>();

                    // ایجاد پوشه برای آگهی
                    var adImagePath = Path.Combine("wwwroot", "AdsImages", advertisementId.ToString());

                    if (!Directory.Exists(adImagePath))
                    {
                        Directory.CreateDirectory(adImagePath);
                    }

                    for (int i = 0; i < sessionData.ImageUrls.Count; i++)
                    {
                        var tempImageUrl = sessionData.ImageUrls[i];
                        var isMain = i == (sessionData.MainImageIndex ?? 0);

                        try
                        {
                            // مسیر فایل موقت
                            var tempFilePath = Path.Combine("wwwroot", tempImageUrl.TrimStart('/'));

                            if (System.IO.File.Exists(tempFilePath))
                            {
                                // نام فایل جدید با GUID
                                var newFileName = $"{Guid.NewGuid()}.jpg";
                                var finalFilePath = Path.Combine(adImagePath, newFileName);
                                var finalImageUrl = $"/AdsImages/{advertisementId}/{newFileName}";

                                // کپی فایل از مکان موقت به مکان نهایی
                                System.IO.File.Copy(tempFilePath, finalFilePath, true);

                                // حذف فایل موقت
                                System.IO.File.Delete(tempFilePath);

                                images.Add(new DAL.Models.AdImage
                                {
                                    AdvertisementId = advertisementId,
                                    ImageUrl = finalImageUrl,
                                    IsMain = isMain
                                });
                            }
                        }
                        catch (Exception ex)
                        {
                            // خطا در پردازش تصویر - ادامه با تصاویر بعدی
                        }
                    }

                    // ذخیره تصاویر در دیتابیس
                    foreach (var image in images)
                    {
                        await _advertisementService.AddImageAsync(image);
                    }
                }
            }
            catch (Exception ex)
            {
                // خطا در آپلود تصاویر
            }
        }

        private void CleanupTempImages(List<string> imageUrls)
        {
            try
            {
                foreach (var imageUrl in imageUrls)
                {
                    var tempFilePath = Path.Combine("wwwroot", imageUrl.TrimStart('/'));
                    if (System.IO.File.Exists(tempFilePath))
                    {
                        System.IO.File.Delete(tempFilePath);
                    }
                }
            }
            catch (Exception ex)
            {
                // خطا در پاک‌سازی فایل‌های موقت
            }
        }

        private void CleanupOldTempFiles()
        {
            try
            {
                var tempPath = Path.Combine("wwwroot", "temp");
                if (Directory.Exists(tempPath))
                {
                    var files = Directory.GetFiles(tempPath);
                    var cutoffTime = DateTime.Now.AddHours(-2); // فایل‌های بیش از 2 ساعت قدیمی

                    foreach (var file in files)
                    {
                        var fileInfo = new FileInfo(file);
                        if (fileInfo.CreationTime < cutoffTime)
                        {
                            System.IO.File.Delete(file);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // خطا در پاک‌سازی فایل‌های قدیمی
            }
        }

        private string GenerateSlug(string title)
        {
            if (string.IsNullOrEmpty(title))
                return Guid.NewGuid().ToString();

            // ساده‌سازی slug - فقط حروف و اعداد
            var slug = title.Replace(" ", "-").Replace("‌", "-");
            slug = System.Text.RegularExpressions.Regex.Replace(slug, @"[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFFa-zA-Z0-9\-]", "");

            return $"{slug}-{Guid.NewGuid().ToString("N")[..8]}";
        }

        #endregion
    }
}
