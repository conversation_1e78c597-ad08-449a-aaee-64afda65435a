﻿using Keleid.BLL.Services;
using Keleid.DAL.Models;
using Keleid.Web.Extensions;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;

namespace Keleid.Web.Areas.Admin.Controllers
{
    public class HomeController : BaseAdminController
    {
        private readonly NotificationService _notificationService;
        private readonly UserTaskService _taskService;
        private readonly DashboardService _dashboardService;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly LoggingService _loggingService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public HomeController(NotificationService notificationService, UserTaskService taskService,
            DashboardService dashboardService, SignInManager<ApplicationUser> signInManager, LoggingService loggingService, IHttpContextAccessor httpContextAccessor)
        {
            _notificationService = notificationService;
            _taskService = taskService;
            _dashboardService = dashboardService;
            _signInManager = signInManager;
            _loggingService = loggingService;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<IActionResult> Index()
        {
            try
            {
                var dashboardData = await _dashboardService.GetDashboardDataAsync();
                return View(dashboardData);
            }
            catch (Exception ex)
            {
                ViewBag.ErrorMessage = $"خطا در بارگذاری داشبورد: {ex.Message}";
                return View(new Keleid.BLL.DTOs.DashboardDto());
            }
        }

        public async Task<IActionResult> Notifications(int page = 1)
        {
            const int pageSize = 10;
            var (notifications, totalCount, totalPages) = await _notificationService.GetNotificationsWithPaginationAsync(page, pageSize);

            var model = new Keleid.BLL.DTOs.NotificationPageDto
            {
                Notifications = notifications,
                CurrentPage = page,
                TotalPages = totalPages,
                TotalCount = totalCount
            };

            return View(model);
        }

        public async Task<IActionResult> Tasks(int page = 1)
        {
            const int pageSize = 10;
            var (tasks, totalCount, totalPages) = await _taskService.GetTasksWithPaginationAsync(page, pageSize);

            var model = new Keleid.BLL.DTOs.UserTaskPageDto
            {
                Tasks = tasks,
                CurrentPage = page,
                TotalPages = totalPages,
                TotalCount = totalCount
            };

            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> MarkNotificationAsRead([FromBody] MarkAsReadRequest request)
        {
            // دریافت UserId کاربر فعلی
            var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            var adminUsername = User.Identity?.Name ?? "Unknown";

            var result = await _notificationService.MarkAsReadAsync(request.Id, userId);

            if (result)
            {
                // لاگ کردن خواندن اعلان
                await _loggingService.LogNotificationReadAsync(request.Id, adminUsername, _httpContextAccessor);
            }

            return Json(new { success = result });
        }

        [HttpPost]
        public async Task<IActionResult> MarkTaskAsCompleted([FromBody] MarkAsCompletedRequest request)
        {
            // دریافت UserId کاربر فعلی
            var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            var adminUsername = User.Identity?.Name ?? "Unknown";

            var result = await _taskService.MarkAsCompletedAsync(request.Id, userId);

            if (result)
            {
                // لاگ کردن انجام وظیفه
                await _loggingService.LogTaskCompleteAsync(request.Id, adminUsername, _httpContextAccessor);
            }

            return Json(new { success = result });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Logout()
        {
            await _signInManager.SignOutAsync();
            HttpContext.Session.Clear();
            return RedirectToAction("Index", "Home", new { area = "" }); // هدایت به صفحه اصلی سایت
        }
    }

    public class MarkAsReadRequest
    {
        public int Id { get; set; }
    }

    public class MarkAsCompletedRequest
    {
        public int Id { get; set; }
    }
}
