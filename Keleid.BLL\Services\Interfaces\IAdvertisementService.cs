using Keleid.BLL.DTOs;

namespace Keleid.BLL.Services.Interfaces
{
    public interface IAdvertisementService
    {
        /// <summary>
        /// دریافت تمام آگهی‌های تایید شده و غیر حذف شده
        /// </summary>
        /// <returns>لیست آگهی‌ها</returns>
        Task<List<AdvertisementDto>> GetApprovedAdvertisementsAsync();

        /// <summary>
        /// دریافت آگهی‌های در انتظار تایید
        /// </summary>
        /// <returns>لیست آگهی‌های در انتظار تایید</returns>
        Task<List<AdvertisementDto>> GetPendingAdvertisementsAsync();

        /// <summary>
        /// دریافت آگهی‌های یک دسته‌بندی خاص
        /// </summary>
        /// <param name="categoryId">شناسه دسته‌بندی</param>
        /// <returns>لیست آگهی‌های دسته‌بندی</returns>
        Task<List<AdvertisementDto>> GetAdvertisementsByCategoryAsync(int categoryId);

        /// <summary>
        /// دریافت آگهی‌های یک شهر خاص
        /// </summary>
        /// <param name="city">نام شهر</param>
        /// <returns>لیست آگهی‌های شهر</returns>
        Task<List<AdvertisementDto>> GetAdvertisementsByCityAsync(string city);

        /// <summary>
        /// دریافت آگهی با شناسه
        /// </summary>
        /// <param name="id">شناسه آگهی</param>
        /// <returns>آگهی</returns>
        Task<AdvertisementDto?> GetAdvertisementByIdAsync(int id);

        /// <summary>
        /// دریافت آگهی بر اساس شناسه برای ادمین (بدون فیلتر وضعیت)
        /// </summary>
        /// <param name="id">شناسه آگهی</param>
        /// <returns>آگهی یافت شده</returns>
        Task<AdvertisementDto?> GetAdvertisementByIdForAdminAsync(int id);

        /// <summary>
        /// دریافت آگهی با شناسه و Slug
        /// </summary>
        /// <param name="id">شناسه آگهی</param>
        /// <param name="slug">Slug آگهی</param>
        /// <returns>آگهی</returns>
        Task<AdvertisementDto?> GetAdvertisementByIdAndSlugAsync(int id, string slug);

        /// <summary>
        /// دریافت آگهی‌های یک کاربر
        /// </summary>
        /// <param name="userId">شناسه کاربر</param>
        /// <returns>لیست آگهی‌های کاربر</returns>
        Task<List<AdvertisementDto>> GetUserAdvertisementsAsync(string userId);

        /// <summary>
        /// دریافت آگهی‌های یک کاربر با صفحه‌بندی
        /// </summary>
        /// <param name="userId">شناسه کاربر</param>
        /// <param name="pageNumber">شماره صفحه</param>
        /// <param name="pageSize">تعداد آگهی در هر صفحه</param>
        /// <returns>لیست آگهی‌های کاربر با اطلاعات صفحه‌بندی</returns>
        Task<(List<AdvertisementDto> advertisements, int totalCount, int totalPages)> GetUserAdvertisementsWithPaginationAsync(string userId, int pageNumber = 1, int pageSize = 10);

        /// <summary>
        /// حذف نرم آگهی (تنظیم IsDeleted به true)
        /// </summary>
        /// <param name="advertisementId">شناسه آگهی</param>
        /// <param name="userId">شناسه کاربر (برای اطمینان از مالکیت)</param>
        /// <returns>نتیجه عملیات حذف</returns>
        Task<bool> SoftDeleteAdvertisementAsync(int advertisementId, string userId);

        /// <summary>
        /// دریافت آگهی‌های نشان شده توسط کاربر با صفحه‌بندی
        /// </summary>
        /// <param name="userId">شناسه کاربر</param>
        /// <param name="pageNumber">شماره صفحه</param>
        /// <param name="pageSize">تعداد آگهی در هر صفحه</param>
        /// <returns>لیست آگهی‌های نشان شده با اطلاعات صفحه‌بندی</returns>
        Task<(List<FavoriteDto> favorites, int totalCount, int totalPages)> GetUserFavoritesWithPaginationAsync(string userId, int pageNumber = 1, int pageSize = 10);

        /// <summary>
        /// حذف آگهی از لیست نشان شده‌ها
        /// </summary>
        /// <param name="advertisementId">شناسه آگهی</param>
        /// <param name="userId">شناسه کاربر</param>
        /// <returns>نتیجه عملیات حذف</returns>
        Task<bool> RemoveFavoriteAsync(int advertisementId, string userId);

        /// <summary>
        /// بررسی اینکه آیا آگهی توسط کاربر نشان شده است یا خیر
        /// </summary>
        /// <param name="advertisementId">شناسه آگهی</param>
        /// <param name="userId">شناسه کاربر</param>
        /// <returns>true اگر نشان شده باشد</returns>
        Task<bool> IsAdvertisementFavoritedAsync(int advertisementId, string userId);

        /// <summary>
        /// اضافه کردن آگهی به لیست نشان شده‌ها
        /// </summary>
        /// <param name="advertisementId">شناسه آگهی</param>
        /// <param name="userId">شناسه کاربر</param>
        /// <returns>نتیجه عملیات اضافه کردن</returns>
        Task<bool> AddFavoriteAsync(int advertisementId, string userId);

        /// <summary>
        /// تغییر وضعیت نشان کردن آگهی (اضافه/حذف)
        /// </summary>
        /// <param name="advertisementId">شناسه آگهی</param>
        /// <param name="userId">شناسه کاربر</param>
        /// <returns>وضعیت جدید (true = نشان شده، false = نشان نشده)</returns>
        Task<bool> ToggleFavoriteAsync(int advertisementId, string userId);



        /// <summary>
        /// جستجو در آگهی‌ها
        /// </summary>
        /// <param name="searchTerm">کلمه جستجو</param>
        /// <returns>لیست آگهی‌های یافت شده</returns>
        Task<List<AdvertisementDto>> SearchAdvertisementsAsync(string searchTerm);

        /// <summary>
        /// دریافت آگهی‌ها با فیلتر پیشرفته
        /// </summary>
        /// <param name="categoryId">شناسه دسته‌بندی (اختیاری)</param>
        /// <param name="city">شهر (اختیاری)</param>
        /// <param name="province">استان (اختیاری)</param>
        /// <param name="minPrice">حداقل قیمت (اختیاری)</param>
        /// <param name="maxPrice">حداکثر قیمت (اختیاری)</param>
        /// <param name="searchTerm">کلمه جستجو (اختیاری)</param>
        /// <param name="pageNumber">شماره صفحه</param>
        /// <param name="pageSize">تعداد آگهی در هر صفحه</param>
        /// <returns>لیست آگهی‌های فیلتر شده</returns>
        Task<(List<AdvertisementDto> advertisements, int totalCount)> GetFilteredAdvertisementsAsync(
            int? categoryId = null,
            string? city = null,
            string? province = null,
            long? minPrice = null,
            long? maxPrice = null,
            string? searchTerm = null,
            int pageNumber = 1,
            int pageSize = 20);

        /// <summary>
        /// دریافت آخرین آگهی‌ها با فیلتر استان و جستجو
        /// </summary>
        /// <param name="count">تعداد آگهی</param>
        /// <param name="categorySlug">اسلاگ دسته‌بندی (اختیاری)</param>
        /// <param name="province">استان (اختیاری)</param>
        /// <param name="searchTerm">عبارت جستجو (اختیاری)</param>
        /// <returns>لیست آخرین آگهی‌ها</returns>
        Task<List<AdvertisementDto>> GetLatestAdvertisementsAsync(int count, string? categorySlug = null, string? province = null, string? searchTerm = null);

        /// <summary>
        /// دریافت آخرین آگهی‌ها با صفحه‌بندی برای صفحه اصلی
        /// </summary>
        /// <param name="pageNumber">شماره صفحه</param>
        /// <param name="pageSize">تعداد آگهی در هر صفحه</param>
        /// <param name="categorySlug">اسلاگ دسته‌بندی (اختیاری)</param>
        /// <param name="province">استان (اختیاری)</param>
        /// <param name="searchTerm">عبارت جستجو (اختیاری)</param>
        /// <returns>لیست آخرین آگهی‌ها با اطلاعات صفحه‌بندی</returns>
        Task<(List<AdvertisementDto> advertisements, int totalCount, bool hasMore)> GetLatestAdvertisementsWithPaginationAsync(int pageNumber = 1, int pageSize = 6, string? categorySlug = null, string? province = null, string? searchTerm = null);

        /// <summary>
        /// دریافت تمام آگهی‌ها با صفحه‌بندی برای پنل ادمین
        /// </summary>
        /// <param name="pageNumber">شماره صفحه</param>
        /// <param name="pageSize">تعداد آگهی در هر صفحه</param>
        /// <returns>لیست آگهی‌ها با اطلاعات صفحه‌بندی</returns>
        Task<(List<AdvertisementDto> advertisements, int totalCount, int totalPages)> GetAllAdvertisementsWithPaginationAsync(int pageNumber = 1, int pageSize = 10);

        /// <summary>
        /// دریافت آگهی‌های در انتظار تایید با صفحه‌بندی برای پنل ادمین
        /// </summary>
        /// <param name="pageNumber">شماره صفحه</param>
        /// <param name="pageSize">تعداد آگهی در هر صفحه</param>
        /// <returns>لیست آگهی‌های در انتظار تایید با اطلاعات صفحه‌بندی</returns>
        Task<(List<AdvertisementDto> advertisements, int totalCount, int totalPages)> GetPendingAdvertisementsWithPaginationAsync(int pageNumber = 1, int pageSize = 10);

        /// <summary>
        /// دریافت آگهی‌های تایید شده با صفحه‌بندی برای پنل ادمین
        /// </summary>
        /// <param name="pageNumber">شماره صفحه</param>
        /// <param name="pageSize">تعداد آگهی در هر صفحه</param>
        /// <returns>لیست آگهی‌های تایید شده با اطلاعات صفحه‌بندی</returns>
        Task<(List<AdvertisementDto> advertisements, int totalCount, int totalPages)> GetApprovedAdvertisementsWithPaginationAsync(int pageNumber = 1, int pageSize = 10);

        /// <summary>
        /// دریافت آگهی‌های حذف شده با صفحه‌بندی برای پنل ادمین
        /// </summary>
        /// <param name="pageNumber">شماره صفحه</param>
        /// <param name="pageSize">تعداد آگهی در هر صفحه</param>
        /// <returns>لیست آگهی‌های حذف شده با اطلاعات صفحه‌بندی</returns>
        Task<(List<AdvertisementDto> advertisements, int totalCount, int totalPages)> GetDeletedAdvertisementsWithPaginationAsync(int pageNumber = 1, int pageSize = 10);

        /// <summary>
        /// تایید آگهی
        /// </summary>
        /// <param name="advertisementId">شناسه آگهی</param>
        /// <param name="approvedByUserId">شناسه ادمین تایید کننده</param>
        /// <returns>موفقیت عملیات</returns>
        Task<bool> ApproveAdvertisementAsync(int advertisementId, string? approvedByUserId = null);

        /// <summary>
        /// رد آگهی
        /// </summary>
        /// <param name="advertisementId">شناسه آگهی</param>
        /// <param name="rejectedByUserId">شناسه ادمین رد کننده</param>
        /// <returns>موفقیت عملیات</returns>
        Task<bool> RejectAdvertisementAsync(int advertisementId, string? rejectedByUserId = null);

        /// <summary>
        /// ایجاد آگهی جدید
        /// </summary>
        /// <param name="advertisement">مدل آگهی</param>
        /// <returns>شناسه آگهی ایجاد شده</returns>
        Task<int> CreateAdvertisementAsync(Keleid.DAL.Models.Advertisement advertisement);

        /// <summary>
        /// اضافه کردن تصویر به آگهی
        /// </summary>
        /// <param name="image">مدل تصویر</param>
        /// <returns>نتیجه عملیات</returns>
        Task<bool> AddImageAsync(Keleid.DAL.Models.AdImage image);
    }
}
