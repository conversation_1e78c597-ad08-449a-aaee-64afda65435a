using Keleid.BLL.Services;
using Microsoft.AspNetCore.Http;

namespace Keleid.Web.Extensions
{
    public static class LoggingExtensions
    {
        private static string GetClientIpAddress(IHttpContextAccessor httpContextAccessor)
        {
            var httpContext = httpContextAccessor.HttpContext;
            if (httpContext == null) return "Unknown";

            // بررسی X-Forwarded-For header برای proxy ها
            var forwardedFor = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(forwardedFor))
            {
                return forwardedFor.Split(',')[0].Trim();
            }

            // بررسی X-Real-IP header
            var realIp = httpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(realIp))
            {
                return realIp;
            }

            // استفاده از RemoteIpAddress
            return httpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
        }
        public static async Task LogUserActionAsync(this LoggingService loggingService, string action, string userId, object? details = null)
        {
            await loggingService.LogInformationAsync(
                message: $"کاربر {userId} عملیات {action} را انجام داد",
                source: "UserAction",
                userId: userId,
                properties: details
            );
        }

        public static async Task LogAdvertisementActionAsync(this LoggingService loggingService, string action, int adId, string? userId = null, object? details = null)
        {
            await loggingService.LogInformationAsync(
                message: $"عملیات {action} روی آگهی {adId} انجام شد",
                source: "AdvertisementAction",
                userId: userId,
                properties: new { AdvertisementId = adId, Details = details }
            );
        }

        public static async Task LogAuthenticationAsync(this LoggingService loggingService, string action, string phoneNumber, bool success, string? reason = null)
        {
            var level = success ? "Information" : "Warning";
            var message = success 
                ? $"ورود موفق کاربر {phoneNumber}"
                : $"ورود ناموفق کاربر {phoneNumber}: {reason}";

            await loggingService.LogGeneralAsync(
                level: level,
                message: message,
                source: "Authentication",
                properties: new { PhoneNumber = phoneNumber, Success = success, Reason = reason }
            );
        }

        public static async Task LogSmsAsync(this LoggingService loggingService, string phoneNumber, string messageType, bool success, string? error = null)
        {
            var level = success ? "Information" : "Warning";
            var message = success 
                ? $"پیامک {messageType} به {phoneNumber} ارسال شد"
                : $"خطا در ارسال پیامک {messageType} به {phoneNumber}: {error}";

            await loggingService.LogGeneralAsync(
                level: level,
                message: message,
                source: "SmsService",
                properties: new { PhoneNumber = phoneNumber, MessageType = messageType, Success = success, Error = error }
            );
        }

        public static async Task LogFileOperationAsync(this LoggingService loggingService, string operation, string fileName, bool success, string? userId = null, string? error = null)
        {
            var level = success ? "Information" : "Error";
            var message = success 
                ? $"عملیات فایل {operation} برای {fileName} موفق بود"
                : $"خطا در عملیات فایل {operation} برای {fileName}: {error}";

            await loggingService.LogGeneralAsync(
                level: level,
                message: message,
                source: "FileService",
                userId: userId,
                properties: new { Operation = operation, FileName = fileName, Success = success, Error = error }
            );
        }

        public static async Task LogDatabaseOperationAsync(this LoggingService loggingService, string operation, string tableName, bool success, string? userId = null, object? details = null)
        {
            var level = success ? "Debug" : "Error";
            var message = success 
                ? $"عملیات دیتابیس {operation} روی جدول {tableName} موفق بود"
                : $"خطا در عملیات دیتابیس {operation} روی جدول {tableName}";

            await loggingService.LogGeneralAsync(
                level: level,
                message: message,
                source: "DatabaseService",
                userId: userId,
                properties: new { Operation = operation, TableName = tableName, Success = success, Details = details }
            );
        }

        public static async Task LogSecurityEventAsync(this LoggingService loggingService, string eventType, string description, string? userId = null, object? details = null)
        {
            await loggingService.LogWarningAsync(
                message: $"رویداد امنیتی: {eventType} - {description}",
                source: "SecurityService",
                userId: userId,
                properties: new { EventType = eventType, Description = description, Details = details }
            );
        }

        // لاگ‌های عمومی کاربران
        public static async Task LogUserLoginAsync(this LoggingService loggingService, string phoneNumber, bool success, string? reason = null, IHttpContextAccessor? httpContextAccessor = null)
        {
            var ipAddress = httpContextAccessor != null ? GetClientIpAddress(httpContextAccessor) : "Unknown";
            var level = success ? "Information" : "Warning";
            var message = success
                ? $"ورود موفق کاربر {phoneNumber}"
                : $"ورود ناموفق کاربر {phoneNumber}: {reason}";

            await loggingService.LogGeneralAsync(
                level: level,
                message: message,
                source: "Authentication",
                userId: phoneNumber,
                properties: new { PhoneNumber = phoneNumber, Success = success, Reason = reason, IpAddress = ipAddress },
                ipAddress: ipAddress
            );
        }

        public static async Task LogUserRegisterAsync(this LoggingService loggingService, string phoneNumber, bool success, string? reason = null, IHttpContextAccessor? httpContextAccessor = null)
        {
            var ipAddress = httpContextAccessor != null ? GetClientIpAddress(httpContextAccessor) : "Unknown";
            var level = success ? "Information" : "Warning";
            var message = success
                ? $"ثبت‌نام موفق کاربر {phoneNumber}"
                : $"ثبت‌نام ناموفق کاربر {phoneNumber}: {reason}";

            await loggingService.LogGeneralAsync(
                level: level,
                message: message,
                source: "Registration",
                userId: phoneNumber,
                properties: new { PhoneNumber = phoneNumber, Success = success, Reason = reason, IpAddress = ipAddress },
                ipAddress: ipAddress
            );
        }

        public static async Task LogAdvertisementCreateAsync(this LoggingService loggingService, int adId, string? userId = null, string? title = null, IHttpContextAccessor? httpContextAccessor = null)
        {
            var ipAddress = httpContextAccessor != null ? GetClientIpAddress(httpContextAccessor) : "Unknown";
            await loggingService.LogInformationAsync(
                message: $"آگهی جدید ثبت شد - شناسه: {adId}, عنوان: {title}",
                source: "Advertisement",
                userId: userId,
                properties: new { AdvertisementId = adId, Title = title, IpAddress = ipAddress }
            );
        }

        public static async Task LogAdvertisementDeleteAsync(this LoggingService loggingService, int adId, string? userId = null, string? title = null, IHttpContextAccessor? httpContextAccessor = null)
        {
            var ipAddress = httpContextAccessor != null ? GetClientIpAddress(httpContextAccessor) : "Unknown";
            await loggingService.LogWarningAsync(
                message: $"آگهی حذف شد - شناسه: {adId}, عنوان: {title}",
                source: "Advertisement",
                userId: userId,
                properties: new { AdvertisementId = adId, Title = title, IpAddress = ipAddress }
            );
        }

        public static async Task LogSearchAsync(this LoggingService loggingService, string searchTerm, int resultCount, string? userId = null, IHttpContextAccessor? httpContextAccessor = null)
        {
            var ipAddress = httpContextAccessor != null ? GetClientIpAddress(httpContextAccessor) : "Unknown";
            await loggingService.LogInformationAsync(
                message: $"جستجو انجام شد - کلمه: '{searchTerm}', نتایج: {resultCount}",
                source: "Search",
                userId: userId,
                properties: new { SearchTerm = searchTerm, ResultCount = resultCount, IpAddress = ipAddress }
            );
        }

        // لاگ‌های ادمین
        public static async Task LogNotificationReadAsync(this LoggingService loggingService, int notificationId, string adminUsername, IHttpContextAccessor? httpContextAccessor = null)
        {
            var ipAddress = httpContextAccessor != null ? GetClientIpAddress(httpContextAccessor) : "Unknown";
            await loggingService.LogInformationAsync(
                message: $"اعلان خوانده شد - شناسه: {notificationId}",
                source: "AdminPanel",
                userId: adminUsername,
                properties: new { NotificationId = notificationId, AdminUsername = adminUsername, IpAddress = ipAddress }
            );
        }

        public static async Task LogTaskCompleteAsync(this LoggingService loggingService, int taskId, string adminUsername, IHttpContextAccessor? httpContextAccessor = null)
        {
            var ipAddress = httpContextAccessor != null ? GetClientIpAddress(httpContextAccessor) : "Unknown";
            await loggingService.LogInformationAsync(
                message: $"وظیفه انجام شد - شناسه: {taskId}",
                source: "AdminPanel",
                userId: adminUsername,
                properties: new { TaskId = taskId, AdminUsername = adminUsername, IpAddress = ipAddress }
            );
        }

        public static async Task LogAdvertisementApproveAsync(this LoggingService loggingService, int adId, string adminUsername, string? adTitle = null, IHttpContextAccessor? httpContextAccessor = null)
        {
            var ipAddress = httpContextAccessor != null ? GetClientIpAddress(httpContextAccessor) : "Unknown";
            await loggingService.LogInformationAsync(
                message: $"آگهی تایید شد - شناسه: {adId}, عنوان: {adTitle}",
                source: "AdminPanel",
                userId: adminUsername,
                properties: new { AdvertisementId = adId, Title = adTitle, AdminUsername = adminUsername, IpAddress = ipAddress }
            );
        }

        public static async Task LogAdvertisementRejectAsync(this LoggingService loggingService, int adId, string adminUsername, string? adTitle = null, string? reason = null, IHttpContextAccessor? httpContextAccessor = null)
        {
            var ipAddress = httpContextAccessor != null ? GetClientIpAddress(httpContextAccessor) : "Unknown";
            await loggingService.LogWarningAsync(
                message: $"آگهی رد شد - شناسه: {adId}, عنوان: {adTitle}, دلیل: {reason}",
                source: "AdminPanel",
                userId: adminUsername,
                properties: new { AdvertisementId = adId, Title = adTitle, Reason = reason, AdminUsername = adminUsername, IpAddress = ipAddress }
            );
        }

        public static async Task LogCategoryCreateAsync(this LoggingService loggingService, int categoryId, string categoryTitle, string adminUsername, bool isSubCategory = false, IHttpContextAccessor? httpContextAccessor = null)
        {
            var ipAddress = httpContextAccessor != null ? GetClientIpAddress(httpContextAccessor) : "Unknown";
            var type = isSubCategory ? "زیردسته‌بندی" : "دسته‌بندی";
            await loggingService.LogInformationAsync(
                message: $"{type} جدید افزوده شد - شناسه: {categoryId}, عنوان: {categoryTitle}",
                source: "AdminPanel",
                userId: adminUsername,
                properties: new { CategoryId = categoryId, Title = categoryTitle, IsSubCategory = isSubCategory, AdminUsername = adminUsername, IpAddress = ipAddress }
            );
        }

        public static async Task LogCategoryUpdateAsync(this LoggingService loggingService, int categoryId, string oldTitle, string newTitle, string adminUsername, bool isSubCategory = false, IHttpContextAccessor? httpContextAccessor = null)
        {
            var ipAddress = httpContextAccessor != null ? GetClientIpAddress(httpContextAccessor) : "Unknown";
            var type = isSubCategory ? "زیردسته‌بندی" : "دسته‌بندی";
            await loggingService.LogInformationAsync(
                message: $"{type} ویرایش شد - شناسه: {categoryId}, عنوان قبلی: {oldTitle}, عنوان جدید: {newTitle}",
                source: "AdminPanel",
                userId: adminUsername,
                properties: new { CategoryId = categoryId, OldTitle = oldTitle, NewTitle = newTitle, IsSubCategory = isSubCategory, AdminUsername = adminUsername, IpAddress = ipAddress }
            );
        }

        public static async Task LogCategoryDeleteAsync(this LoggingService loggingService, int categoryId, string categoryTitle, string adminUsername, bool isSubCategory = false, IHttpContextAccessor? httpContextAccessor = null)
        {
            var ipAddress = httpContextAccessor != null ? GetClientIpAddress(httpContextAccessor) : "Unknown";
            var type = isSubCategory ? "زیردسته‌بندی" : "دسته‌بندی";
            await loggingService.LogWarningAsync(
                message: $"{type} حذف شد - شناسه: {categoryId}, عنوان: {categoryTitle}",
                source: "AdminPanel",
                userId: adminUsername,
                properties: new { CategoryId = categoryId, Title = categoryTitle, IsSubCategory = isSubCategory, AdminUsername = adminUsername, IpAddress = ipAddress }
            );
        }

        public static async Task LogCategoryFeaturesUpdateAsync(this LoggingService loggingService, int categoryId, string categoryTitle, string adminUsername, object? changes = null, IHttpContextAccessor? httpContextAccessor = null)
        {
            var ipAddress = httpContextAccessor != null ? GetClientIpAddress(httpContextAccessor) : "Unknown";
            await loggingService.LogInformationAsync(
                message: $"ویژگی‌های زیردسته‌بندی تغییر کرد - شناسه: {categoryId}, عنوان: {categoryTitle}",
                source: "AdminPanel",
                userId: adminUsername,
                properties: new { CategoryId = categoryId, Title = categoryTitle, Changes = changes, AdminUsername = adminUsername, IpAddress = ipAddress }
            );
        }

        public static async Task LogUserDeactivateAsync(this LoggingService loggingService, string targetUserId, string targetPhoneNumber, string adminUsername, IHttpContextAccessor? httpContextAccessor = null)
        {
            var ipAddress = httpContextAccessor != null ? GetClientIpAddress(httpContextAccessor) : "Unknown";
            await loggingService.LogWarningAsync(
                message: $"کاربر غیرفعال شد - شناسه: {targetUserId}, شماره: {targetPhoneNumber}",
                source: "AdminPanel",
                userId: adminUsername,
                properties: new { TargetUserId = targetUserId, TargetPhoneNumber = targetPhoneNumber, AdminUsername = adminUsername, IpAddress = ipAddress }
            );
        }

        public static async Task LogUserActivateAsync(this LoggingService loggingService, string targetUserId, string targetPhoneNumber, string adminUsername, IHttpContextAccessor? httpContextAccessor = null)
        {
            var ipAddress = httpContextAccessor != null ? GetClientIpAddress(httpContextAccessor) : "Unknown";
            await loggingService.LogInformationAsync(
                message: $"کاربر فعال شد - شناسه: {targetUserId}, شماره: {targetPhoneNumber}",
                source: "AdminPanel",
                userId: adminUsername,
                properties: new { TargetUserId = targetUserId, TargetPhoneNumber = targetPhoneNumber, AdminUsername = adminUsername, IpAddress = ipAddress }
            );
        }
    }
}
