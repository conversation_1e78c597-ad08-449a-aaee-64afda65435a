using System.Globalization;

namespace Keleid.Web.ViewModels
{
    public class UserIndexViewModel
    {
        public string PhoneNumber { get; set; } = string.Empty;
        public DateTime RegisterDate { get; set; }
        public bool IsActive { get; set; }
        public bool PhoneNumberConfirmed { get; set; }

        public string GetPersianRegisterDate()
        {
            var persianCalendar = new PersianCalendar();
            var year = persianCalendar.GetYear(RegisterDate);
            var month = persianCalendar.GetMonth(RegisterDate);
            var day = persianCalendar.GetDayOfMonth(RegisterDate);

            var monthNames = new[]
            {
                "فروردین", "اردیبهشت", "خرداد", "تیر", "مرداد", "شهریور",
                "مهر", "آبان", "آذر", "دی", "بهمن", "اسفند"
            };

            return $"{day} {monthNames[month - 1]} {year}";
        }

        public string GetAccountStatus()
        {
            if (!IsActive)
                return "غیرفعال";
            
            return PhoneNumberConfirmed ? "فعال" : "در انتظار تایید";
        }

        public string GetAccountStatusBadgeClass()
        {
            if (!IsActive)
                return "bg-danger-light text-danger";
            
            return PhoneNumberConfirmed ? "bg-success-light text-success" : "bg-warning-light text-warning";
        }

        public string GetPhoneStatusBadgeClass()
        {
            return PhoneNumberConfirmed ? "bg-success-light text-success" : "bg-warning-light text-warning";
        }

        public string GetPhoneStatusText()
        {
            return PhoneNumberConfirmed ? "تایید شده" : "تایید نشده";
        }
    }
}
