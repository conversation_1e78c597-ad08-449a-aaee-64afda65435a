﻿namespace Keleid.DAL.Models
{
    public class Advertisement
    {
        public int Id { get; set; }

        public string Title { get; set; }
        public string Slug { get; set; }
        public string Description { get; set; }
        public long Price { get; set; }
        public bool IsPriceless { get; set; }

        public bool IsDeleted { get; set; } = false;

        public int LocationId { get; set; }
        public Location Location { get; set; }

        public ContactInfo ContactInfo { get; set; }
        public ICollection<Favorite> Favorites { get; set; }

        public int CategoryId { get; set; }
        public Category Category { get; set; }

        public string UserId { get; set; }
        public ApplicationUser User { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public bool IsApproved { get; set; } = false;

        // Admin who approved the advertisement
        public string? ApprovedByUserId { get; set; }
        public ApplicationUser? ApprovedByUser { get; set; }

        public ICollection<AdvertisementFeature> Features { get; set; }
        public ICollection<AdImage> Images { get; set; }
    }

}
