﻿@model Keleid.BLL.DTOs.GeneralLogPageDto
@{
    ViewBag.Title = "لاگ‌های عمومی";
}

<!-- Left Sidebar -->
@await Html.PartialAsync("_LeftSidebar")

<!-- Main Content -->
<section class="content home">

    <!-- Breadcrumb -->
    @await Html.PartialAsync("_Breadcrumb")

    <div class="container-fluid">
        <!-- Basic Table -->
        <div class="row clearfix">
            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="card">
                    <div class="header">
                        <h2>لاگ‌های <strong>عمومی</strong></h2>
                        <ul class="header-dropdown m-r--5">
                            <li class="dropdown">
                                <span class="badge badge-info">@Model.TotalCount لاگ</span>
                            </li>
                        </ul>
                    </div>
                    <div class="body table-responsive">
                        @if (Model.GeneralLogs.Any())
                        {
                            <table class="table table-striped m-b-0">
                                <thead>
                                    <tr>
                                        <th>سطح</th>
                                        <th>پیام</th>
                                        <th>تاریخ</th>
                                        <th>جزئیات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var log in Model.GeneralLogs)
                                    {
                                        <tr>
                                            <td>
                                                <span class="badge @log.LevelClass">@log.LevelText</span>
                                            </td>
                                            <td>
                                                <span>@log.Message</span>
                                            </td>
                                            <td>
                                                <span>@log.RelativeTime</span>
                                                <br><small class="text-muted">@log.FormattedDate</small>
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(log.Properties))
                                                {
                                                    <button class="btn btn-sm btn-outline-info" type="button" data-toggle="collapse" data-target="#<EMAIL>()" aria-expanded="false">
                                                        <i class="zmdi zmdi-info"></i> جزئیات
                                                    </button>
                                                    <div class="collapse mt-2" id="<EMAIL>()">
                                                        <div class="card card-body">
                                                            <code>@log.Properties</code>
                                                        </div>
                                                    </div>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>

                            <!-- Pagination -->
                            @if (Model.TotalPages > 1)
                            {
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <div>
                                        <small class="text-muted">
                                            نمایش @((Model.CurrentPage - 1) * 20 + 1) تا @(Math.Min(Model.CurrentPage * 20, Model.TotalCount)) از @Model.TotalCount لاگ
                                        </small>
                                    </div>
                                    <nav>
                                        <ul class="pagination pagination-sm">
                                            @if (Model.HasPreviousPage)
                                            {
                                                <li class="page-item">
                                                    <a class="page-link" href="@Url.Action("Index", new { page = Model.CurrentPage - 1 })">قبلی</a>
                                                </li>
                                            }

                                            @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                                            {
                                                <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                                    <a class="page-link" href="@Url.Action("Index", new { page = i })">@i</a>
                                                </li>
                                            }

                                            @if (Model.HasNextPage)
                                            {
                                                <li class="page-item">
                                                    <a class="page-link" href="@Url.Action("Index", new { page = Model.CurrentPage + 1 })">بعدی</a>
                                                </li>
                                            }
                                        </ul>
                                    </nav>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="alert alert-info text-center">
                                <i class="zmdi zmdi-info"></i>
                                هیچ لاگی یافت نشد.
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

</section>