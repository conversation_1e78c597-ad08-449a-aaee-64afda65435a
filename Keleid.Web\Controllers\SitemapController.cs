using Keleid.BLL.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace Keleid.Web.Controllers
{
    public class SitemapController : Controller
    {
        private readonly ISitemapService _sitemapService;
        private readonly ILogger<SitemapController> _logger;

        public SitemapController(ISitemapService sitemapService, ILogger<SitemapController> logger)
        {
            _sitemapService = sitemapService;
            _logger = logger;
        }

        [HttpGet]
        [Route("sitemap.xml")]
        public async Task<IActionResult> Index()
        {
            try
            {
                // بررسی نیاز به به‌روزرسانی
                var needsUpdate = await _sitemapService.NeedsUpdateAsync();
                if (needsUpdate)
                {
                    _logger.LogInformation("Sitemap needs update, generating new sitemap");
                    await _sitemapService.RefreshSitemapAsync();
                }

                // دریافت محتوای sitemap
                var sitemapContent = await _sitemapService.GenerateSitemapXmlAsync();
                
                // تنظیم headers مناسب
                Response.ContentType = "application/xml; charset=utf-8";

                // تنظیم cache headers
                var lastModified = await _sitemapService.GetLastModifiedAsync();
                if (lastModified.HasValue)
                {
                    Response.Headers["Last-Modified"] = lastModified.Value.ToString("R");
                    Response.Headers["Cache-Control"] = "public, max-age=3600"; // 1 ساعت cache
                }

                return Content(sitemapContent, "application/xml", Encoding.UTF8);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error serving sitemap.xml");
                return StatusCode(500, "خطا در تولید sitemap");
            }
        }

        [HttpPost]
        [Route("admin/sitemap/refresh")]
        public async Task<IActionResult> RefreshSitemap()
        {
            try
            {
                // بررسی دسترسی ادمین
                if (!User.IsInRole("Admin"))
                {
                    return Forbid();
                }

                var result = await _sitemapService.RefreshSitemapAsync();
                
                if (result)
                {
                    _logger.LogInformation("Sitemap refreshed manually by admin {AdminName}", User.Identity?.Name);
                    return Json(new { success = true, message = "Sitemap با موفقیت به‌روزرسانی شد" });
                }
                else
                {
                    return Json(new { success = false, message = "خطا در به‌روزرسانی sitemap" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error manually refreshing sitemap");
                return Json(new { success = false, message = "خطا در به‌روزرسانی sitemap" });
            }
        }

        [HttpGet]
        [Route("admin/sitemap/status")]
        public async Task<IActionResult> GetSitemapStatus()
        {
            try
            {
                // بررسی دسترسی ادمین
                if (!User.IsInRole("Admin"))
                {
                    return Forbid();
                }

                var lastModified = await _sitemapService.GetLastModifiedAsync();
                var needsUpdate = await _sitemapService.NeedsUpdateAsync();

                return Json(new
                {
                    lastModified = lastModified?.ToString("yyyy-MM-dd HH:mm:ss"),
                    needsUpdate = needsUpdate,
                    exists = lastModified.HasValue
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sitemap status");
                return Json(new { error = "خطا در دریافت وضعیت sitemap" });
            }
        }
    }
}
