-- SQL Script برای ایجاد آگهی‌های نمونه در دیتابیس کلید

-- ابتدا چند Location اضافه می‌کنیم
INSERT INTO Locations (Province, City, Addresss) VALUES
('همدان', 'همدان', 'مرکز شهر'),
('همدان', 'همدان', 'کمال آباد'),
('همدان', 'همدان', 'شهرک فرهنگیان'),
('همدان', 'همدان', 'بلوار مدنی'),
('همدان', 'ملایر', 'مرکز شهر'),
('همدان', 'نهاوند', 'مرکز شهر'),
('همدان', 'تویسرکان', 'مرکز شهر'),
('همدان', 'اسدآباد', 'مرکز شهر');

-- حالا آگهی‌ها را اضافه می‌کنیم
-- فرض می‌کنیم کاربر ادمین با UserId موجود است

DECLARE @AdminUserId NVARCHAR(450);
SELECT @AdminUserId = Id FROM AspNetUsers WHERE Email = '<EMAIL>';

-- آگهی 1: جایگاه سوخت (5 دقیقه پیش - لحظاتی پیش)
INSERT INTO Advertisements (Title, Slug, Description, Price, IsPriceless, CategoryId, LocationId, UserId, CreatedAt, IsApproved, IsDeleted)
VALUES
('فروش جایگاه سوخت فعال در همدان',
'forosh-jaygah-sokht-faal-dar-hamedan',
'جایگاه سوخت فعال با مجوز کامل، دارای 4 نازل بنزین و 2 نازل گازوئیل، موقعیت عالی در خیابان اصلی',
15000000000, 0, 1, 1, @AdminUserId, DATEADD(MINUTE, -5, GETDATE()), 1, 0);

-- آگهی 2: زمین صنعتی (20 دقیقه پیش - یک ربع پیش)
INSERT INTO Advertisements (Title, Slug, Description, Price, IsPriceless, CategoryId, LocationId, UserId, CreatedAt, IsApproved, IsDeleted)
VALUES
('فروش زمین 5000 متری در شهرک صنعتی همدان',
'forosh-zamin-5000-metri-dar-shahrak-sanati-hamedan',
'زمین 5000 متری با کاربری صنعتی، دارای انشعابات آب، برق و گاز، مناسب برای احداث کارخانه',
8500000000, 0, 2, 1, @AdminUserId, DATEADD(MINUTE, -20, GETDATE()), 1, 0);

-- آگهی 3: سایت پرورش آبزیان (45 دقیقه پیش - نیم ساعت پیش)
INSERT INTO Advertisements (Title, Slug, Description, Price, IsPriceless, CategoryId, LocationId, UserId, CreatedAt, IsApproved, IsDeleted)
VALUES
('فروش مجموعه پرورش ماهی قزل آلا',
'forosh-majmooe-parvaresh-mahi-ghazal-ala',
'مجموعه کامل پرورش ماهی قزل آلا با 20 استخر، سیستم آب رسانی مدرن و تجهیزات کامل',
12000000000, 0, 3, 5, @AdminUserId, DATEADD(MINUTE, -45, GETDATE()), 1, 0);

-- آگهی 4: سردخانه (1.5 ساعت پیش - یک ساعت پیش)
INSERT INTO Advertisements (Title, Slug, Description, Price, IsPriceless, CategoryId, LocationId, UserId, CreatedAt, IsApproved, IsDeleted)
VALUES
('اجاره سردخانه 500 متری',
'ejareh-sardkhaneh-500-metri',
'سردخانه مدرن 500 متری با دمای زیر صفر، مناسب برای نگهداری محصولات لبنی و گوشتی',
0, 1, 4, 2, @AdminUserId, DATEADD(HOUR, -1.5, GETDATE()), 1, 0);

-- آگهی 5: سوله (3 ساعت پیش - امروز)
INSERT INTO Advertisements (Title, Slug, Description, Price, IsPriceless, CategoryId, LocationId, UserId, CreatedAt, IsApproved, IsDeleted)
VALUES
('فروش سوله 1000 متری نو',
'forosh-soleh-1000-metri-no',
'سوله نوساز 1000 متری با ارتفاع 8 متر، دارای سیستم تهویه و روشنایی مناسب',
4500000000, 0, 5, 3, @AdminUserId, DATEADD(HOUR, -3, GETDATE()), 1, 0);

-- آگهی 6: ماشین آلات (دیروز)
INSERT INTO Advertisements (Title, Slug, Description, Price, IsPriceless, CategoryId, LocationId, UserId, CreatedAt, IsApproved, IsDeleted)
VALUES
('فروش خط تولید نان صنعتی',
'forosh-khat-tolid-nan-sanati',
'خط تولید کامل نان صنعتی شامل میکسر، دستگاه شکل دهی، فر تونلی و بسته بندی',
2800000000, 0, 8, 4, @AdminUserId, DATEADD(DAY, -1, GETDATE()), 1, 0);

-- آگهی 7: مرکز درمانی (3 روز پیش - تاریخ شمسی)
INSERT INTO Advertisements (Title, Slug, Description, Price, IsPriceless, CategoryId, LocationId, UserId, CreatedAt, IsApproved, IsDeleted)
VALUES
('اجاره کلینیک تخصصی مجهز',
'ejareh-klinik-takhasosi-mojahaz',
'کلینیک 200 متری در بهترین نقطه شهر، دارای 5 اتاق ویزیت و تجهیزات پزشکی مدرن',
0, 1, 9, 1, @AdminUserId, DATEADD(DAY, -3, GETDATE()), 1, 0);

-- آگهی 8: نیروگاه خورشیدی (یک هفته پیش - تاریخ شمسی)
INSERT INTO Advertisements (Title, Slug, Description, Price, IsPriceless, CategoryId, LocationId, UserId, CreatedAt, IsApproved, IsDeleted)
VALUES
('فروش نیروگاه خورشیدی 100 کیلووات',
'forosh-niroogah-khorshidi-100-kilowatt',
'نیروگاه خورشیدی 100 کیلووات با پنل های مرغوب و اینورتر اروپایی، نصب شده و آماده بهره برداری',
3200000000, 0, 10, 6, @AdminUserId, DATEADD(DAY, -7, GETDATE()), 1, 0);

-- آگهی 9: هتل (دو هفته پیش - تاریخ شمسی)
INSERT INTO Advertisements (Title, Slug, Description, Price, IsPriceless, CategoryId, LocationId, UserId, CreatedAt, IsApproved, IsDeleted)
VALUES
('فروش هتل 3 ستاره در مرکز شهر',
'forosh-hotel-3-setareh-dar-markaz-shahr',
'هتل 3 ستاره با 25 اتاق، رستوران، سالن کنفرانس و پارکینگ، موقعیت عالی برای گردشگری',
18000000000, 0, 11, 7, @AdminUserId, DATEADD(DAY, -14, GETDATE()), 1, 0);

-- آگهی 10: مرغداری (یک ماه پیش - تاریخ شمسی)
INSERT INTO Advertisements (Title, Slug, Description, Price, IsPriceless, CategoryId, LocationId, UserId, CreatedAt, IsApproved, IsDeleted)
VALUES
('فروش مرغداری 10000 قطعه ای',
'forosh-morghdari-10000-gheteei',
'مرغداری مدرن 10000 قطعه ای با سیستم تهویه اتوماتیک، آبخوری نیپل و تجهیزات کامل',
6800000000, 0, 15, 8, @AdminUserId, DATEADD(DAY, -30, GETDATE()), 1, 0);

-- اضافه کردن ContactInfo برای آگهی‌ها
INSERT INTO ContactInfos (Name, PhoneNumber, Email, AdId)
SELECT 
    'مدیر فروش کلید',
    '09359313137',
    '<EMAIL>',
    Id
FROM Advertisements 
WHERE UserId = @AdminUserId;

-- اضافه کردن تصاویر نمونه برای آگهی‌ها
INSERT INTO AdImages (AdvertisementId, ImageUrl, IsMain)
SELECT 
    Id,
    '/assets/img/placeholder.png',
    1
FROM Advertisements 
WHERE UserId = @AdminUserId;

-- اضافه کردن ویژگی‌ها برای دسته‌بندی‌ها (CategoryFeatures)

-- ویژگی‌های جایگاه سوخت (CategoryId = 1)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(1, 'تعداد نازل بنزین', 'number', 1),
(1, 'تعداد نازل گازوئیل', 'number', 1),
(1, 'متراژ زمین', 'number', 1),
(1, 'نوع مالکیت', 'select', 1),
(1, 'وضعیت مجوز', 'select', 1),
(1, 'امکانات جانبی', 'text', 0);

-- ویژگی‌های زمین با کاربری صنعتی (CategoryId = 2)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(2, 'متراژ زمین', 'number', 1),
(2, 'نوع کاربری', 'select', 1),
(2, 'وضعیت انشعابات', 'text', 1),
(2, 'عرض بر', 'number', 0),
(2, 'نوع مالکیت', 'select', 1),
(2, 'موقعیت جغرافیایی', 'text', 0);

-- ویژگی‌های کارخانه صنعتی و تولیدی (CategoryId = 6)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(6, 'متراژ زیربنا', 'number', 1),
(6, 'متراژ زمین', 'number', 1),
(6, 'نوع صنعت', 'text', 1),
(6, 'تعداد کارگر', 'number', 0),
(6, 'ظرفیت تولید', 'text', 0),
(6, 'وضعیت مجوزها', 'text', 1),
(6, 'تجهیزات موجود', 'text', 0);

-- ویژگی‌های واحد دامداری (CategoryId = 13)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(13, 'نوع دام', 'select', 1),
(13, 'ظرفیت نگهداری', 'number', 1),
(13, 'متراژ زمین', 'number', 1),
(13, 'متراژ سالن', 'number', 1),
(13, 'تجهیزات موجود', 'text', 0),
(13, 'منابع آب', 'text', 1),
(13, 'دسترسی به علوفه', 'text', 0);

-- ویژگی‌های واحد مرغداری (CategoryId = 15)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(15, 'ظرفیت پرورش', 'number', 1),
(15, 'تعداد سالن', 'number', 1),
(15, 'متراژ هر سالن', 'number', 1),
(15, 'سیستم تهویه', 'select', 1),
(15, 'سیستم آبخوری', 'select', 1),
(15, 'سیستم غذادهی', 'select', 1),
(15, 'تجهیزات کنترل دما', 'text', 0);

-- ویژگی‌های سردخانه، انبار، سیلو (CategoryId = 4)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(4, 'نوع انبار', 'select', 1),
(4, 'ظرفیت ذخیره‌سازی', 'number', 1),
(4, 'متراژ زیربنا', 'number', 1),
(4, 'ارتفاع سقف', 'number', 0),
(4, 'سیستم سرمایش', 'select', 0),
(4, 'دمای نگهداری', 'text', 0),
(4, 'تجهیزات بارگیری', 'text', 0);

-- نمایش نتایج
SELECT
    a.Id,
    a.Title,
    a.Price,
    a.IsPriceless,
    c.Title as CategoryTitle,
    l.Province + ', ' + l.City as Location,
    a.CreatedAt
FROM Advertisements a
INNER JOIN Categories c ON a.CategoryId = c.Id
INNER JOIN Locations l ON a.LocationId = l.Id
WHERE a.UserId = @AdminUserId
ORDER BY a.CreatedAt DESC;

-- نمایش ویژگی‌های اضافه شده
SELECT
    c.Title as CategoryTitle,
    cf.Title as FeatureTitle,
    cf.InputType,
    cf.IsRequired
FROM CategoryFeatures cf
INNER JOIN Categories c ON cf.CategoryId = c.Id
ORDER BY c.Id, cf.Id;
