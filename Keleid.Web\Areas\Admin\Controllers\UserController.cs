﻿using Keleid.BLL.DTOs;
using Keleid.BLL.Services;
using Keleid.Web.Extensions;
using Microsoft.AspNetCore.Mvc;

namespace Keleid.Web.Areas.Admin.Controllers
{
    public class UserController : BaseAdminController
    {
        private readonly UserService _userService;
        private readonly LoggingService _loggingService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public UserController(UserService userService, LoggingService loggingService, IHttpContextAccessor httpContextAccessor)
        {
            _userService = userService;
            _loggingService = loggingService;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<IActionResult> Index(int page = 1)
        {
            const int pageSize = 10;
            var (users, totalCount, totalPages) = await _userService.GetUsersWithPaginationAsync(page, pageSize);

            var model = new UserPageDto
            {
                Users = users,
                CurrentPage = page,
                TotalPages = totalPages,
                TotalCount = totalCount
            };

            return View(model);
        }

        public async Task<IActionResult> Details(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                TempData["Error"] = "شناسه کاربر مشخص نشده است.";
                return RedirectToAction("Index");
            }

            var userDetail = await _userService.GetUserDetailAsync(id);
            if (userDetail == null)
            {
                TempData["Error"] = "کاربر یافت نشد.";
                return RedirectToAction("Index");
            }

            return View(userDetail);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ToggleStatus(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                TempData["Error"] = "شناسه کاربر مشخص نشده است.";
                return RedirectToAction("Index");
            }

            // دریافت اطلاعات کاربر قبل از تغییر وضعیت
            var userDetail = await _userService.GetUserDetailAsync(id);
            if (userDetail == null)
            {
                TempData["Error"] = "کاربر یافت نشد.";
                return RedirectToAction("Index");
            }

            var wasActive = userDetail.IsActive;
            var adminUsername = User.Identity?.Name ?? "Unknown";

            var result = await _userService.ToggleUserStatusAsync(id);
            if (result)
            {
                // لاگ کردن تغییر وضعیت کاربر
                if (wasActive)
                {
                    // کاربر غیرفعال شد
                    await _loggingService.LogUserDeactivateAsync(id, userDetail.PhoneNumber, adminUsername, _httpContextAccessor);
                }
                else
                {
                    // کاربر فعال شد
                    await _loggingService.LogUserActivateAsync(id, userDetail.PhoneNumber, adminUsername, _httpContextAccessor);
                }

                TempData["Success"] = "وضعیت کاربر با موفقیت تغییر یافت.";
            }
            else
            {
                TempData["Error"] = "خطا در تغییر وضعیت کاربر.";
            }

            return RedirectToAction("Index");
        }
    }
}
