using Keleid.DAL.Models;

namespace Keleid.BLL.Services.Interfaces
{
    public interface ISmsService
    {
        /// <summary>
        /// ارسال کد تایید به شماره تلفن
        /// </summary>
        /// <param name="phoneNumber">شماره تلفن</param>
        /// <param name="ipAddress">آدرس IP درخواست‌کننده</param>
        /// <returns>موفقیت ارسال</returns>
        Task<bool> SendVerificationCodeAsync(string phoneNumber, string ipAddress);

        /// <summary>
        /// تایید کد ارسال شده
        /// </summary>
        /// <param name="phoneNumber">شماره تلفن</param>
        /// <param name="code">کد تایید</param>
        /// <returns>صحت کد</returns>
        Task<bool> VerifyCodeAsync(string phoneNumber, string code);

        /// <summary>
        /// تولید کد تایید تصادفی
        /// </summary>
        /// <returns>کد 5 رقمی</returns>
        string GenerateVerificationCode();

        /// <summary>
        /// ذخیره پیامک در دیتابیس
        /// </summary>
        /// <param name="phoneNumber">شماره تلفن</param>
        /// <param name="message">متن پیامک</param>
        /// <returns>موفقیت ذخیره</returns>
        Task<bool> SaveSmsAsync(string phoneNumber, string message);

        /// <summary>
        /// دریافت آخرین پیامک ارسال شده به شماره
        /// </summary>
        /// <param name="phoneNumber">شماره تلفن</param>
        /// <returns>آخرین پیامک</returns>
        Task<Sms?> GetLatestSmsAsync(string phoneNumber);

        /// <summary>
        /// بررسی اینکه آیا می‌توان کد جدید ارسال کرد یا نه
        /// </summary>
        /// <param name="phoneNumber">شماره تلفن</param>
        /// <returns>امکان ارسال کد جدید</returns>
        Task<bool> CanSendNewCodeAsync(string phoneNumber);

        /// <summary>
        /// بررسی محدودیت IP برای ارسال پیامک
        /// </summary>
        /// <param name="ipAddress">آدرس IP</param>
        /// <returns>امکان ارسال از این IP</returns>
        Task<bool> CanSendFromIpAsync(string ipAddress);

        /// <summary>
        /// دریافت تعداد پیامک‌های ارسال شده از یک IP در ساعت گذشته
        /// </summary>
        /// <param name="ipAddress">آدرس IP</param>
        /// <returns>تعداد پیامک‌ها</returns>
        Task<int> GetSmsCountFromIpInLastHourAsync(string ipAddress);

        /// <summary>
        /// دریافت اعتبار باقی‌مانده پنل پیامک
        /// </summary>
        /// <returns>اعتبار به ریال</returns>
        Task<long> GetSmsBalanceAsync();
    }
}
