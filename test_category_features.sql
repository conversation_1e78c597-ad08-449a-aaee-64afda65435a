-- اسکریپت تست برای بررسی ویژگی‌های زیردسته‌بندی‌ها
-- این اسکریپت فقط چند زیردسته‌بندی را تست می‌کند

-- حذف ویژگی‌های قبلی برای تست
DELETE FROM CategoryFeatures WHERE CategoryId IN (230, 231, 271, 272);

-- تست ویژگی‌های جایگاه C.N.G (CategoryId = 230)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(230, N'تعداد نازل CNG', 'number', 1),
(230, N'ظرفیت مخزن (متر مکعب)', 'number', 1),
(230, N'متراژ زمین (متر مربع)', 'number', 1),
(230, N'نوع مالکیت', 'select', 1),
(230, N'وضعیت مجوز', 'select', 1);

-- تست ویژگی‌های جایگاه سوخت بنزین و گازوئیل (CategoryId = 231)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(231, N'تعداد نازل بنزین', 'number', 1),
(231, N'تعداد نازل گازوئیل', 'number', 1),
(231, N'متراژ زمین (متر مربع)', 'number', 1),
(231, N'ظرفیت مخازن (لیتر)', 'number', 1),
(231, N'نوع مالکیت', 'select', 1);

-- تست ویژگی‌های مرغداری گوشتی (CategoryId = 271)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(271, N'ظرفیت پرورش (قطعه)', 'number', 1),
(271, N'تعداد سالن', 'number', 1),
(271, N'متراژ هر سالن (متر مربع)', 'number', 1),
(271, N'سیستم تهویه', 'select', 1),
(271, N'سیستم آبخوری', 'select', 1);

-- تست ویژگی‌های مرغداری مرغ تخمگذار (CategoryId = 272)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(272, N'ظرفیت پرورش (قطعه)', 'number', 1),
(272, N'تعداد سالن', 'number', 1),
(272, N'متراژ هر سالن (متر مربع)', 'number', 1),
(272, N'سیستم تهویه', 'select', 1),
(272, N'ظرفیت تولید تخم روزانه', 'number', 0);

-- نمایش نتایج تست
PRINT N'ویژگی‌های تست با موفقیت اضافه شدند.';

SELECT 
    c.Id as N'شناسه',
    c.Title as N'زیردسته‌بندی',
    COUNT(cf.Id) as N'تعداد ویژگی'
FROM Categories c
LEFT JOIN CategoryFeatures cf ON c.Id = cf.CategoryId
WHERE c.Id IN (230, 231, 271, 272)
GROUP BY c.Id, c.Title
ORDER BY c.Id;

-- نمایش جزئیات
SELECT 
    c.Id as N'شناسه',
    c.Title as N'زیردسته‌بندی',
    cf.Title as N'عنوان ویژگی',
    cf.InputType as N'نوع ورودی',
    CASE WHEN cf.IsRequired = 1 THEN N'اجباری' ELSE N'اختیاری' END as N'وضعیت'
FROM CategoryFeatures cf
INNER JOIN Categories c ON cf.CategoryId = c.Id
WHERE c.Id IN (230, 231, 271, 272)
ORDER BY c.Id, cf.Id;
