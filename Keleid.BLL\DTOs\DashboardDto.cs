namespace Keleid.BLL.DTOs
{
    public class DashboardDto
    {
        public DashboardStatsDto Stats { get; set; } = new();
        public List<DashboardAdvertisementDto> LatestAdvertisements { get; set; } = new();
    }

    public class DashboardStatsDto
    {
        public int TotalUsers { get; set; }
        public int PendingAdvertisements { get; set; }
        public int ApprovedAdvertisements { get; set; }
        public int UnreadNotifications { get; set; }
        public int PendingTasks { get; set; }
        public int TodayRegistrations { get; set; }
        public int TodayAdvertisements { get; set; }
        public long SmsBalance { get; set; }
        public string FormattedSmsBalance => SmsBalance.ToString("N0") + " ریال";
    }

    public class DashboardAdvertisementDto
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string UserPhone { get; set; }
        public string CategoryTitle { get; set; }
        public bool IsApproved { get; set; }
        public bool IsDeleted { get; set; }
        public DateTime CreatedAt { get; set; }
        public string RelativeTime { get; set; }
        public string StatusText { get; set; }
        public string StatusClass { get; set; }
    }
}
