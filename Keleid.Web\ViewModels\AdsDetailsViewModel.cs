using Keleid.BLL.DTOs;
using System.ComponentModel.DataAnnotations;

namespace Keleid.Web.ViewModels
{
    public class AdsDetailsViewModel
    {
        public int CategoryId { get; set; }
        public string CategoryTitle { get; set; } = "";
        public List<CategoryFeatureDto> CategoryFeatures { get; set; } = new List<CategoryFeatureDto>();

        // فیلدهای عمومی آگهی
        [Required(ErrorMessage = "عنوان آگهی الزامی است")]
        [MinLength(10, ErrorMessage = "عنوان باید حداقل 10 کاراکتر باشد")]
        [MaxLength(200, ErrorMessage = "عنوان نمی‌تواند بیش از 200 کاراکتر باشد")]
        public string Title { get; set; } = "";

        [Required(ErrorMessage = "توضیحات آگهی الزامی است")]
        [MinLength(30, ErrorMessage = "توضیحات باید حداقل 30 کاراکتر باشد")]
        [MaxLength(2000, ErrorMessage = "توضیحات نمی‌تواند بیش از 2000 کاراکتر باشد")]
        public string Description { get; set; } = "";

        public long? Price { get; set; }

        public bool IsPriceless { get; set; }

        // ویژگی‌های اختصاصی - Dictionary برای ذخیره مقادیر
        public Dictionary<int, string> FeatureValues { get; set; } = new Dictionary<int, string>();
    }
}
