using Keleid.BLL.DTOs;
using Keleid.DAL;
using Keleid.DAL.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Keleid.BLL.Services
{
    public class UserService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<UserService> _logger;

        public UserService(ApplicationDbContext context, ILogger<UserService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<(List<UserDto> users, int totalCount, int totalPages)> GetUsersWithPaginationAsync(int pageNumber = 1, int pageSize = 10)
        {
            try
            {
                // فیلتر کردن کاربران ادمین - فقط کاربران عادی نمایش داده شوند
                var adminRoleId = await _context.Roles
                    .Where(r => r.Name == "Admin")
                    .Select(r => r.Id)
                    .FirstOrDefaultAsync();

                var adminUserIds = new List<string>();
                if (!string.IsNullOrEmpty(adminRoleId))
                {
                    adminUserIds = await _context.UserRoles
                        .Where(ur => ur.RoleId == adminRoleId)
                        .Select(ur => ur.UserId)
                        .ToListAsync();
                }

                var query = _context.Users
                    .Where(u => !adminUserIds.Contains(u.Id)); // حذف کاربران ادمین

                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                var users = await query
                    .OrderByDescending(u => u.RegisterDate)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var userDtos = new List<UserDto>();

                foreach (var user in users)
                {
                    var advertisementCount = await _context.Advertisements.CountAsync(a => a.UserId == user.Id);
                    var approvedCount = await _context.Advertisements.CountAsync(a => a.UserId == user.Id && a.IsApproved && !a.IsDeleted);
                    var pendingCount = await _context.Advertisements.CountAsync(a => a.UserId == user.Id && !a.IsApproved && !a.IsDeleted);
                    var favoriteCount = await _context.Favorites.CountAsync(f => f.UserId == user.Id);

                    userDtos.Add(MapToDto(user, advertisementCount, approvedCount, pendingCount, favoriteCount));
                }

                return (userDtos, totalCount, totalPages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users with pagination");
                return (new List<UserDto>(), 0, 0);
            }
        }

        public async Task<UserDetailDto?> GetUserDetailAsync(string userId)
        {
            try
            {
                var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
                if (user == null)
                {
                    return null;
                }

                // Statistics
                var totalAds = await _context.Advertisements.CountAsync(a => a.UserId == userId);
                var approvedAds = await _context.Advertisements.CountAsync(a => a.UserId == userId && a.IsApproved && !a.IsDeleted);
                var pendingAds = await _context.Advertisements.CountAsync(a => a.UserId == userId && !a.IsApproved && !a.IsDeleted);
                var rejectedAds = await _context.Advertisements.CountAsync(a => a.UserId == userId && a.IsDeleted);
                var totalFavorites = await _context.Favorites.CountAsync(f => f.UserId == userId);

                // Recent advertisements (last 5)
                var recentAds = await _context.Advertisements
                    .Where(a => a.UserId == userId)
                    .Include(a => a.Category)
                    .Include(a => a.Location)
                    .Include(a => a.ContactInfo)
                    .Include(a => a.Images)
                    .OrderByDescending(a => a.CreatedAt)
                    .Take(5)
                    .ToListAsync();

                // Recent favorites (last 5)
                var recentFavorites = await _context.Favorites
                    .Where(f => f.UserId == userId)
                    .Include(f => f.Advertisement)
                        .ThenInclude(a => a.Category)
                    .Include(f => f.Advertisement)
                        .ThenInclude(a => a.Location)
                    .Include(f => f.Advertisement)
                        .ThenInclude(a => a.Images)
                    .OrderByDescending(f => f.CreatedAt)
                    .Take(5)
                    .ToListAsync();

                return MapToDetailDto(user, totalAds, approvedAds, pendingAds, rejectedAds, totalFavorites, recentAds, recentFavorites);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user detail for user {UserId}", userId);
                return null;
            }
        }

        public async Task<bool> ToggleUserStatusAsync(string userId)
        {
            try
            {
                var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
                if (user == null)
                {
                    return false;
                }

                user.IsActive = !user.IsActive;
                await _context.SaveChangesAsync();

                _logger.LogInformation("User {UserId} status changed to {Status}", userId, user.IsActive ? "Active" : "Inactive");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling user status for user {UserId}", userId);
                return false;
            }
        }

        private UserDto MapToDto(ApplicationUser user, int adCount, int approvedCount, int pendingCount, int favoriteCount)
        {
            return new UserDto
            {
                Id = user.Id,
                PhoneNumber = user.PhoneNumber ?? "نامشخص",
                Email = user.Email,
                RegisterDate = user.RegisterDate,
                IsActive = user.IsActive,
                PhoneNumberConfirmed = user.PhoneNumberConfirmed,
                AdvertisementCount = adCount,
                ApprovedAdvertisementCount = approvedCount,
                PendingAdvertisementCount = pendingCount,
                FavoriteCount = favoriteCount,
                RelativeRegisterTime = GetRelativeTime(user.RegisterDate)
            };
        }

        private UserDetailDto MapToDetailDto(ApplicationUser user, int totalAds, int approvedAds, int pendingAds, int rejectedAds, int totalFavorites, List<Advertisement> recentAds, List<Favorite> recentFavorites)
        {
            return new UserDetailDto
            {
                Id = user.Id,
                PhoneNumber = user.PhoneNumber ?? "نامشخص",
                Email = user.Email,
                RegisterDate = user.RegisterDate,
                IsActive = user.IsActive,
                PhoneNumberConfirmed = user.PhoneNumberConfirmed,
                RelativeRegisterTime = GetRelativeTime(user.RegisterDate),
                TotalAdvertisements = totalAds,
                ApprovedAdvertisements = approvedAds,
                PendingAdvertisements = pendingAds,
                RejectedAdvertisements = rejectedAds,
                TotalFavorites = totalFavorites,
                RecentAdvertisements = recentAds.Select(MapAdvertisementToDto).ToList(),
                RecentFavorites = recentFavorites.Select(MapFavoriteToDto).ToList()
            };
        }

        private AdvertisementDto MapAdvertisementToDto(Advertisement ad)
        {
            return new AdvertisementDto
            {
                Id = ad.Id,
                Title = ad.Title,
                Price = ad.Price,
                IsPriceless = ad.IsPriceless,
                CategoryTitle = ad.Category?.Title ?? "نامشخص",
                Province = ad.Location?.Province ?? "نامشخص",
                City = ad.Location?.City ?? "نامشخص",
                IsApproved = ad.IsApproved,
                IsDeleted = ad.IsDeleted,
                CreatedAt = ad.CreatedAt,
                MainImageUrl = ad.Images?.FirstOrDefault()?.ImageUrl ?? "/assets/img/placeholder.png"
            };
        }

        private FavoriteDto MapFavoriteToDto(Favorite favorite)
        {
            return new FavoriteDto
            {
                Id = favorite.Id,
                UserId = favorite.UserId,
                AdId = favorite.AdId,
                CreatedAt = favorite.CreatedAt,
                Advertisement = MapAdvertisementToDto(favorite.Advertisement)
            };
        }

        private string GetRelativeTime(DateTime dateTime)
        {
            var timeSpan = DateTime.UtcNow - dateTime;

            if (timeSpan.TotalMinutes < 15)
                return "لحظاتی پیش";
            if (timeSpan.TotalMinutes < 30)
                return "ربع ساعت پیش";
            if (timeSpan.TotalMinutes < 60)
                return "نیم ساعت پیش";
            if (timeSpan.TotalHours < 2)
                return "یک ساعت پیش";
            if (timeSpan.TotalHours < 24)
                return "امروز";
            if (timeSpan.TotalDays < 2)
                return "دیروز";

            return dateTime.ToString("dd MMMM yyyy", new System.Globalization.CultureInfo("fa-IR"));
        }
    }
}
