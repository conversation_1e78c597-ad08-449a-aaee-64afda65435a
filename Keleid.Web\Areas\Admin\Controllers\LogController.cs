﻿using Keleid.BLL.DTOs;
using Keleid.BLL.Services;
using Microsoft.AspNetCore.Mvc;

namespace Keleid.Web.Areas.Admin.Controllers
{
    public class LogController : BaseAdminController
    {
        private readonly LogService _logService;

        public LogController(LogService logService)
        {
            _logService = logService;
        }

        public async Task<IActionResult> Index(int page = 1)
        {
            const int pageSize = 20;
            var (generalLogs, totalCount, totalPages) = await _logService.GetGeneralLogsAsync(page, pageSize);

            var model = new GeneralLogPageDto
            {
                GeneralLogs = generalLogs,
                CurrentPage = page,
                TotalPages = totalPages,
                TotalCount = totalCount
            };

            return View(model);
        }

        public async Task<IActionResult> Sms(int page = 1)
        {
            const int pageSize = 20;
            var (smsLogs, totalCount, totalPages) = await _logService.GetSmsLogsWithPaginationAsync(page, pageSize);

            var model = new SmsLogPageDto
            {
                SmsLogs = smsLogs,
                CurrentPage = page,
                TotalPages = totalPages,
                TotalCount = totalCount
            };

            return View(model);
        }

        public async Task<IActionResult> Errors(int page = 1)
        {
            const int pageSize = 20;
            var (errorLogs, totalCount, totalPages) = await _logService.GetErrorLogsAsync(page, pageSize);

            var model = new ErrorLogPageDto
            {
                ErrorLogs = errorLogs,
                CurrentPage = page,
                TotalPages = totalPages,
                TotalCount = totalCount
            };

            return View(model);
        }
    }
}
