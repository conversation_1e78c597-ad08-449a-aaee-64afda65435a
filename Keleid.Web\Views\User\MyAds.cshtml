﻿@model Keleid.Web.ViewModels.MyAdsViewModel

@Html.AntiForgeryToken()

<!-- Main Content -->
<div class="container py-4">
    <div class="row">
        <!-- Sidebar -->
        @await Component.InvokeAsync("UserSidebar")

        <!-- Main Content Area -->
        <div class="col-lg-9">
            <div class="bg-white rounded-3 shadow-sm p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="mb-0">آگهی‌های من (@Model.TotalCount آگهی)</h5>
                    <a asp-controller="Ads" asp-action="Index" class="btn btn-danger">
                        <i class="fas fa-plus"></i>
                        ثبت آگهی جدید
                    </a>
                </div>

                <!-- آگهی‌ها -->
                <div class="my-ads-list">
                    @if (Model.Advertisements.Any())
                    {
                        @foreach (var ad in Model.Advertisements)
                        {
                            <div class="ad-item">
                                <div class="ad-row">
                                    <div class="ad-image">
                                        <img src="@ad.MainImageUrl" class="img-fluid rounded @Model.GetAdvertisementImageClass(ad)" alt="تصویر آگهی">
                                    </div>
                                    <div class="ad-content d-flex flex-column justify-content-between">
                                        <!-- عنوان در بالا -->
                                        <div>
                                            <div class="d-flex justify-content-between align-items-start mb-2 gap-2">
                                                <h5 class="mb-0 fw-bold text-dark">@ad.Title</h5>
                                                @if (!ad.IsDeleted)
                                                {
                                                    <div class="dropdown">
                                                        <button class="btn btn-link text-muted p-0" type="button" data-bs-toggle="dropdown">
                                                            <i class="fas fa-ellipsis-v"></i>
                                                        </button>
                                                        <ul class="dropdown-menu">
                                                            <!-- <li><a class="dropdown-item" href="#"><i class="fas fa-edit me-2"></i>ویرایش</a></li> -->
                                                            <li><a class="dropdown-item text-danger" href="#" data-bs-toggle="modal" data-bs-target="#deleteModal" data-ad-id="@ad.Id" data-ad-title="@ad.Title"><i class="fas fa-trash-alt me-2"></i>حذف</a></li>
                                                        </ul>
                                                    </div>
                                                }
                                            </div>
                                        </div>

                                        <!-- بقیه اطلاعات در پایین -->
                                        <div>
                                            <p class="text-muted mb-2">@ad.FormattedPrice</p>
                                            <div class="d-flex align-items-center justify-content-between text-muted small mb-0">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-location-dot ms-1"></i>
                                                    @ad.LocationDisplay
                                                </div>
                                                <span class="badge @Model.GetAdvertisementStatusBadgeClass(ad)">@Model.GetAdvertisementStatus(ad)</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">هنوز آگهی‌ای ثبت نکرده‌اید</h6>
                            <p class="text-muted">برای شروع، اولین آگهی خود را ثبت کنید</p>
                            <a asp-controller="Ads" asp-action="Index" class="btn btn-danger">
                                <i class="fas fa-plus me-2"></i>
                                ثبت آگهی جدید
                            </a>
                        </div>
                    }


                <!-- Pagination -->
                @if (Model.TotalPages > 1)
                {
                    <nav aria-label="صفحه‌بندی آگهی‌ها" class="mt-4">
                        <ul class="pagination justify-content-center custom-pagination">
                            <!-- Previous Page -->
                            <li class="page-item @(Model.HasPreviousPage ? "" : "disabled")">
                                <a class="page-link" href="@(Model.HasPreviousPage ? Url.Action("MyAds", new { page = Model.PreviousPage }) : "#")" tabindex="@(Model.HasPreviousPage ? "" : "-1")" aria-disabled="@(Model.HasPreviousPage ? "false" : "true")">
                                    <i class="fas fa-chevron-right"></i>
                                    <span class="d-none d-sm-inline ms-1">قبلی</span>
                                </a>
                            </li>

                            <!-- First Page -->
                            @if (Model.ShowStartEllipsis)
                            {
                                <li class="page-item">
                                    <a class="page-link" href="@Url.Action("MyAds", new { page = 1 })">1</a>
                                </li>
                                <li class="page-item">
                                    <span class="page-link">...</span>
                                </li>
                            }

                            <!-- Page Range -->
                            @foreach (var pageNum in Model.GetPageRange())
                            {
                                @if (pageNum == Model.CurrentPage)
                                {
                                    <li class="page-item active" aria-current="page">
                                        <a class="page-link" href="@Url.Action("MyAds", new { page = pageNum })">@pageNum</a>
                                    </li>
                                }
                                else
                                {
                                    <li class="page-item">
                                        <a class="page-link" href="@Url.Action("MyAds", new { page = pageNum })">@pageNum</a>
                                    </li>
                                }
                            }

                            <!-- Last Page -->
                            @if (Model.ShowEndEllipsis)
                            {
                                <li class="page-item">
                                    <span class="page-link">...</span>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="@Url.Action("MyAds", new { page = Model.TotalPages })">@Model.TotalPages</a>
                                </li>
                            }

                            <!-- Next Page -->
                            <li class="page-item @(Model.HasNextPage ? "" : "disabled")">
                                <a class="page-link" href="@(Model.HasNextPage ? Url.Action("MyAds", new { page = Model.NextPage }) : "#")" tabindex="@(Model.HasNextPage ? "" : "-1")" aria-disabled="@(Model.HasNextPage ? "false" : "true")">
                                    <span class="d-none d-sm-inline me-1">بعدی</span>
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                }
            </div>
        </div>
    </div>
</div>
</div>
<!-- Modal حذف آگهی -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0">
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center py-4">
                <div class="mb-4">
                    <i class="fas fa-exclamation-triangle text-warning fa-3x"></i>
                </div>
                <h5 class="mb-3">آیا از حذف این آگهی مطمئن هستید؟</h5>
                <p class="text-muted mb-2"><strong id="adTitleToDelete"></strong></p>
                <p class="text-muted">با حذف آگهی، امکان بازگردانی آن وجود نخواهد داشت.</p>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">انصراف</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <span class="btn-text">حذف آگهی</span>
                    <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
@await Html.PartialAsync("_Footer")

<script>
document.addEventListener('DOMContentLoaded', function() {
    let adIdToDelete = null;

    // Handle delete button click
    document.querySelectorAll('[data-bs-target="#deleteModal"]').forEach(function(button) {
        button.addEventListener('click', function() {
            adIdToDelete = this.getAttribute('data-ad-id');
            const adTitle = this.getAttribute('data-ad-title');

            // Update modal content
            document.getElementById('adTitleToDelete').textContent = adTitle;
        });
    });

    // Handle confirm delete button
    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        if (!adIdToDelete) return;

        const button = this;
        const btnText = button.querySelector('.btn-text');
        const spinner = button.querySelector('.spinner-border');

        // Show loading state
        button.disabled = true;
        btnText.textContent = 'در حال حذف...';
        spinner.classList.remove('d-none');

        // Create form data
        const formData = new FormData();
        formData.append('id', adIdToDelete);
        formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

        // Send delete request
        fetch('@Url.Action("DeleteAdvertisement", "User")', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                showToast('success', data.message);

                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
                modal.hide();

                // Reload page after a short delay
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                // Show error message
                showToast('error', data.message);

                // Reset button state
                button.disabled = false;
                btnText.textContent = 'حذف آگهی';
                spinner.classList.add('d-none');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('error', 'خطا در ارتباط با سرور');

            // Reset button state
            button.disabled = false;
            btnText.textContent = 'حذف آگهی';
            spinner.classList.add('d-none');
        });
    });

    // Toast notification function
    function showToast(type, message) {
        // Create toast element
        const toastHtml = `
            <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;

        // Add toast to container
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '1055';
            document.body.appendChild(toastContainer);
        }

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        // Show toast
        const toastElement = toastContainer.lastElementChild;
        const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
        toast.show();

        // Remove toast element after it's hidden
        toastElement.addEventListener('hidden.bs.toast', function() {
            this.remove();
        });
    }
});
</script>

<style>
    .ad-item {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        transition: box-shadow 0.2s ease;
    }

    .ad-item:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .ad-row {
        display: flex;
        align-items: stretch;
        gap: 15px;
    }

    .ad-image {
        flex: 0 0 30%;
        max-width: 30%;
    }

    .ad-content {
        flex: 1;
        min-width: 0;
    }

    .ad-item img {
        width: 100%;
        aspect-ratio: 1 / 1;
        object-fit: cover;
        border-radius: 6px;
    }

    /* موبایل */
    @@media (max-width: 767.98px) {
        .ad-item {
            padding: 10px;
        }

        .ad-row {
            gap: 10px;
        }

        .ad-image {
            flex: 0 0 35%;
            max-width: 35%;
        }

        .ad-content {
            /* محتوای آگهی در موبایل */
        }

        .ad-item img {
            aspect-ratio: 1 / 1;
        }

        .ad-item h5 {
            font-size: 15px;
            line-height: 1.3;
        }

        .ad-item .text-muted {
            font-size: 15px;
            margin-bottom: 8px !important;
        }

        .ad-item .small {
            font-size: 11px;
        }

        .ad-item .badge {
            font-size: 10px;
            padding: 4px 8px;
        }
    }

    /* تبلت */
    @@media (min-width: 768px) and (max-width: 991.98px) {
        .ad-item img {
            aspect-ratio: 1 / 1;
        }
    }
</style>