﻿namespace Keleid.DAL.Models
{
    public class CategoryFeature
    {
        public int Id { get; set; }

        public int CategoryId { get; set; }
        public Category Category { get; set; }

        public string Title { get; set; }
        public string InputType { get; set; }
        public bool IsRequired { get; set; }

        /// <summary>
        /// گزینه‌های انتخابی برای فیلدهای select - با خط تیره جدا شده
        /// مثال: "شخصی|اجاره‌ای|رهنی"
        /// </summary>
        public string? Options { get; set; }
    }

}
