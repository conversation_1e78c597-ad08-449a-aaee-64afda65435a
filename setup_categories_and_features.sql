-- اسکریپت کامل برای ایجاد دسته‌بندی‌ها و ویژگی‌هایشان
-- این اسکریپت ابتدا دسته‌بندی‌ها را ایجاد می‌کند (اگر موجود نباشند) و سپس ویژگی‌هایشان را اضافه می‌کند

-- ایجاد دسته‌بندی‌های اصلی (اگر موجود نباشند)
IF NOT EXISTS (SELECT 1 FROM Categories WHERE Title = N'جایگاه سوخت')
    INSERT INTO Categories (Title, Slug, ParentCategoryId) VALUES (N'جایگاه سوخت', 'jaygah-sokht', NULL);

IF NOT EXISTS (SELECT 1 FROM Categories WHERE Title = N'زمین با کاربری صنعتی')
    INSERT INTO Categories (Title, Slug, ParentCategoryId) VALUES (N'زمین با کاربری صنعتی', 'zamin-ba-karbari-sanati', NULL);

IF NOT EXISTS (SELECT 1 FROM Categories WHERE Title = N'کارخانه صنعتی و تولیدی')
    INSERT INTO Categories (Title, Slug, ParentCategoryId) VALUES (N'کارخانه صنعتی و تولیدی', 'karkhane-sanati-va-tolidi', NULL);

IF NOT EXISTS (SELECT 1 FROM Categories WHERE Title = N'واحد دامداری')
    INSERT INTO Categories (Title, Slug, ParentCategoryId) VALUES (N'واحد دامداری', 'vahed-damdari', NULL);

IF NOT EXISTS (SELECT 1 FROM Categories WHERE Title = N'واحد مرغداری')
    INSERT INTO Categories (Title, Slug, ParentCategoryId) VALUES (N'واحد مرغداری', 'vahed-morghdari', NULL);

IF NOT EXISTS (SELECT 1 FROM Categories WHERE Title = N'سردخانه، انبار، سیلو')
    INSERT INTO Categories (Title, Slug, ParentCategoryId) VALUES (N'سردخانه، انبار، سیلو', 'sardkhane-anbar-silo', NULL);

IF NOT EXISTS (SELECT 1 FROM Categories WHERE Title = N'واحد کشاورزی')
    INSERT INTO Categories (Title, Slug, ParentCategoryId) VALUES (N'واحد کشاورزی', 'vahed-keshavarzi', NULL);

IF NOT EXISTS (SELECT 1 FROM Categories WHERE Title = N'سوله')
    INSERT INTO Categories (Title, Slug, ParentCategoryId) VALUES (N'سوله', 'sole', NULL);

-- حذف ویژگی‌های قبلی (اگر موجود باشند) برای جلوگیری از تکرار
DELETE FROM CategoryFeatures WHERE CategoryId IN (
    SELECT Id FROM Categories WHERE Title IN (
        N'جایگاه سوخت', 
        N'زمین با کاربری صنعتی', 
        N'کارخانه صنعتی و تولیدی',
        N'واحد دامداری',
        N'واحد مرغداری',
        N'سردخانه، انبار، سیلو',
        N'واحد کشاورزی',
        N'سوله'
    )
);

-- اضافه کردن ویژگی‌ها

-- 1. ویژگی‌های جایگاه سوخت
DECLARE @GasStationId INT = (SELECT Id FROM Categories WHERE Title = N'جایگاه سوخت');
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(@GasStationId, N'تعداد نازل بنزین', 'number', 1),
(@GasStationId, N'تعداد نازل گازوئیل', 'number', 1),
(@GasStationId, N'متراژ زمین (متر مربع)', 'number', 1),
(@GasStationId, N'نوع مالکیت', 'select', 1),
(@GasStationId, N'وضعیت مجوز', 'select', 1),
(@GasStationId, N'امکانات جانبی', 'text', 0);

-- 2. ویژگی‌های زمین با کاربری صنعتی
DECLARE @IndustrialLandId INT = (SELECT Id FROM Categories WHERE Title = N'زمین با کاربری صنعتی');
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(@IndustrialLandId, N'متراژ زمین (متر مربع)', 'number', 1),
(@IndustrialLandId, N'نوع کاربری', 'select', 1),
(@IndustrialLandId, N'وضعیت انشعابات', 'text', 1),
(@IndustrialLandId, N'عرض بر (متر)', 'number', 0),
(@IndustrialLandId, N'نوع مالکیت', 'select', 1),
(@IndustrialLandId, N'موقعیت جغرافیایی', 'text', 0);

-- 3. ویژگی‌های کارخانه صنعتی و تولیدی
DECLARE @FactoryId INT = (SELECT Id FROM Categories WHERE Title = N'کارخانه صنعتی و تولیدی');
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(@FactoryId, N'متراژ زیربنا (متر مربع)', 'number', 1),
(@FactoryId, N'متراژ زمین (متر مربع)', 'number', 1),
(@FactoryId, N'نوع صنعت', 'text', 1),
(@FactoryId, N'تعداد کارگر', 'number', 0),
(@FactoryId, N'ظرفیت تولید روزانه', 'text', 0),
(@FactoryId, N'وضعیت مجوزها', 'text', 1),
(@FactoryId, N'تجهیزات موجود', 'text', 0);

-- 4. ویژگی‌های واحد دامداری
DECLARE @LivestockId INT = (SELECT Id FROM Categories WHERE Title = N'واحد دامداری');
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(@LivestockId, N'نوع دام', 'select', 1),
(@LivestockId, N'ظرفیت نگهداری (راس)', 'number', 1),
(@LivestockId, N'متراژ زمین (متر مربع)', 'number', 1),
(@LivestockId, N'متراژ سالن (متر مربع)', 'number', 1),
(@LivestockId, N'تجهیزات موجود', 'text', 0),
(@LivestockId, N'منابع آب', 'text', 1),
(@LivestockId, N'دسترسی به علوفه', 'text', 0);

-- 5. ویژگی‌های واحد مرغداری
DECLARE @PoultryId INT = (SELECT Id FROM Categories WHERE Title = N'واحد مرغداری');
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(@PoultryId, N'ظرفیت پرورش (قطعه)', 'number', 1),
(@PoultryId, N'تعداد سالن', 'number', 1),
(@PoultryId, N'متراژ هر سالن (متر مربع)', 'number', 1),
(@PoultryId, N'سیستم تهویه', 'select', 1),
(@PoultryId, N'سیستم آبخوری', 'select', 1),
(@PoultryId, N'سیستم غذادهی', 'select', 1),
(@PoultryId, N'تجهیزات کنترل دما', 'text', 0);

-- 6. ویژگی‌های سردخانه، انبار، سیلو
DECLARE @WarehouseId INT = (SELECT Id FROM Categories WHERE Title = N'سردخانه، انبار، سیلو');
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(@WarehouseId, N'نوع انبار', 'select', 1),
(@WarehouseId, N'ظرفیت ذخیره‌سازی (تن)', 'number', 1),
(@WarehouseId, N'متراژ زیربنا (متر مربع)', 'number', 1),
(@WarehouseId, N'ارتفاع سقف (متر)', 'number', 0),
(@WarehouseId, N'سیستم سرمایش', 'select', 0),
(@WarehouseId, N'دمای نگهداری', 'text', 0),
(@WarehouseId, N'تجهیزات بارگیری', 'text', 0);

-- 7. ویژگی‌های واحد کشاورزی
DECLARE @AgricultureId INT = (SELECT Id FROM Categories WHERE Title = N'واحد کشاورزی');
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(@AgricultureId, N'متراژ زمین (هکتار)', 'number', 1),
(@AgricultureId, N'نوع محصول', 'text', 1),
(@AgricultureId, N'نوع آبیاری', 'select', 1),
(@AgricultureId, N'منابع آب', 'text', 1),
(@AgricultureId, N'نوع خاک', 'text', 0),
(@AgricultureId, N'تجهیزات موجود', 'text', 0),
(@AgricultureId, N'دسترسی به جاده', 'select', 1);

-- 8. ویژگی‌های سوله
DECLARE @WarehouseBuildingId INT = (SELECT Id FROM Categories WHERE Title = N'سوله');
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(@WarehouseBuildingId, N'متراژ زیربنا (متر مربع)', 'number', 1),
(@WarehouseBuildingId, N'ارتفاع سقف (متر)', 'number', 1),
(@WarehouseBuildingId, N'نوع کاربری', 'select', 1),
(@WarehouseBuildingId, N'نوع سقف', 'text', 0),
(@WarehouseBuildingId, N'تجهیزات بارگیری', 'text', 0),
(@WarehouseBuildingId, N'دسترسی خودرو', 'select', 1),
(@WarehouseBuildingId, N'امکانات جانبی', 'text', 0);

-- نمایش نتایج
PRINT N'دسته‌بندی‌ها و ویژگی‌هایشان با موفقیت اضافه شدند.';

SELECT 
    c.Title as N'دسته‌بندی',
    COUNT(cf.Id) as N'تعداد ویژگی'
FROM Categories c
LEFT JOIN CategoryFeatures cf ON c.Id = cf.CategoryId
WHERE c.Title IN (
    N'جایگاه سوخت', 
    N'زمین با کاربری صنعتی', 
    N'کارخانه صنعتی و تولیدی',
    N'واحد دامداری',
    N'واحد مرغداری',
    N'سردخانه، انبار، سیلو',
    N'واحد کشاورزی',
    N'سوله'
)
GROUP BY c.Title, c.Id
ORDER BY c.Id;
