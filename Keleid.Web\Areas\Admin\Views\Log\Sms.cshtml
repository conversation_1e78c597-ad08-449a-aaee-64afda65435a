﻿@model Keleid.BLL.DTOs.SmsLogPageDto
@{
    ViewBag.Title = "لاگ پیامک‌ها";
}

<!-- Left Sidebar -->
@await Html.PartialAsync("_LeftSidebar")

<!-- Main Content -->
<section class="content home">

    <!-- Breadcrumb -->
    @await Html.PartialAsync("_Breadcrumb")

            <div class="container-fluid">
            <!-- Basic Table -->
            <div class="row clearfix">
                <div class="col-lg-12 col-md-12 col-sm-12">
                    <div class="card">
                        <div class="header">
                            <h2>لاگ <strong>پیامک‌ها</strong></h2>
                            <ul class="header-dropdown m-r--5">
                                <li class="dropdown">
                                    <span class="badge badge-info">@Model.TotalCount پیامک</span>
                                </li>
                            </ul>
                        </div>
                        <div class="body table-responsive">
                            @if (Model.SmsLogs.Any())
                            {
                                <table class="table table-striped m-b-0">
                                    <thead>
                                        <tr>
                                            <th>شماره تماس</th>
                                            <th>متن پیامک</th>
                                            <th>تاریخ ارسال</th>
                                            <th>آدرس IP</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var sms in Model.SmsLogs)
                                        {
                                            <tr>
                                                <td>
                                                    <strong>@sms.Phone</strong>
                                                </td>
                                                <td>
                                                    <span class="text-truncate" style="max-width: 300px; display: inline-block;" title="@sms.Body">
                                                        @sms.Body
                                                    </span>
                                                </td>
                                                <td>
                                                    <span>@sms.RelativeTime</span>
                                                    <br><small class="text-muted">@sms.FormattedDate</small>
                                                </td>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(sms.IpAddress))
                                                    {
                                                        <code>@sms.IpAddress</code>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">نامشخص</span>
                                                    }
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>

                                <!-- Pagination -->
                                @if (Model.TotalPages > 1)
                                {
                                    <div class="d-flex justify-content-between align-items-center mt-3">
                                        <div>
                                            <small class="text-muted">
                                                نمایش @((Model.CurrentPage - 1) * 20 + 1) تا @(Math.Min(Model.CurrentPage * 20, Model.TotalCount)) از @Model.TotalCount پیامک
                                            </small>
                                        </div>
                                        <nav>
                                            <ul class="pagination pagination-sm">
                                                @if (Model.HasPreviousPage)
                                                {
                                                    <li class="page-item">
                                                        <a class="page-link" href="@Url.Action("Sms", new { page = Model.CurrentPage - 1 })">قبلی</a>
                                                    </li>
                                                }

                                                @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                                                {
                                                    <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                                        <a class="page-link" href="@Url.Action("Sms", new { page = i })">@i</a>
                                                    </li>
                                                }

                                                @if (Model.HasNextPage)
                                                {
                                                    <li class="page-item">
                                                        <a class="page-link" href="@Url.Action("Sms", new { page = Model.CurrentPage + 1 })">بعدی</a>
                                                    </li>
                                                }
                                            </ul>
                                        </nav>
                                    </div>
                                }
                            }
                            else
                            {
                                <div class="alert alert-info text-center">
                                    <i class="zmdi zmdi-info"></i>
                                    هیچ پیامکی یافت نشد.
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>

</section>