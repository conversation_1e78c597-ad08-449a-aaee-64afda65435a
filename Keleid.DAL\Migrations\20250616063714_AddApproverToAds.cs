﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Keleid.DAL.Migrations
{
    /// <inheritdoc />
    public partial class AddApproverToAds : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ApprovedByUserId",
                table: "Advertisements",
                type: "nvarchar(450)",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Advertisements_ApprovedByUserId",
                table: "Advertisements",
                column: "ApprovedByUserId");

            migrationBuilder.AddForeignKey(
                name: "FK_Advertisements_AspNetUsers_ApprovedByUserId",
                table: "Advertisements",
                column: "ApprovedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Advertisements_AspNetUsers_ApprovedByUserId",
                table: "Advertisements");

            migrationBuilder.DropIndex(
                name: "IX_Advertisements_ApprovedByUserId",
                table: "Advertisements");

            migrationBuilder.DropColumn(
                name: "ApprovedByUserId",
                table: "Advertisements");
        }
    }
}
