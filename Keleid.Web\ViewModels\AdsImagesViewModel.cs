using Keleid.BLL.DTOs;

namespace Keleid.Web.ViewModels
{
    public class AdsImagesViewModel
    {
        public int CategoryId { get; set; }
        public string CategoryTitle { get; set; } = "";
        
        // تصاویر موجود (اگر در حال ویرایش باشیم)
        public List<AdImageDto> ExistingImages { get; set; } = new List<AdImageDto>();
        
        // فایل‌های جدید برای آپلود
        public List<IFormFile> NewImages { get; set; } = new List<IFormFile>();
        
        // شناسه تصویر اصلی
        public int? MainImageId { get; set; }
        
        // آیا حداقل یک تصویر آپلود شده؟
        public bool HasImages => ExistingImages.Any() || NewImages.Any();
        
        // تعداد کل تصاویر
        public int TotalImagesCount => ExistingImages.Count + NewImages.Count;
        
        // آیا می‌توان تصویر جدید اضافه کرد؟
        public bool CanAddMoreImages => TotalImagesCount < 10;
    }
}
