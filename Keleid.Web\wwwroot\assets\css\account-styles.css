@font-face {
    font-family: 'IRANSans';
    src: url('../fonts/IRANSansWeb.woff2') format('woff2'),
         url('../fonts/IRANSansWeb.woff') format('woff'),
         url('../fonts/IRANSansWeb.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'IRANSans', Arial, sans-serif;
    background: white;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.login-container {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
}

.login-box {
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 16px;
    overflow: hidden;
    position: relative;
    padding: 40px 30px;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
    position: relative;
}

.logo {
    margin-bottom: 20px;
}

.logo-img {
    height: 50px;
    width: auto;
}

.login-header h1,
.login-header h2 {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
}

.subtitle {
    font-size: 18px;
    color: #333;
    margin-bottom: 8px;
}

.description {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}

.back-btn {
    position: absolute;
    right: 0;
    top: 7px;
    background: none;
    border: none;
    font-size: 20px;
    color: #666;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.2s;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-btn:hover {
    background: #f5f5f5;
    color: #333;
}

.user-info {
    margin-top: 30px;
}

.user-info h3 {
    font-size: 18px;
    color: #333;
    margin-bottom: 8px;
}

.edit-phone {
    background: none;
    border: none;
    color: #007bff;
    font-size: 14px;
    cursor: pointer;
    text-decoration: underline;
}

.login-form {
    margin-bottom: 20px;
}

.input-group {
    margin-bottom: 20px;
}

.input-group label {
    display: block;
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
    font-weight: 500;
}

.phone-input,
.password-input,
.verification-input {
    position: relative;
    display: flex;
    align-items: center;
}

.phone-input input,
.password-input input,
.verification-input input {
    width: 100%;
    padding: 16px;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    font-size: 16px;
    font-family: inherit;
    transition: all 0.2s;
    background: #fafafa;
}

.phone-input input,
.password-input input {
    padding: 16px;
}

.verification-input input {
    text-align: center;
    letter-spacing: 8px;
    font-size: 20px;
    font-weight: bold;
}

.phone-input input:focus,
.password-input input:focus,
.verification-input input:focus {
    outline: none;
    border-color: #007bff;
    background: white;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.country-code {
    display: none;
}

.toggle-password {
    display: none;
}

.error-message {
    color: #dc3545;
    font-size: 12px;
    margin-top: 8px;
    display: none;
}

.error-message.show {
    display: block;
}

.btn-primary {
    width: 100%;
    padding: 16px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-family: inherit;
    text-decoration: none;
}

.btn-primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-link {
    background: none;
    border: none;
    color: #007bff;
    font-size: 14px;
    cursor: pointer;
    text-decoration: underline;
    font-family: inherit;
    margin-top: 16px;
    width: 100%;
    padding: 8px;
}

.btn-link:hover {
    color: #0056b3;
}

.btn-link:disabled {
    color: #ccc;
    cursor: not-allowed;
    text-decoration: none;
}

.terms {
    text-align: center;
    margin-top: 20px;
}

.terms p {
    font-size: 12px;
    color: #666;
    line-height: 1.5;
}

.terms a {
    color: #007bff;
    text-decoration: none;
}

.terms a:hover {
    text-decoration: underline;
}

.auth-section h4 {
    font-size: 18px;
    color: #333;
    margin-bottom: 16px;
    text-align: center;
}

.verification-desc {
    font-size: 14px;
    color: #666;
    text-align: center;
    margin-bottom: 24px;
    line-height: 1.5;
}

.timer-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    font-size: 14px;
}

#timer {
    color: #007bff;
    font-weight: bold;
}

/* Responsive */
@media (max-width: 480px) {
    .login-container {
        padding: 10px;
    }
    
    .login-box {
        padding: 30px 20px;
    }
    
    .login-header h1,
    .login-header h2 {
        font-size: 20px;
    }
    
    .subtitle {
        font-size: 16px;
    }
}
