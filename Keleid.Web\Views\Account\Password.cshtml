﻿@await Html.PartialAsync("_Account")

<div class="login-container">
    <div class="login-box">
        <div class="login-header">
            <div class="logo">
                <a asp-controller="Home" asp-action="Index"><img src="/assets/img/logo.png" alt="لوگو" class="logo-img"></a>
            </div>
            <a href="@Url.Action("Index", "Account")" class="back-btn">
                <i class="bi bi-arrow-right"></i>
            </a>
        </div>

        <div class="auth-section">
            <h4>رمز عبور خود را وارد کنید</h4>
            <p class="verification-desc">شماره <span id="loginPhone" dir="ltr">0935***1234</span></p>

            <form id="passwordForm" class="login-form" autocomplete="on">
                @Html.AntiForgeryToken()

                <!-- فیلد مخفی برای شماره تلفن به عنوان username -->
                <input type="text" id="username" name="username" autocomplete="username" style="position: absolute; left: -9999px; opacity: 0;" tabindex="-1">

                <div class="mb-3">
                    <div class="password-input">
                        <input type="password" id="password" name="password" placeholder="رمز عبور" autocomplete="current-password" required>
                    </div>
                    <div class="error-message" id="passwordError"></div>
                </div>

                <button type="button" class="btn-link mt-0 mb-3 text-start" id="forgotPassword">
                    ورود با رمز یکبار مصرف
                </button>

                <button type="submit" class="btn btn-danger w-100" id="loginBtn">
                    <span id="loginBtnText">ورود</span>
                    <i class="bi bi-arrow-left"></i>
                </button>
            </form>
        </div>
    </div>
</div>

@section js {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const passwordForm = document.getElementById('passwordForm');
            const passwordInput = document.getElementById('password');
            const usernameInput = document.getElementById('username');
            const passwordError = document.getElementById('passwordError');
            const loginBtn = document.getElementById('loginBtn');
            const loginBtnText = document.getElementById('loginBtnText');
            const forgotPasswordBtn = document.getElementById('forgotPassword');
            const loginPhoneElement = document.getElementById('loginPhone');

            // دریافت شماره تلفن از server-side
            const phoneNumber = '@Context.Session.GetString("PhoneNumber")';
            if (phoneNumber) {
                // نمایش شماره تلفن مخفی شده
                const maskedPhone = phoneNumber.substring(0, 4) + '***' + phoneNumber.substring(7);
                loginPhoneElement.textContent = maskedPhone;

                // پر کردن فیلد username مخفی برای مرورگر
                usernameInput.value = phoneNumber;
            }

            // ارسال فرم لاگین
            passwordForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Clear previous errors
                passwordError.textContent = '';
                passwordError.classList.remove('show');

                const password = passwordInput.value.trim();

                // اعتبارسنجی
                if (!password) {
                    passwordError.textContent = 'لطفا رمز عبور خود را وارد کنید';
                    passwordError.classList.add('show');
                    return;
                }

                if (!phoneNumber) {
                    passwordError.textContent = 'شماره تلفن یافت نشد';
                    passwordError.classList.add('show');
                    return;
                }

                // Disable button and show loading
                loginBtn.disabled = true;
                loginBtnText.textContent = 'در حال ورود...';

                // ارسال درخواست AJAX
                fetch('@Url.Action("Login", "Account")', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                    },
                    body: `phoneNumber=${encodeURIComponent(phoneNumber)}&password=${encodeURIComponent(password)}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // ورود موفق، هدایت به صفحه مقصد
                        window.location.href = data.redirectUrl;
                    } else {
                        passwordError.textContent = data.message || 'خطایی رخ داده است';
                        passwordError.classList.add('show');

                        // اگر نیاز به تایید شماره تلفن دارد
                        if (data.needsVerification) {
                            setTimeout(() => {
                                window.location.href = '@Url.Action("Active", "Account")';
                            }, 2000);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    passwordError.textContent = 'خطای ارتباط با سرور';
                    passwordError.classList.add('show');
                })
                .finally(() => {
                    // Re-enable button
                    loginBtn.disabled = false;
                    loginBtnText.textContent = 'ورود';
                });
            });

            // دکمه فراموشی رمز عبور (ورود با رمز یکبار مصرف)
            forgotPasswordBtn.addEventListener('click', function() {
                if (!phoneNumber) {
                    passwordError.textContent = 'شماره تلفن یافت نشد';
                    passwordError.classList.add('show');
                    return;
                }

                forgotPasswordBtn.disabled = true;
                forgotPasswordBtn.textContent = 'در حال ارسال...';

                fetch('@Url.Action("ForgotPassword", "Account")', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                    },
                    body: `phoneNumber=${encodeURIComponent(phoneNumber)}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // کد ارسال شد، هدایت به صفحه تایید
                        window.location.href = data.redirectUrl;
                    } else {
                        passwordError.textContent = data.message || 'خطا در ارسال کد';
                        passwordError.classList.add('show');

                        // اگر محدودیت زمانی دارد
                        if (data.remainingSeconds && data.remainingSeconds > 0) {
                            let remainingTime = data.remainingSeconds;
                            const cooldownInterval = setInterval(() => {
                                if (remainingTime <= 0) {
                                    clearInterval(cooldownInterval);
                                    forgotPasswordBtn.disabled = false;
                                    forgotPasswordBtn.textContent = 'ورود با رمز یکبار مصرف';
                                } else {
                                    const minutes = Math.floor(remainingTime / 60);
                                    const seconds = remainingTime % 60;
                                    forgotPasswordBtn.textContent = `صبر کنید ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                                    remainingTime--;
                                }
                            }, 1000);
                        } else {
                            forgotPasswordBtn.disabled = false;
                            forgotPasswordBtn.textContent = 'ورود با رمز یکبار مصرف';
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    passwordError.textContent = 'خطای ارتباط با سرور';
                    passwordError.classList.add('show');
                    forgotPasswordBtn.disabled = false;
                    forgotPasswordBtn.textContent = 'ورود با رمز یکبار مصرف';
                });
            });
        });
    </script>
}