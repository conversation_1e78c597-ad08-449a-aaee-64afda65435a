namespace Keleid.Web.Services
{
    public interface IFileService
    {
        /// <summary>
        /// آپلود تصویر آگهی
        /// </summary>
        /// <param name="file">فایل تصویر</param>
        /// <param name="advertisementId">شناسه آگهی</param>
        /// <returns>مسیر نسبی فایل ذخیره شده</returns>
        Task<string> UploadAdvertisementImageAsync(IFormFile file, int advertisementId);

        /// <summary>
        /// آپلود چندین تصویر آگهی
        /// </summary>
        /// <param name="files">فایل‌های تصویر</param>
        /// <param name="advertisementId">شناسه آگهی</param>
        /// <returns>لیست مسیرهای نسبی فایل‌های ذخیره شده</returns>
        Task<List<string>> UploadAdvertisementImagesAsync(IFormFileCollection files, int advertisementId);

        /// <summary>
        /// حذف تصویر آگهی
        /// </summary>
        /// <param name="imagePath">مسیر تصویر</param>
        /// <returns>نتیجه عملیات</returns>
        Task<bool> DeleteAdvertisementImageAsync(string imagePath);

        /// <summary>
        /// حذف تمام تصاویر یک آگهی
        /// </summary>
        /// <param name="advertisementId">شناسه آگهی</param>
        /// <returns>نتیجه عملیات</returns>
        Task<bool> DeleteAdvertisementImagesAsync(int advertisementId);

        /// <summary>
        /// بررسی معتبر بودن فایل تصویر
        /// </summary>
        /// <param name="file">فایل تصویر</param>
        /// <returns>نتیجه اعتبارسنجی</returns>
        bool IsValidImageFile(IFormFile file);

        /// <summary>
        /// دریافت مسیر کامل فایل
        /// </summary>
        /// <param name="relativePath">مسیر نسبی</param>
        /// <returns>مسیر کامل</returns>
        string GetFullPath(string relativePath);
    }
}
