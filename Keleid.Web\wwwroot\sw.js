const CACHE_NAME = 'keleid-v1.2';
const urlsToCache = [
  '/',
  '/assets/css/styles.css',
  '/assets/css/pwa.css',
  '/assets/libs/bootstrap-5.0.2/css/bootstrap.rtl.min.css',
  '/assets/libs/fontAwesome-6.5.1/css/all.min.css',
  '/assets/libs/bootstrap-5.0.2/js/bootstrap.bundle.min.js',
  '/assets/js/scripts.js',
  '/assets/js/pwa.js',
  '/assets/img/logo.png',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png',
  '/manifest.json',
  '/offline.html'
];

// Install event - cache resources
self.addEventListener('install', event => {
  console.log('Service Worker installing with cache:', CACHE_NAME);
  // Skip waiting to activate immediately
  self.skipWaiting();

  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Opened cache');
        return cache.addAll(urlsToCache);
      })
  );
});

// Fetch event - serve from cache when offline
self.addEventListener('fetch', event => {
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // Return cached version or fetch from network
        if (response) {
          return response;
        }
        return fetch(event.request);
      })
      .catch(() => {
        // If both cache and network fail, show offline page for navigation requests
        if (event.request.destination === 'document') {
          return caches.match('/offline.html');
        }
      })
  );
});

// Activate event - clean up old caches and notify clients
self.addEventListener('activate', event => {
  console.log('Service Worker activating with cache:', CACHE_NAME);

  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then(cacheNames => {
        const oldCaches = cacheNames.filter(cacheName => cacheName !== CACHE_NAME);
        if (oldCaches.length > 0) {
          console.log('Deleting old caches:', oldCaches);
          // Notify clients about update
          self.clients.matchAll().then(clients => {
            clients.forEach(client => {
              client.postMessage({
                type: 'CACHE_UPDATED',
                oldCaches: oldCaches,
                newCache: CACHE_NAME
              });
            });
          });
        }
        return Promise.all(
          oldCaches.map(cacheName => caches.delete(cacheName))
        );
      }),
      // Take control of all clients immediately
      self.clients.claim()
    ])
  );
});

// Background sync for offline form submissions
self.addEventListener('sync', event => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

function doBackgroundSync() {
  // Handle offline form submissions when back online
  return new Promise((resolve) => {
    // Implementation for handling offline data sync
    resolve();
  });
}

// Push notification handling
self.addEventListener('push', event => {
  const options = {
    body: event.data ? event.data.text() : 'پیام جدید از کلید',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/icon-192x192.png',
    dir: 'rtl',
    lang: 'fa'
  };

  event.waitUntil(
    self.registration.showNotification('کلید', options)
  );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
  event.notification.close();
  
  event.waitUntil(
    clients.openWindow('/')
  );
});
