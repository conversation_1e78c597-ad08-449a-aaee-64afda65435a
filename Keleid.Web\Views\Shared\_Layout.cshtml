﻿<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>کلید - نیازمندی‌های رایگان صنعتی</title>

    <!-- Bootstrap 5 RTL CSS -->
    <link rel="stylesheet" href="/assets/libs/bootstrap-5.0.2/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="~/assets/libs/fontAwesome-6.5.1/css/all.min.css">

    <!-- PWA Meta Tags -->
    <link rel="manifest" href="~/manifest.json" />
    <link rel="shortcut icon" href="~/favicon.ico" />
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="کلید">
    <link rel="apple-touch-icon" href="~/icons/icon-180x180.png">
    <meta name="msapplication-TileImage" content="~/icons/icon-192x192.png">
    <meta name="msapplication-TileColor" content="#a62626">

    @RenderSection("css", false)

    <!-- Custom CSS -->
    <link rel="stylesheet" href="/assets/css/styles.css">
    <link rel="stylesheet" href="/assets/css/pwa.css">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@@context": "https://schema.org",
        "@@type": "Organization",
        "name": "کلید",
        "alternateName": "نیازمندی‌های رایگان صنعتی کلید",
        "url": "https://keleid.ir",
        "logo": "https://keleid.ir/assets/img/logo.png",
        "description": "کلید یک پلتفرم آنلاین برای خرید و فروش نیازمندی‌های صنعتی است که به کاربران امکان ثبت و جستجوی آگهی‌های مختلف در حوزه‌های صنعتی را فراهم می‌کند.",
        "address": {
            "@@type": "PostalAddress",
            "streetAddress": "خیابان آزادی، پلاک 123",
            "addressLocality": "تهران",
            "postalCode": "1234567890",
            "addressCountry": "IR"
        },
        "contactPoint": {
            "@@type": "ContactPoint",
            "telephone": "+982112345678",
            "contactType": "customer service",
            "availableLanguage": "Persian"
        },
        "sameAs": [
            "https://instagram.com/keleid_ir",
            "https://t.me/keleid_ir",
            "https://eitaa.com/keleid_ir"
        ]
    }
    </script>

    <script type="application/ld+json">
    {
        "@@context": "https://schema.org",
        "@@type": "WebSite",
        "name": "کلید",
        "url": "https://keleid.ir",
        "description": "نیازمندی‌های رایگان صنعتی - پلتفرم خرید و فروش تجهیزات و ماشین‌آلات صنعتی",
        "potentialAction": {
            "@@type": "SearchAction",
            "target": {
                "@@type": "EntryPoint",
                "urlTemplate": "https://keleid.ir/search/{search_term_string}"
            },
            "query-input": "required name=search_term_string"
        },
        "publisher": {
            "@@type": "Organization",
            "name": "کلید",
            "logo": "https://keleid.ir/assets/img/logo.png"
        }
    }
    </script>

    @RenderSection("structuredData", false)
</head>
<body>
    <!-- Navbar -->
    <nav id="DefHeader" class="navbar navbar-expand-lg bg-white border-bottom sticky-top">

        <!-- Desktop Header -->
        <div class="container d-none d-lg-flex">
            <a class="navbar-brand" href="~/">
                <img src="/assets/img/logo.png" alt="کلید" height="24">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMain">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarMain">
                <div class="d-flex align-items-center flex-grow-1">
                    <div class="dropdown me-3">
                        <button class="btn" type="button" data-bs-toggle="modal" data-bs-target="#cityModal">
                            <i class="fas fa-location-dot"></i>
                            <span class="selected-city">@(ViewBag.SelectedProvince ?? "کل ایران")</span>
                        </button>
                    </div>
                    <div class="search-box flex-grow-1">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="جستجو در همه آگهی‌ها" value="@(ViewBag.SearchTerm ?? "")" id="desktopSearchInput">
                            <span class="input-group-text d-flex">
                                @if (!string.IsNullOrEmpty(ViewBag.SearchTerm))
                                {
                                    <button type="button" class="btn btn-link clear-search-btn p-0" style="border: none; background: none; color: #6c757d;">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <div class="search-divider"></div>
                                }
                                <i class="fas fa-search"></i>
                            </span>
                        </div>
                    </div>
                </div>
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="User" asp-action="Index"><i class="fas fa-user"></i> کلید من</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Info" asp-action="Contact"><i class="fas fa-question-circle"></i> پشتیبانی</a>
                    </li>
                    <li class="nav-item">
                        <a asp-controller="Ads" asp-action="Index" class="btn btn-danger" >ثبت آگهی</a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Mobile Header -->
        <div class="mobile-header d-flex d-lg-none">
            <div class="d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#cityModal" style="cursor: pointer;">
                <i class="fas fa-location-dot"></i>
                <span class="me-2 selected-city">@(ViewBag.SelectedProvince ?? "کل ایران")</span>
            </div>
            <div class="mobile-search">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="جستجو در همه آگهی‌ها" value="@(ViewBag.SearchTerm ?? "")" id="mobileSearchInput">
                    <span class="input-group-text d-flex">
                        @if (!string.IsNullOrEmpty(ViewBag.SearchTerm))
                        {
                            <button type="button" class="btn btn-link clear-search-btn p-0" style="border: none; background: none; color: #6c757d;">
                                <i class="fas fa-times"></i>
                            </button>
                            <div class="search-divider"></div>
                        }
                        <i class="fas fa-search"></i>
                    </span>
                </div>
            </div>
        </div>
    </nav>

    @RenderBody()

    <!-- Mobile Navigation -->
    @await Html.PartialAsync("_MobileNav")

    <!-- Modal انتخاب استان -->
    <div class="modal fade" id="cityModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header border-0">
                    <h5 class="modal-title">انتخاب شهر</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="city-search p-3 border-bottom">
                    <div class="input-group">
                        <span class="input-group-text bg-transparent border-start-0">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control border-end-0" placeholder="جستجو در شهرها" id="citySearchInput">
                    </div>
                </div>
                <div class="modal-body p-0">
                    <div class="city-list">
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="allIran" value="کل ایران" checked>
                            <label class="form-check-label w-100" for="allIran">
                                کل ایران
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="eastAzerbaijan" value="آذربایجان شرقی">
                            <label class="form-check-label w-100" for="eastAzerbaijan">
                                آذربایجان شرقی
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="westAzerbaijan" value="آذربایجان غربی">
                            <label class="form-check-label w-100" for="westAzerbaijan">
                                آذربایجان غربی
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="ardabil" value="اردبیل">
                            <label class="form-check-label w-100" for="ardabil">
                                اردبیل
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="isfahan" value="اصفهان">
                            <label class="form-check-label w-100" for="isfahan">
                                اصفهان
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="alborz" value="البرز">
                            <label class="form-check-label w-100" for="alborz">
                                البرز
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="ilam" value="ایلام">
                            <label class="form-check-label w-100" for="ilam">
                                ایلام
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="bushehr" value="بوشهر">
                            <label class="form-check-label w-100" for="bushehr">
                                بوشهر
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="tehran" value="تهران">
                            <label class="form-check-label w-100" for="tehran">
                                تهران
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="chaharmahal" value="چهارمحال و بختیاری">
                            <label class="form-check-label w-100" for="chaharmahal">
                                چهارمحال و بختیاری
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="khorasan-south" value="خراسان جنوبی">
                            <label class="form-check-label w-100" for="khorasan-south">
                                خراسان جنوبی
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="khorasan-razavi" value="خراسان رضوی">
                            <label class="form-check-label w-100" for="khorasan-razavi">
                                خراسان رضوی
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="khorasan-north" value="خراسان شمالی">
                            <label class="form-check-label w-100" for="khorasan-north">
                                خراسان شمالی
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="khuzestan" value="خوزستان">
                            <label class="form-check-label w-100" for="khuzestan">
                                خوزستان
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="zanjan" value="زنجان">
                            <label class="form-check-label w-100" for="zanjan">
                                زنجان
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="semnan" value="سمنان">
                            <label class="form-check-label w-100" for="semnan">
                                سمنان
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="sistan" value="سیستان و بلوچستان">
                            <label class="form-check-label w-100" for="sistan">
                                سیستان و بلوچستان
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="fars" value="فارس">
                            <label class="form-check-label w-100" for="fars">
                                فارس
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="qazvin" value="قزوین">
                            <label class="form-check-label w-100" for="qazvin">
                                قزوین
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="qom" value="قم">
                            <label class="form-check-label w-100" for="qom">
                                قم
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="kurdistan" value="کردستان">
                            <label class="form-check-label w-100" for="kurdistan">
                                کردستان
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="kerman" value="کرمان">
                            <label class="form-check-label w-100" for="kerman">
                                کرمان
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="kermanshah" value="کرمانشاه">
                            <label class="form-check-label w-100" for="kermanshah">
                                کرمانشاه
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="kohgiluyeh" value="کهگیلویه و بویراحمد">
                            <label class="form-check-label w-100" for="kohgiluyeh">
                                کهگیلویه و بویراحمد
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="golestan" value="گلستان">
                            <label class="form-check-label w-100" for="golestan">
                                گلستان
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="gilan" value="گیلان">
                            <label class="form-check-label w-100" for="gilan">
                                گیلان
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="lorestan" value="لرستان">
                            <label class="form-check-label w-100" for="lorestan">
                                لرستان
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="mazandaran" value="مازندران">
                            <label class="form-check-label w-100" for="mazandaran">
                                مازندران
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="markazi" value="مرکزی">
                            <label class="form-check-label w-100" for="markazi">
                                مرکزی
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="hormozgan" value="هرمزگان">
                            <label class="form-check-label w-100" for="hormozgan">
                                هرمزگان
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="hamedan" value="همدان">
                            <label class="form-check-label w-100" for="hamedan">
                                همدان
                            </label>
                        </div>
                        <div class="form-check p-3 border-bottom">
                            <input class="form-check-input" type="radio" name="city" id="yazd" value="یزد">
                            <label class="form-check-label w-100" for="yazd">
                                یزد
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">انصراف</button>
                    <button type="button" class="btn btn-danger" id="confirmCitySelection">تایید</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/assets/libs/bootstrap-5.0.2/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="/assets/js/scripts.js"></script>

    <!-- PWA JS with cache buster -->
    <script src="/assets/js/pwa.js?v=@DateTime.Now.Ticks"></script>

    @RenderSection("js", false)

</body>
</html>