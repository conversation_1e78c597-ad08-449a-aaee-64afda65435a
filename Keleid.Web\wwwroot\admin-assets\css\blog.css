.blog-page .single_post {
    -webkit-transition: all .4s ease;
    transition: all .4s ease
}

.blog-page .single_post .img-post {
    position: relative;
    overflow: hidden;
    max-height: 500px
}

.blog-page .single_post .img-post>img {
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
    opacity: 1;
    -webkit-transition: -webkit-transform .4s ease, opacity .4s ease;
    transition: transform .4s ease, opacity .4s ease;
    max-width: 100%;
    filter: none;
    -webkit-filter: grayscale(0);
    -webkit-transform: scale(1.01)
}

.blog-page .single_post .img-post:hover img {
    -webkit-transform: scale(1.02);
    -ms-transform: scale(1.02);
    transform: scale(1.02);
    opacity: .7;
    filter: gray;
    -webkit-filter: grayscale(1);
    -webkit-transition: all .8s ease-in-out
}

.blog-page .single_post .img-post:hover .social_share {
    display: block
}

.blog-page .single_post .img-post .social_share {
    position: absolute;
    bottom: 10px;
    right: 10px;
    display: none
}

.blog-page .single_post .meta {
    list-style: none;
    padding: 0;
    margin: 0
}

.blog-page .single_post .meta li {
    display: inline-block;
    margin-left: 15px
}

.blog-page .single_post .meta li a {
    font-style: italic;
    color: #959595;
    text-decoration: none;
    font-size: 12px
}

.blog-page .single_post .meta li a i {
    margin-left: 6px;
    font-size: 12px
}

.blog-page .single_post h3 {
    font-size: 20px;
    line-height: 26px;
    -webkit-transition: color .4s ease;
    transition: color .4s ease
}

.blog-page .single_post h3 a {
    color: #242424;
    text-decoration: none
}

.blog-page .single_post p {
    font-size: 16px;
    text-align:justify
}

.blog-page .single_post .blockquote p {
    margin-top: 0 !important
}

.blog-page .right-box .popular-post ul li {
    margin-bottom: 15px
}

.blog-page .right-box .popular-post ul li:last-child {
    margin-bottom: 0
}

.blog-page .right-box .popular-post ul li h5 {
    font-size: 18px
}

.blog-page .right-box .popular-post ul li h5 a {
    color: #333
}

.blog-page .right-box .popular-post ul li .author-name,
.blog-page .right-box .popular-post ul li .date {
    display: block
}

.blog-page .right-box .tag-clouds ul li {
    display: inline-block;
    margin: 5px
}

.blog-page .right-box .instagram-plugin {
    overflow: hidden
}

.blog-page .right-box .instagram-plugin li {
    float: right;
    overflow: hidden
}

.blog-page .comment-reply li {
    margin-bottom: 15px
}

.blog-page .comment-reply li:last-child {
    margin-bottom: none
}

.blog-page .comment-reply li p {
    margin-bottom: 0px;
    font-size: 16px;
    color: #777
}

.blog-page .comment-reply .list-inline li {
    display: inline-block;
    margin: 0;
    padding-left: 20px
}

.blog-page .comment-reply .list-inline li a {
    font-size: 13px
}

.page.with-sidebar.right .left-box {
    margin-right: -20px
}

@media (max-width: 414px) {
    .section.blog-page {
        padding: 20px 0
    }
    .blog-page .left-box .single-comment-box>ul>li {
        padding: 25px 0
    }
    .blog-page .left-box .single-comment-box ul li .icon-box {
        display: inline-block
    }
    .blog-page .left-box .single-comment-box ul li .text-box {
        display: block;
        padding-left: 0;
        margin-top: 10px
    }
}

.list-unstyled,
.list-inline {
    padding-right: 0px;
}