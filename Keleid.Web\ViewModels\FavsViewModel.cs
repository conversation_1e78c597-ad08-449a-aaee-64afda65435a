using Keleid.BLL.DTOs;

namespace Keleid.Web.ViewModels
{
    public class FavsViewModel
    {
        public List<FavoriteDto> Favorites { get; set; } = new List<FavoriteDto>();
        public int CurrentPage { get; set; } = 1;
        public int TotalPages { get; set; } = 1;
        public int TotalCount { get; set; } = 0;
        public int PageSize { get; set; } = 10;

        // Pagination helpers
        public bool HasPreviousPage => CurrentPage > 1;
        public bool HasNextPage => CurrentPage < TotalPages;
        public int PreviousPage => CurrentPage - 1;
        public int NextPage => CurrentPage + 1;

        // Page range for pagination display
        public List<int> GetPageRange()
        {
            var pages = new List<int>();
            var startPage = Math.Max(1, CurrentPage - 2);
            var endPage = Math.Min(TotalPages, CurrentPage + 2);

            for (int i = startPage; i <= endPage; i++)
            {
                pages.Add(i);
            }

            return pages;
        }

        // Check if we should show ellipsis before or after page range
        public bool ShowStartEllipsis => GetPageRange().First() > 2;
        public bool ShowEndEllipsis => GetPageRange().Last() < TotalPages - 1;
    }
}
