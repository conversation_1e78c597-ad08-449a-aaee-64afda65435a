﻿@{
    ViewBag.Title = "افزودن دسته بندی";
}

<!-- Left Sidebar -->
@await Html.PartialAsync("_LeftSidebar")

<!-- Main Content -->
<section class="content home">

    <!-- Breadcrumb -->
    @await Html.PartialAsync("_Breadcrumb")

    <div class="container-fluid">
        <div class="row clearfix">
            <div class="col-lg-8 col-md-12 mx-auto">
                <div class="card">
                    <div class="header">
                        <h2><i class="fas fa-plus text-primary"></i> افزودن <strong>دسته بندی اصلی</strong></h2>
                    </div>
                    <div class="body">
                        <form asp-area="Admin" asp-controller="Category" asp-action="Add" method="post">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="title" class="form-label">
                                            <i class="fas fa-tag"></i> عنوان دسته بندی <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" id="title" name="Title" class="form-control"
                                               placeholder="مثال: جایگاه سوخت" required maxlength="100">
                                        <small class="form-text text-muted">
                                            عنوان دسته بندی باید منحصر به فرد باشد
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="slug" class="form-label">
                                            <i class="fas fa-link"></i> نامک (Slug) <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" id="slug" name="Slug" class="form-control"
                                               placeholder="مثال: gas-station" required maxlength="100">
                                        <small class="form-text text-muted">
                                            فقط حروف انگلیسی، اعداد و خط تیره مجاز است
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-info-circle"></i> پیش‌نمایش
                                        </label>
                                        <div class="alert alert-info">
                                            <i class="fas fa-folder text-primary"></i>
                                            <span id="preview-title">عنوان دسته بندی</span>
                                            <small class="text-muted d-block">
                                                URL: /category/<span id="preview-slug">slug</span>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <hr>
                                    <button type="submit" class="btn btn-primary btn-round waves-effect">
                                        <i class="fas fa-save"></i> ذخیره دسته بندی
                                    </button>
                                    <a asp-area="Admin" asp-controller="Category" asp-action="Index"
                                       class="btn btn-secondary btn-round waves-effect">
                                        <i class="fas fa-arrow-left"></i> بازگشت
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const titleInput = document.getElementById('title');
    const slugInput = document.getElementById('slug');
    const previewTitle = document.getElementById('preview-title');
    const previewSlug = document.getElementById('preview-slug');

    // Auto-generate slug from title
    titleInput.addEventListener('input', function() {
        const title = this.value;
        previewTitle.textContent = title || 'عنوان دسته بندی';

        // Generate Persian slug
        let slug = title
            .replace(/\s+/g, '-')         // Replace spaces with hyphens
            .replace(/-+/g, '-')          // Replace multiple hyphens with single
            .replace(/^-+|-+$/g, '');     // Remove leading/trailing hyphens

        if (slug) {
            slugInput.value = slug;
            previewSlug.textContent = slug;
        }
    });

    // Update preview when slug changes
    slugInput.addEventListener('input', function() {
        previewSlug.textContent = this.value || 'slug';
    });
});
</script>