namespace Keleid.BLL.DTOs
{
    public class SmsLogDto
    {
        public int Id { get; set; }
        public string Phone { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public string? IpAddress { get; set; }
        public string RelativeTime { get; set; } = string.Empty;
        public string FormattedDate { get; set; } = string.Empty;
    }

    public class SmsLogPageDto
    {
        public List<SmsLogDto> SmsLogs { get; set; } = new();
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public int TotalCount { get; set; }
        public bool HasPreviousPage => CurrentPage > 1;
        public bool HasNextPage => CurrentPage < TotalPages;
    }

    public class GeneralLogDto
    {
        public string Level { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string? Exception { get; set; }
        public string? Properties { get; set; }
        public string RelativeTime { get; set; } = string.Empty;
        public string FormattedDate { get; set; } = string.Empty;
        public string LevelClass => Level.ToLower() switch
        {
            "error" => "badge-danger",
            "warning" => "badge-warning",
            "information" => "badge-info",
            "debug" => "badge-secondary",
            _ => "badge-primary"
        };
        public string LevelText => Level.ToLower() switch
        {
            "error" => "خطا",
            "warning" => "هشدار",
            "information" => "اطلاعات",
            "debug" => "دیباگ",
            _ => Level
        };
    }

    public class GeneralLogPageDto
    {
        public List<GeneralLogDto> GeneralLogs { get; set; } = new();
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public int TotalCount { get; set; }
        public bool HasPreviousPage => CurrentPage > 1;
        public bool HasNextPage => CurrentPage < TotalPages;
    }

    public class ErrorLogDto
    {
        public string Message { get; set; } = string.Empty;
        public string? Exception { get; set; }
        public string? StackTrace { get; set; }
        public DateTime Timestamp { get; set; }
        public string? Source { get; set; }
        public string? Properties { get; set; }
        public string RelativeTime { get; set; } = string.Empty;
        public string FormattedDate { get; set; } = string.Empty;
    }

    public class ErrorLogPageDto
    {
        public List<ErrorLogDto> ErrorLogs { get; set; } = new();
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public int TotalCount { get; set; }
        public bool HasPreviousPage => CurrentPage > 1;
        public bool HasNextPage => CurrentPage < TotalPages;
    }
}
