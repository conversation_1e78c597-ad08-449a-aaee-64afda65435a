<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Areas\Controllers\**" />
    <Compile Remove="wwwroot\assets\NewFolder\**" />
    <Content Remove="Areas\Controllers\**" />
    <Content Remove="wwwroot\assets\NewFolder\**" />
    <EmbeddedResource Remove="Areas\Controllers\**" />
    <EmbeddedResource Remove="wwwroot\assets\NewFolder\**" />
    <None Remove="Areas\Controllers\**" />
    <None Remove="wwwroot\assets\NewFolder\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.16">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Keleid.BLL\Keleid.BLL.csproj" />
    <ProjectReference Include="..\Keleid.DAL\Keleid.DAL.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\admin-assets\" />
  </ItemGroup>

</Project>
