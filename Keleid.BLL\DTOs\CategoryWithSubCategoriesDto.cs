namespace Keleid.BLL.DTOs
{
    public class CategoryWithSubCategoriesDto
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string Slug { get; set; }
        public string IconClass { get; set; } = "fas fa-folder"; // Default icon
        public List<SubCategoryDto> SubCategories { get; set; } = new List<SubCategoryDto>();
    }

    public class SubCategoryDto
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string Slug { get; set; }
        public int ParentCategoryId { get; set; }
    }
}
