﻿@model Keleid.Web.ViewModels.FavsViewModel

@Html.AntiForgeryToken()

<!-- Main Content -->
<div class="container py-4">
    <div class="row">
        <!-- Sidebar -->
        @await Component.InvokeAsync("UserSidebar")

        <!-- Main Content Area -->
        <div class="col-lg-9">
            <div class="bg-white rounded-3 shadow-sm p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="mb-0">آگهی‌های نشان شده</h5>
                    <div class="text-muted small">
                        <i class="fas fa-bookmark"></i>
                        @Model.TotalCount آگهی نشان شده
                    </div>
                </div>

                <!-- آگهی‌ها -->
                <div class="fav-ads-list">
                    @if (Model.Favorites.Any())
                    {
                        @foreach (var favorite in Model.Favorites)
                        {
                            <a href="@Url.Action("Ads", "Home", new { id = favorite.Advertisement.Id, slug = favorite.Advertisement.Slug })" class="fav-ad-item">
                                <div class="row">
                                    <div class="col-md-3">
                                        <img src="@favorite.Advertisement.MainImageUrl" class="img-fluid rounded" alt="تصویر آگهی">
                                    </div>
                                    <div class="col-md-9">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="mb-0">@favorite.Advertisement.Title</h6>
                                            <button class="btn btn-link text-danger p-0" data-bs-toggle="modal" data-bs-target="#removeFavoriteModal" data-ad-id="@favorite.Advertisement.Id" data-ad-title="@favorite.Advertisement.Title" onclick="event.preventDefault(); event.stopPropagation();">
                                                <i class="fas fa-bookmark"></i>
                                            </button>
                                        </div>
                                        <p class="text-success mb-2">@favorite.Advertisement.FormattedPrice</p>
                                        <div class="d-flex align-items-center text-muted small mb-3">
                                            <i class="fas fa-location-dot ms-1"></i>
                                            @favorite.Advertisement.LocationDisplay
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <small class="text-muted">نشان شده در: @favorite.GetFavoriteTime()</small>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-bookmark fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">هنوز آگهی‌ای نشان نکرده‌اید</h6>
                            <p class="text-muted">آگهی‌های مورد علاقه خود را نشان کنید تا بعداً به راحتی پیدا کنید</p>
                            <a href="@Url.Action("Index", "Home")" class="btn btn-danger">
                                <i class="fas fa-search me-2"></i>
                                مشاهده آگهی‌ها
                            </a>
                        </div>
                    }
                </div>

                <!-- Pagination -->
                @if (Model.TotalPages > 1)
                {
                    <nav aria-label="صفحه‌بندی آگهی‌های نشان شده" class="mt-4">
                        <ul class="pagination justify-content-center custom-pagination">
                            <!-- Previous Page -->
                            <li class="page-item @(Model.HasPreviousPage ? "" : "disabled")">
                                <a class="page-link" href="@(Model.HasPreviousPage ? Url.Action("Favs", new { page = Model.PreviousPage }) : "#")" tabindex="@(Model.HasPreviousPage ? "" : "-1")" aria-disabled="@(Model.HasPreviousPage ? "false" : "true")">
                                    <i class="fas fa-chevron-right"></i>
                                    <span class="d-none d-sm-inline ms-1">قبلی</span>
                                </a>
                            </li>

                            <!-- First Page -->
                            @if (Model.ShowStartEllipsis)
                            {
                                <li class="page-item">
                                    <a class="page-link" href="@Url.Action("Favs", new { page = 1 })">1</a>
                                </li>
                                <li class="page-item">
                                    <span class="page-link">...</span>
                                </li>
                            }

                            <!-- Page Range -->
                            @foreach (var pageNum in Model.GetPageRange())
                            {
                                @if (pageNum == Model.CurrentPage)
                                {
                                    <li class="page-item active" aria-current="page">
                                        <a class="page-link" href="@Url.Action("Favs", new { page = pageNum })">@pageNum</a>
                                    </li>
                                }
                                else
                                {
                                    <li class="page-item">
                                        <a class="page-link" href="@Url.Action("Favs", new { page = pageNum })">@pageNum</a>
                                    </li>
                                }
                            }

                            <!-- Last Page -->
                            @if (Model.ShowEndEllipsis)
                            {
                                <li class="page-item">
                                    <span class="page-link">...</span>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="@Url.Action("Favs", new { page = Model.TotalPages })">@Model.TotalPages</a>
                                </li>
                            }

                            <!-- Next Page -->
                            <li class="page-item @(Model.HasNextPage ? "" : "disabled")">
                                <a class="page-link" href="@(Model.HasNextPage ? Url.Action("Favs", new { page = Model.NextPage }) : "#")" tabindex="@(Model.HasNextPage ? "" : "-1")" aria-disabled="@(Model.HasNextPage ? "false" : "true")">
                                    <span class="d-none d-sm-inline me-1">بعدی</span>
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                }
            </div>
        </div>
    </div>
</div>

<!-- Modal حذف از نشان شده‌ها -->
<div class="modal fade" id="removeFavoriteModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0">
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center py-4">
                <div class="mb-4">
                    <i class="fas fa-bookmark text-warning fa-3x"></i>
                </div>
                <h5 class="mb-3">آیا از حذف این آگهی از نشان شده‌ها مطمئن هستید؟</h5>
                <p class="text-muted mb-2"><strong id="favAdTitleToRemove"></strong></p>
                <p class="text-muted">این آگهی از لیست نشان شده‌های شما حذف خواهد شد.</p>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">انصراف</button>
                <button type="button" class="btn btn-danger" id="confirmRemoveFavBtn">
                    <span class="btn-text">حذف از نشان شده‌ها</span>
                    <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
@await Html.PartialAsync("_Footer")

<script>
document.addEventListener('DOMContentLoaded', function() {
    let adIdToRemove = null;

    // Handle remove favorite button click
    document.querySelectorAll('[data-bs-target="#removeFavoriteModal"]').forEach(function(button) {
        button.addEventListener('click', function() {
            adIdToRemove = this.getAttribute('data-ad-id');
            const adTitle = this.getAttribute('data-ad-title');

            // Update modal content
            document.getElementById('favAdTitleToRemove').textContent = adTitle;
        });
    });

    // Handle confirm remove button
    document.getElementById('confirmRemoveFavBtn').addEventListener('click', function() {
        if (!adIdToRemove) return;

        removeFavorite(adIdToRemove, this);
    });

    function removeFavorite(adId, buttonElement) {
        // Show loading state
        const btnText = buttonElement.querySelector('.btn-text');
        const spinner = buttonElement.querySelector('.spinner-border');

        buttonElement.disabled = true;
        btnText.textContent = 'در حال حذف...';
        spinner.classList.remove('d-none');

        // Create form data
        const formData = new FormData();
        formData.append('id', adId);
        formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

        // Send remove request
        fetch('@Url.Action("RemoveFavorite", "User")', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                showToast('success', data.message);

                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('removeFavoriteModal'));
                modal.hide();

                // Reload page after a short delay to show updated list
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                // Show error message
                showToast('error', data.message);

                // Reset button state
                buttonElement.disabled = false;
                btnText.textContent = 'حذف از نشان شده‌ها';
                spinner.classList.add('d-none');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('error', 'خطا در ارتباط با سرور');

            // Reset button state
            buttonElement.disabled = false;
            btnText.textContent = 'حذف از نشان شده‌ها';
            spinner.classList.add('d-none');
        });
    }

    // Toast notification function
    function showToast(type, message) {
        // Create toast element
        const toastHtml = `
            <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;

        // Add toast to container
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '1055';
            document.body.appendChild(toastContainer);
        }

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        // Show toast
        const toastElement = toastContainer.lastElementChild;
        const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
        toast.show();

        // Remove toast element after it's hidden
        toastElement.addEventListener('hidden.bs.toast', function() {
            this.remove();
        });
    }
});
</script>