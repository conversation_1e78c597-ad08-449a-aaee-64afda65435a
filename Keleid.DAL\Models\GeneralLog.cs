namespace Keleid.DAL.Models
{
    public class GeneralLog
    {
        public int Id { get; set; }
        public string Level { get; set; } = string.Empty; // Information, Warning, Debug, etc.
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string? Exception { get; set; }
        public string? Properties { get; set; } // JSON string for additional properties
        public string? Source { get; set; } // Source of the log (Controller, Service, etc.)
        public string? UserId { get; set; } // User who triggered the action (if applicable)
        public string? IpAddress { get; set; }
        public string? UserAgent { get; set; }
        public string? RequestPath { get; set; }
    }
}
