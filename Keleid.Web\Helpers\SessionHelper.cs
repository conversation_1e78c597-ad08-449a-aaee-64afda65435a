using Keleid.Web.ViewModels;
using System.Text.Json;

namespace Keleid.Web.Helpers
{
    public static class SessionHelper
    {
        private const string CREATE_AD_SESSION_KEY = "CreateAdvertisement";

        /// <summary>
        /// ذخیره اطلاعات ثبت آگهی در Session
        /// </summary>
        public static void SetCreateAdvertisementData(ISession session, CreateAdvertisementViewModel model)
        {
            var json = JsonSerializer.Serialize(model);
            session.SetString(CREATE_AD_SESSION_KEY, json);
        }

        /// <summary>
        /// دریافت اطلاعات ثبت آگهی از Session
        /// </summary>
        public static CreateAdvertisementViewModel? GetCreateAdvertisementData(ISession session)
        {
            var json = session.GetString(CREATE_AD_SESSION_KEY);
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<CreateAdvertisementViewModel>(json);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// بروزرسانی مرحله خاصی از اطلاعات ثبت آگهی
        /// </summary>
        public static void UpdateCreateAdvertisementStep(ISession session, int step, Action<CreateAdvertisementViewModel> updateAction)
        {
            var model = GetCreateAdvertisementData(session) ?? new CreateAdvertisementViewModel();
            model.CurrentStep = step;
            updateAction(model);
            SetCreateAdvertisementData(session, model);
        }

        /// <summary>
        /// پاک کردن اطلاعات ثبت آگهی از Session
        /// </summary>
        public static void ClearCreateAdvertisementData(ISession session)
        {
            session.Remove(CREATE_AD_SESSION_KEY);
        }

        /// <summary>
        /// بررسی اینکه آیا کاربر در فرآیند ثبت آگهی است یا نه
        /// </summary>
        public static bool IsInCreateAdvertisementProcess(ISession session)
        {
            return !string.IsNullOrEmpty(session.GetString(CREATE_AD_SESSION_KEY));
        }

        /// <summary>
        /// بررسی اینکه آیا کاربر مجاز به دسترسی به مرحله خاصی است یا نه
        /// </summary>
        public static bool CanAccessStep(ISession session, int requestedStep)
        {
            var model = GetCreateAdvertisementData(session);
            if (model == null) return requestedStep == 1;

            // کاربر فقط می‌تواند به مرحله فعلی یا مراحل قبلی دسترسی داشته باشد
            return requestedStep <= model.CurrentStep;
        }
    }
}
