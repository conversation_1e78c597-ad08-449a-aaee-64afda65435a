using Keleid.BLL.Services.Interfaces;
using Keleid.DAL;
using Keleid.DAL.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PARSGREEN.CORE.RESTful.SMS;

namespace Keleid.BLL.Services
{
    public class SmsService : ISmsService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<SmsService> _logger;

        public SmsService(ApplicationDbContext dbContext, ILogger<SmsService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<bool> SendVerificationCodeAsync(string phoneNumber, string ipAddress)
        {
            try
            {
                // بررسی محدودیت IP
                var canSendFromIp = await CanSendFromIpAsync(ipAddress);
                if (!canSendFromIp)
                {
                    _logger.LogWarning("SMS sending blocked due to IP rate limit for IP: {IpAddress}", ipAddress);
                    return false;
                }

                // بررسی محدودیت زمانی ارسال برای شماره تلفن
                var canSend = await CanSendNewCodeAsync(phoneNumber);
                if (!canSend)
                {
                    _logger.LogWarning("SMS sending blocked due to cooldown for phone: {PhoneNumber}", phoneNumber);
                    return false;
                }

                // تولید کد تایید
                var verificationCode = GenerateVerificationCode();

                // متن پیامک
                var message = $"کد تایید شما: {verificationCode}\nکلید - Keleid";

                // ذخیره در دیتابیس با IP Address
                var saveResult = await SaveSmsAsync(phoneNumber, message, ipAddress);
                if (!saveResult)
                {
                    _logger.LogError("Failed to save SMS to database for phone: {PhoneNumber}", phoneNumber);
                    return false;
                }

                // TODO: اینجا باید کد ارسال واقعی پیامک نوشته شود
                // مثلاً فراخوانی API ارائه‌دهنده پیامک
                var sendResult = await SendSmsToProviderAsync(phoneNumber, message);

                _logger.LogInformation("Verification code sent to {PhoneNumber} from IP: {IpAddress}, Code: {Code}", phoneNumber, ipAddress, verificationCode);
                return sendResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending verification code to {PhoneNumber} from IP: {IpAddress}", phoneNumber, ipAddress);
                return false;
            }
        }

        public async Task<bool> VerifyCodeAsync(string phoneNumber, string code)
        {
            try
            {
                var validityPeriod = TimeSpan.FromMinutes(2);
                var currentTime = DateTime.UtcNow;

                var latestSms = await GetLatestSmsAsync(phoneNumber);

                if (latestSms == null)
                {
                    _logger.LogWarning("No SMS found for phone number: {PhoneNumber}", phoneNumber);
                    return false;
                }

                // بررسی انقضای زمان
                if (currentTime - latestSms.Date > validityPeriod)
                {
                    _logger.LogWarning("SMS code expired for phone number: {PhoneNumber}", phoneNumber);
                    return false;
                }

                // بررسی صحت کد
                var isCodeValid = latestSms.Body.Contains(code);
                
                if (isCodeValid)
                {
                    _logger.LogInformation("SMS code verified successfully for phone: {PhoneNumber}", phoneNumber);
                }
                else
                {
                    _logger.LogWarning("Invalid SMS code for phone: {PhoneNumber}", phoneNumber);
                }

                return isCodeValid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying SMS code for {PhoneNumber}", phoneNumber);
                return false;
            }
        }

        public string GenerateVerificationCode()
        {
            var random = new Random();
            return random.Next(10000, 99999).ToString();
        }

        public async Task<bool> SaveSmsAsync(string phoneNumber, string message)
        {
            return await SaveSmsAsync(phoneNumber, message, null);
        }

        public async Task<bool> SaveSmsAsync(string phoneNumber, string message, string? ipAddress)
        {
            try
            {
                var sms = new Sms
                {
                    Phone = phoneNumber,
                    Body = message,
                    Date = DateTime.UtcNow,
                    IpAddress = ipAddress
                };

                _dbContext.Smses.Add(sms);
                await _dbContext.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving SMS to database for {PhoneNumber}", phoneNumber);
                return false;
            }
        }

        public async Task<Sms?> GetLatestSmsAsync(string phoneNumber)
        {
            try
            {
                return await _dbContext.Smses
                    .Where(s => s.Phone == phoneNumber)
                    .OrderByDescending(s => s.Date)
                    .FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting latest SMS for {PhoneNumber}", phoneNumber);
                return null;
            }
        }

        public async Task<bool> CanSendNewCodeAsync(string phoneNumber)
        {
            try
            {
                var latestSms = await GetLatestSmsAsync(phoneNumber);

                if (latestSms == null)
                {
                    // هیچ پیامکی ارسال نشده، می‌توان ارسال کرد
                    return true;
                }

                var timeSinceLastSms = DateTime.UtcNow - latestSms.Date;
                var cooldownPeriod = TimeSpan.FromMinutes(2); // 2 دقیقه محدودیت

                // اگر از آخرین پیامک 2 دقیقه گذشته باشد، می‌توان ارسال کرد
                bool canSend = timeSinceLastSms >= cooldownPeriod;

                if (!canSend)
                {
                    var remainingTime = cooldownPeriod - timeSinceLastSms;
                    _logger.LogWarning("SMS cooldown active for {PhoneNumber}. Remaining time: {RemainingSeconds} seconds",
                        phoneNumber, remainingTime.TotalSeconds);
                }

                return canSend;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking SMS cooldown for {PhoneNumber}", phoneNumber);
                // در صورت خطا، اجازه ارسال ندهیم تا امنیت حفظ شود
                return false;
            }
        }

        public async Task<bool> CanSendFromIpAsync(string ipAddress)
        {
            try
            {
                const int maxSmsPerHour = 3; // حداکثر 3 پیامک در ساعت از هر IP

                var smsCount = await GetSmsCountFromIpInLastHourAsync(ipAddress);

                bool canSend = smsCount < maxSmsPerHour;

                if (!canSend)
                {
                    _logger.LogWarning("IP rate limit exceeded for {IpAddress}. SMS count in last hour: {SmsCount}",
                        ipAddress, smsCount);
                }

                return canSend;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking IP rate limit for {IpAddress}", ipAddress);
                // در صورت خطا، اجازه ارسال ندهیم تا امنیت حفظ شود
                return false;
            }
        }

        public async Task<int> GetSmsCountFromIpInLastHourAsync(string ipAddress)
        {
            try
            {
                var oneHourAgo = DateTime.UtcNow.AddHours(-1);

                var count = await _dbContext.Smses
                    .Where(s => s.IpAddress == ipAddress && s.Date >= oneHourAgo)
                    .CountAsync();

                return count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting SMS count for IP {IpAddress}", ipAddress);
                // در صورت خطا، عدد بالا برگردانیم تا محدودیت اعمال شود
                return int.MaxValue;
            }
        }

        /// <summary>
        /// دریافت اعتبار باقی‌مانده پنل پیامک
        /// </summary>
        /// <returns>اعتبار به ریال</returns>
        public async Task<long> GetSmsBalanceAsync()
        {
            try
            {
                // استفاده از کلاس SendSMS برای دریافت اعتبار
                var balanceString = await Task.Run(() => SendSMS.GetCredit());

                if (string.IsNullOrEmpty(balanceString))
                {
                    _logger.LogWarning("Failed to retrieve SMS balance - empty response");
                    return 0;
                }

                // استخراج عدد از رشته (حذف "ریال" و کاما)
                var cleanBalance = balanceString.Replace("ریال", "").Replace(",", "").Trim();

                if (long.TryParse(cleanBalance, out long balance))
                {
                    _logger.LogInformation("SMS balance retrieved: {Balance} Rials", balance);
                    return balance;
                }
                else
                {
                    _logger.LogWarning("Failed to parse SMS balance: {BalanceString}", balanceString);
                    return 0;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting SMS balance: {ErrorMessage}", ex.Message);
                // در صورت خطا، صفر برمی‌گردانیم
                return 0;
            }
        }

        /// <summary>
        /// ارسال پیامک به ارائه‌دهنده خدمات پیامک
        /// </summary>
        /// <param name="phoneNumber">شماره تلفن</param>
        /// <param name="message">متن پیامک</param>
        /// <returns>موفقیت ارسال</returns>
        private async Task<bool> SendSmsToProviderAsync(string phoneNumber, string message)
        {
            try
            {
                // استفاده از کلاس SendSMS برای ارسال پیامک
                await Task.Run(() => SendSMS.Send(phoneNumber, message));

                _logger.LogInformation("SMS sent successfully to {PhoneNumber}", phoneNumber);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending SMS to provider for {PhoneNumber}: {ErrorMessage}", phoneNumber, ex.Message);
                return false;
            }
        }
    }

    public static class SendSMS
    {
        private const string APIKEY = "82B1E956-8363-4CC9-BA6A-87ED42955767";

        public static void Send(string phone, string text)
        {
            try
            {
                string message = text.Replace('ی', 'ي').Replace('ک', 'ك');

                string[] phones = [phone];

                string apikey = APIKEY;
                var _Message = new Message(apikey);

                _Message.SendSms(message, phones);

            }
            catch (Exception) 
            {
                throw new Exception("خطا در ارسال پیامک از سرویس دهنده");
            }
        }


        public static string GetCredit()
        {
            try
            {
                string apikey = APIKEY;
                var _Message = new Message(apikey);

                PARSGREEN.CORE.RESTful.SMS.User u = new PARSGREEN.CORE.RESTful.SMS.User(apikey);
                return u.Credit().Amount.ToString("N0") + " ریال";

            }
            catch (Exception) { return ""; }
        }
    }
}
