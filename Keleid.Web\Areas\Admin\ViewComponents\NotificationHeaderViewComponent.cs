using Keleid.BLL.Services;
using Microsoft.AspNetCore.Mvc;

namespace Keleid.Web.Areas.Admin.ViewComponents
{
    public class NotificationHeaderViewComponent : ViewComponent
    {
        private readonly NotificationService _notificationService;

        public NotificationHeaderViewComponent(NotificationService notificationService)
        {
            _notificationService = notificationService;
        }

        public async Task<IViewComponentResult> InvokeAsync()
        {
            var notifications = await _notificationService.GetUnreadNotificationsAsync(5);
            var unreadCount = await _notificationService.GetUnreadNotificationCountAsync();
            
            ViewBag.UnreadCount = unreadCount;
            
            return View(notifications);
        }
    }
}
