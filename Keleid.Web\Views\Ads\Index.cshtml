﻿@model List<Keleid.BLL.DTOs.CategoryDto>
@{
    ViewData["Title"] = "انتخاب دسته‌بندی";

    string GetIconClassForCategory(string categoryTitle)
    {
        return categoryTitle switch
        {
            "جایگاه سوخت" => "fas fa-gas-pump",
            "زمین با کاربری صنعتی" => "fas fa-industry",
            "سایت پرورش آبزیان" => "fas fa-fish",
            "سردخانه، انبار، سیلو" => "fas fa-warehouse",
            "سوله" => "fas fa-industry",
            "کارخانه صنعتی و تولیدی" => "fas fa-industry",
            "مجتمع کشت و صنعت" => "fas fa-tractor",
            "ماشین‌آلات خطوط تولید" => "fas fa-cogs",
            "مراکز درمانی" => "fas fa-hospital",
            "نیروگاه" => "fas fa-bolt",
            "محل اقامت" => "fas fa-hotel",
            "واحد پرورش طیور" => "fas fa-dove",
            "واحد دامداری" => "fas fa-cow",
            "واحد کشاورزی" => "fas fa-seedling",
            "واحد مرغداری" => "fas fa-kiwi-bird",
            _ => "fas fa-folder"
        };
    }
}

<!-- Header -->
@await Html.PartialAsync("_SecHeader","ثبت آگهی")

<div class="container my-5">
    <!-- Step Indicator -->
    <div class="step-indicator mb-4">
        <div class="step-line"></div>
        <div class="step active">
            <div class="step-circle">1</div>
            <div class="step-title">دسته‌بندی</div>
        </div>
        <div class="step">
            <div class="step-circle">2</div>
            <div class="step-title">مشخصات آگهی</div>
        </div>
        <div class="step">
            <div class="step-circle">3</div>
            <div class="step-title">تصاویر</div>
        </div>
        <div class="step">
            <div class="step-circle">4</div>
            <div class="step-title">موقعیت و تماس</div>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <h5 class="card-title mb-4">انتخاب دسته‌بندی آگهی</h5>

            @if (TempData["Error"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    @TempData["Error"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <!-- دکمه برگشت -->
            <button class="btn btn-outline-secondary mb-3 back-button">
                <i class="fas fa-arrow-right ml-1"></i>
                بازگشت به دسته‌بندی‌های اصلی
            </button>

            <!-- لیست دسته‌بندی‌ها -->
            <div class="category-list">
                @if (Model != null && Model.Any())
                {
                    @foreach (var category in Model.Where(c => c.ParentCategoryId == null))
                    {
                        <div class="category-item" data-category="@category.Id">
                            <i class="@GetIconClassForCategory(category.Title)"></i>
                            @category.Title
                            <i class="fas fa-chevron-left float-start"></i>
                        </div>
                    }
                }
            </div>

            <!-- زیردسته‌ها -->
            @if (Model != null && Model.Any())
            {
                @foreach (var parentCategory in Model.Where(c => c.ParentCategoryId == null))
                {
                    var subCategories = Model.Where(c => c.ParentCategoryId == parentCategory.Id).ToList();
                    if (subCategories.Any())
                    {
                        <div class="subcategory-list" data-parent="@parentCategory.Id">
                            @foreach (var subCategory in subCategories)
                            {
                                <div class="category-item" data-category="@subCategory.Id">
                                    <i class="@GetIconClassForCategory(parentCategory.Title)"></i>
                                    @subCategory.Title
                                </div>
                            }
                        </div>
                    }
                }
            }

            <!-- دکمه ادامه -->
            <div class="text-start mt-4">
                <button class="btn btn-danger px-4" id="nextStep" disabled>
                    ادامه
                    <i class="fas fa-arrow-left mr-1"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const categoryItems = document.querySelectorAll('.category-item');
        const backButton = document.querySelector('.back-button');
        const nextButton = document.getElementById('nextStep');
        let selectedCategory = null;

        // مخفی کردن دکمه برگشت در ابتدا
        backButton.style.display = 'none';

        // مخفی کردن تمام زیردسته‌ها در ابتدا
        document.querySelectorAll('.subcategory-list').forEach(list => {
            list.style.display = 'none';
        });

        // نمایش زیردسته‌ها
        categoryItems.forEach(item => {
            if (!item.querySelector('.float-start')) { // اگر آیتم زیردسته است
                item.addEventListener('click', function() {
                    // حذف انتخاب قبلی
                    document.querySelectorAll('.category-item').forEach(cat => {
                        cat.classList.remove('selected');
                    });

                    // انتخاب آیتم جدید
                    this.classList.add('selected');
                    selectedCategory = this.dataset.category;
                    nextButton.disabled = false;

                    console.log('Selected category:', selectedCategory);
                });
            } else { // اگر دسته اصلی است
                item.addEventListener('click', function() {
                    const parentId = this.dataset.category;

                    // مخفی کردن تمام زیردسته‌ها
                    document.querySelectorAll('.subcategory-list').forEach(list => {
                        list.style.display = 'none';
                    });

                    // نمایش زیردسته‌های مربوط به دسته انتخاب شده
                    const targetSubcategory = document.querySelector(`.subcategory-list[data-parent="${parentId}"]`);
                    if (targetSubcategory) {
                        targetSubcategory.style.display = 'block';
                        document.querySelector('.category-list').style.display = 'none';
                        backButton.style.display = 'block';
                    }
                });
            }
        });

        // دکمه برگشت
        backButton.addEventListener('click', function() {
            document.querySelectorAll('.subcategory-list').forEach(list => {
                list.style.display = 'none';
            });
            document.querySelector('.category-list').style.display = 'block';
            backButton.style.display = 'none';
            nextButton.disabled = true;
            selectedCategory = null;

            // حذف انتخاب
            document.querySelectorAll('.category-item').forEach(cat => {
                cat.classList.remove('selected');
            });
        });

        // دکمه ادامه
        nextButton.addEventListener('click', function() {
            if (selectedCategory) {
                // انتقال به صفحه بعدی با ارسال categoryId
                window.location.href = `/Ads/Details?categoryId=${selectedCategory}`;
            }
        });
    });
</script>

<style>
    .subcategory-list {
        display: none;
    }

    .category-item {
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        margin-bottom: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .category-item:hover {
        background-color: #f8f9fa;
        border-color: #dc3545;
    }

    .category-item.selected {
        background-color: #dc3545;
        color: white;
        border-color: #dc3545;
    }

    .category-item i {
        margin-left: 10px;
    }

    .back-button {
        display: none;
    }

    .back-button.show {
        display: inline-block;
    }
</style>