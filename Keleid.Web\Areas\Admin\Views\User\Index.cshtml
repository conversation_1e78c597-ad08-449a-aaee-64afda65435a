﻿@model Keleid.BLL.DTOs.UserPageDto
@{
    ViewBag.Title = "لیست کاربران";
}

<!-- Left Sidebar -->
@await Html.PartialAsync("_LeftSidebar")

<!-- Main Content -->
<section class="content home">

    <!-- Breadcrumb -->
    @await Html.PartialAsync("_Breadcrumb")

    <div class="container-fluid">
        <!-- Basic Table -->
        <div class="row clearfix">
            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="card">
                    <div class="header">
                        <h2>لیست <strong>کاربران</strong></h2>
                        <ul class="header-dropdown m-r--5">
                            <li class="dropdown">
                                <span class="badge badge-info">@Model.TotalCount کاربر</span>
                            </li>
                        </ul>
                    </div>
                    <div class="body table-responsive">
                        @if (Model.Users.Any())
                        {
                            <table class="table table-striped m-b-0">
                                <thead>
                                    <tr>
                                        <th>شماره تماس</th>
                                        <th>تاریخ ثبت‌نام</th>
                                        <th>وضعیت</th>
                                        <th>تایید شماره</th>
                                        <th>تعداد آگهی</th>
                                        <th>عملیات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var user in Model.Users)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@user.PhoneNumber</strong>
                                                @if (!string.IsNullOrEmpty(user.Email))
                                                {
                                                    <br><small class="text-muted">@user.Email</small>
                                                }
                                            </td>
                                            <td>
                                                <span>@user.RelativeRegisterTime</span>
                                                <br><small class="text-muted">@user.RegisterDate.ToString("yyyy/MM/dd HH:mm")</small>
                                            </td>
                                            <td>
                                                <span class="badge @user.StatusClass">@user.StatusText</span>
                                            </td>
                                            <td>
                                                <span class="badge @(user.PhoneNumberConfirmed ? "badge-success" : "badge-warning")">
                                                    @(user.PhoneNumberConfirmed ? "تایید شده" : "تایید نشده")
                                                </span>
                                            </td>
                                            <td>
                                                <div class="d-flex flex-column">
                                                    <span class="badge badge-primary mb-1">کل: @user.AdvertisementCount</span>
                                                    @if (user.ApprovedAdvertisementCount > 0)
                                                    {
                                                        <span class="badge badge-success mb-1">تایید: @user.ApprovedAdvertisementCount</span>
                                                    }
                                                    @if (user.PendingAdvertisementCount > 0)
                                                    {
                                                        <span class="badge badge-warning">انتظار: @user.PendingAdvertisementCount</span>
                                                    }
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="@Url.Action("Details", new { id = user.Id })"
                                                       class="btn btn-sm btn-info" title="جزئیات">
                                                        <i class="fas fa-eye"></i> جزئیات
                                                    </a>
                                                    <form method="post" action="@Url.Action("ToggleStatus")" style="display: inline;">
                                                        @Html.AntiForgeryToken()
                                                        <input type="hidden" name="id" value="@user.Id" />
                                                        <button type="submit"
                                                                class="btn btn-sm @(user.IsActive ? "btn-danger" : "btn-success")"
                                                                title="@(user.IsActive ? "غیرفعال سازی" : "فعال سازی")"
                                                                onclick="return confirm('@(user.IsActive ? "آیا از غیرفعال سازی این کاربر اطمینان دارید؟" : "آیا از فعال سازی این کاربر اطمینان دارید؟")')">
                                                            <i class="fas @(user.IsActive ? "fa-ban" : "fa-check")"></i>
                                                            @(user.IsActive ? "غیرفعال" : "فعال")
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>

                            <!-- Pagination -->
                            @if (Model.TotalPages > 1)
                            {
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <div>
                                        <small class="text-muted">
                                            نمایش @((Model.CurrentPage - 1) * 10 + 1) تا @(Math.Min(Model.CurrentPage * 10, Model.TotalCount)) از @Model.TotalCount کاربر
                                        </small>
                                    </div>
                                    <nav>
                                        <ul class="pagination pagination-sm">
                                            @if (Model.HasPreviousPage)
                                            {
                                                <li class="page-item">
                                                    <a class="page-link" href="@Url.Action("Index", new { page = Model.CurrentPage - 1 })">قبلی</a>
                                                </li>
                                            }

                                            @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                                            {
                                                <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                                    <a class="page-link" href="@Url.Action("Index", new { page = i })">@i</a>
                                                </li>
                                            }

                                            @if (Model.HasNextPage)
                                            {
                                                <li class="page-item">
                                                    <a class="page-link" href="@Url.Action("Index", new { page = Model.CurrentPage + 1 })">بعدی</a>
                                                </li>
                                            }
                                        </ul>
                                    </nav>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="alert alert-info text-center">
                                <i class="fas fa-info-circle"></i>
                                هیچ کاربری یافت نشد.
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

</section>