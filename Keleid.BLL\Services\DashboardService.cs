using Keleid.BLL.DTOs;
using Keleid.BLL.Services.Interfaces;
using Keleid.DAL;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Keleid.BLL.Services
{
    public class DashboardService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<DashboardService> _logger;
        private readonly ISmsService _smsService;

        public DashboardService(ApplicationDbContext context, ILogger<DashboardService> logger, ISmsService smsService)
        {
            _context = context;
            _logger = logger;
            _smsService = smsService;
        }

        public async Task<DashboardDto> GetDashboardDataAsync()
        {
            try
            {
                var dashboard = new DashboardDto();

                // ابتدا آمار را دریافت می‌کنیم
                dashboard.Stats = await GetDashboardStatsAsync();

                // سپس آگهی‌ها را دریافت می‌کنیم
                dashboard.LatestAdvertisements = await GetLatestAdvertisementsAsync();

                return dashboard;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard data");
                return new DashboardDto();
            }
        }

        private async Task<DashboardStatsDto> GetDashboardStatsAsync()
        {
            try
            {
                var today = DateTime.Today;
                var tomorrow = today.AddDays(1);

                // دریافت همزمان آمار و اعتبار پیامک
                var statsTask = GetBasicStatsAsync(today, tomorrow);
                var smsBalanceTask = _smsService.GetSmsBalanceAsync();

                await Task.WhenAll(statsTask, smsBalanceTask);

                var basicStats = await statsTask;
                var smsBalance = await smsBalanceTask;

                basicStats.SmsBalance = smsBalance;

                return basicStats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard stats");
                return new DashboardStatsDto();
            }
        }

        private async Task<DashboardStatsDto> GetBasicStatsAsync(DateTime today, DateTime tomorrow)
        {
            var stats = new DashboardStatsDto
            {
                TotalUsers = await _context.Users.CountAsync(),
                PendingAdvertisements = await _context.Advertisements.CountAsync(a => !a.IsApproved && !a.IsDeleted),
                ApprovedAdvertisements = await _context.Advertisements.CountAsync(a => a.IsApproved && !a.IsDeleted),
                UnreadNotifications = await _context.Notifications.CountAsync(n => !n.Status),
                PendingTasks = await _context.Tasks.CountAsync(t => !t.Status),
                TodayRegistrations = await _context.Users.CountAsync(u => u.RegisterDate >= today && u.RegisterDate < tomorrow),
                TodayAdvertisements = await _context.Advertisements.CountAsync(a => a.CreatedAt >= today && a.CreatedAt < tomorrow)
            };

            return stats;
        }

        private async Task<List<DashboardAdvertisementDto>> GetLatestAdvertisementsAsync()
        {
            try
            {
                // ابتدا بدون Include تست می‌کنیم
                var advertisements = await _context.Advertisements
                    .OrderByDescending(a => a.CreatedAt)
                    .Take(10)
                    .ToListAsync();

                if (advertisements.Count == 0)
                {
                    _logger.LogWarning("No advertisements found in database");
                    return new List<DashboardAdvertisementDto>();
                }

                // حالا Category و User را جداگانه load می‌کنیم
                var result = new List<DashboardAdvertisementDto>();

                foreach (var ad in advertisements)
                {
                    // Category را load می‌کنیم
                    var category = await _context.Categories
                        .Where(c => c.Id == ad.CategoryId)
                        .FirstOrDefaultAsync();

                    // User را load می‌کنیم
                    var user = await _context.Users
                        .Where(u => u.Id == ad.UserId)
                        .FirstOrDefaultAsync();

                    result.Add(new DashboardAdvertisementDto
                    {
                        Id = ad.Id,
                        Title = ad.Title ?? "بدون عنوان",
                        UserPhone = user?.PhoneNumber ?? "نامشخص",
                        CategoryTitle = category?.Title ?? "نامشخص",
                        IsApproved = ad.IsApproved,
                        IsDeleted = ad.IsDeleted,
                        CreatedAt = ad.CreatedAt,
                        RelativeTime = GetRelativeTime(ad.CreatedAt),
                        StatusText = GetStatusText(ad.IsApproved, ad.IsDeleted),
                        StatusClass = GetStatusClass(ad.IsApproved, ad.IsDeleted)
                    });
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting latest advertisements");
                return new List<DashboardAdvertisementDto>();
            }
        }

        private string GetRelativeTime(DateTime date)
        {
            var timeSpan = DateTime.Now - date;

            if (timeSpan.TotalMinutes < 15)
                return "لحظاتی پیش";
            else if (timeSpan.TotalMinutes < 30)
                return "ربع ساعت پیش";
            else if (timeSpan.TotalMinutes < 60)
                return "نیم ساعت پیش";
            else if (timeSpan.TotalHours < 2)
                return "یک ساعت پیش";
            else if (date.Date == DateTime.Today)
                return "امروز";
            else if (date.Date == DateTime.Today.AddDays(-1))
                return "دیروز";
            else
                return date.ToString("dd MMMM yyyy", new System.Globalization.CultureInfo("fa-IR"));
        }

        private string GetStatusText(bool isApproved, bool isDeleted)
        {
            if (isDeleted)
                return "حذف شده";
            else if (isApproved)
                return "تایید شده";
            else
                return "در انتظار تایید";
        }

        private string GetStatusClass(bool isApproved, bool isDeleted)
        {
            if (isDeleted)
                return "badge-danger";
            else if (isApproved)
                return "badge-success";
            else
                return "badge-warning";
        }
    }
}
