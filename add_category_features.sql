-- اسکریپت اضافه کردن ویژگی‌ها به زیردسته‌بندی‌ها (CategoryFeatures)
-- این اسکریپت ویژگی‌های مناسب برای هر زیردسته‌بندی را اضافه می‌کند
-- توجه: کاربران فقط می‌توانند از زیردسته‌بندی‌ها (آن‌هایی که والد دارند) استفاده کنند

-- حذف ویژگی‌های قبلی برای جلوگیری از تکرار
DELETE FROM CategoryFeatures WHERE CategoryId IN (230, 231, 232, 233, 239, 240, 241, 242, 243, 244, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273);

-- ویژگی‌های جایگاه C.N.G (CategoryId = 230)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(230, N'تعداد نازل CNG', 'number', 1),
(230, N'ظرفیت مخزن (متر مکعب)', 'number', 1),
(230, N'متراژ زمین (متر مربع)', 'number', 1),
(230, N'نوع مالکیت', 'select', 1),
(230, N'وضعیت مجوز', 'select', 1),
(230, N'تجهیزات ایمنی', 'text', 1),
(230, N'امکانات جانبی', 'text', 0);

-- ویژگی‌های جایگاه سوخت بنزین و گازوئیل (CategoryId = 231)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(231, N'تعداد نازل بنزین', 'number', 1),
(231, N'تعداد نازل گازوئیل', 'number', 1),
(231, N'متراژ زمین (متر مربع)', 'number', 1),
(231, N'ظرفیت مخازن (لیتر)', 'number', 1),
(231, N'نوع مالکیت', 'select', 1),
(231, N'وضعیت مجوز', 'select', 1),
(231, N'امکانات جانبی', 'text', 0);

-- ویژگی‌های زمین داخل شهرک صنعتی (CategoryId = 232)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(232, N'متراژ زمین (متر مربع)', 'number', 1),
(232, N'نوع کاربری', 'select', 1),
(232, N'وضعیت انشعابات', 'text', 1),
(232, N'عرض بر (متر)', 'number', 0),
(232, N'نوع مالکیت', 'select', 1),
(232, N'نام شهرک صنعتی', 'text', 1),
(232, N'موقعیت در شهرک', 'text', 0);

-- ویژگی‌های زمین خارج شهرک صنعتی (CategoryId = 233)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(233, N'متراژ زمین (متر مربع)', 'number', 1),
(233, N'نوع کاربری', 'select', 1),
(233, N'وضعیت انشعابات', 'text', 1),
(233, N'عرض بر (متر)', 'number', 0),
(233, N'نوع مالکیت', 'select', 1),
(233, N'فاصله تا شهرک صنعتی (کیلومتر)', 'number', 0),
(233, N'دسترسی به جاده اصلی', 'select', 1);

-- ویژگی‌های انبار (CategoryId = 239)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(239, N'متراژ زیربنا (متر مربع)', 'number', 1),
(239, N'ارتفاع سقف (متر)', 'number', 1),
(239, N'ظرفیت ذخیره‌سازی (تن)', 'number', 1),
(239, N'نوع کاربری', 'select', 1),
(239, N'تجهیزات بارگیری', 'text', 0),
(239, N'دسترسی خودرو', 'select', 1),
(239, N'امکانات جانبی', 'text', 0);

-- ویژگی‌های سردخانه بالای صفر (CategoryId = 240)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(240, N'متراژ زیربنا (متر مربع)', 'number', 1),
(240, N'ظرفیت ذخیره‌سازی (تن)', 'number', 1),
(240, N'دمای نگهداری (درجه سانتیگراد)', 'number', 1),
(240, N'سیستم سرمایش', 'select', 1),
(240, N'تجهیزات کنترل دما', 'text', 1),
(240, N'تجهیزات بارگیری', 'text', 0),
(240, N'نوع محصولات قابل نگهداری', 'text', 0);

-- ویژگی‌های سردخانه زیر صفر (CategoryId = 241)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(241, N'متراژ زیربنا (متر مربع)', 'number', 1),
(241, N'ظرفیت ذخیره‌سازی (تن)', 'number', 1),
(241, N'دمای نگهداری (درجه سانتیگراد)', 'number', 1),
(241, N'سیستم انجماد', 'select', 1),
(241, N'تجهیزات کنترل دما', 'text', 1),
(241, N'تجهیزات بارگیری', 'text', 0),
(241, N'نوع محصولات قابل نگهداری', 'text', 0);

-- ویژگی‌های سیلو گندم (CategoryId = 242)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(242, N'ظرفیت ذخیره‌سازی (تن)', 'number', 1),
(242, N'ارتفاع سیلو (متر)', 'number', 1),
(242, N'قطر سیلو (متر)', 'number', 1),
(242, N'سیستم تهویه', 'select', 1),
(242, N'سیستم بارگیری', 'text', 1),
(242, N'سیستم تخلیه', 'text', 1),
(242, N'تجهیزات کنترل رطوبت', 'text', 0);

-- ویژگی‌های سوله دست دوم (CategoryId = 243)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(243, N'متراژ زیربنا (متر مربع)', 'number', 1),
(243, N'ارتفاع سقف (متر)', 'number', 1),
(243, N'سال ساخت', 'number', 1),
(243, N'نوع سقف', 'text', 0),
(243, N'وضعیت سازه', 'select', 1),
(243, N'تجهیزات بارگیری', 'text', 0),
(243, N'دسترسی خودرو', 'select', 1);

-- ویژگی‌های واحدهای تولید و ساخت سوله (CategoryId = 244)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(244, N'متراژ زیربنا (متر مربع)', 'number', 1),
(244, N'متراژ زمین (متر مربع)', 'number', 1),
(244, N'ظرفیت تولید ماهانه', 'text', 1),
(244, N'نوع تولیدات', 'text', 1),
(244, N'تجهیزات تولید', 'text', 1),
(244, N'تعداد کارگر', 'number', 0),
(244, N'وضعیت مجوزها', 'text', 1);

-- ویژگی‌های پرورش اسب (CategoryId = 263)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(263, N'تعداد اسب', 'number', 1),
(263, N'متراژ زمین (هکتار)', 'number', 1),
(263, N'متراژ اصطبل (متر مربع)', 'number', 1),
(263, N'نژاد اسب', 'text', 1),
(263, N'امکانات ورزشی', 'text', 0),
(263, N'منابع آب', 'text', 1),
(263, N'دسترسی به علوفه', 'text', 1);

-- ویژگی‌های پرورش گاو پرواری (CategoryId = 264)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(264, N'ظرفیت نگهداری (راس)', 'number', 1),
(264, N'متراژ زمین (هکتار)', 'number', 1),
(264, N'متراژ سالن (متر مربع)', 'number', 1),
(264, N'نژاد گاو', 'text', 1),
(264, N'تجهیزات تغذیه', 'text', 0),
(264, N'منابع آب', 'text', 1),
(264, N'دسترسی به علوفه', 'text', 1);

-- ویژگی‌های پرورش گاو شیری (CategoryId = 265)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(265, N'ظرفیت نگهداری (راس)', 'number', 1),
(265, N'متراژ زمین (هکتار)', 'number', 1),
(265, N'متراژ سالن (متر مربع)', 'number', 1),
(265, N'نژاد گاو', 'text', 1),
(265, N'تجهیزات دوشش', 'text', 1),
(265, N'ظرفیت تولید شیر روزانه (لیتر)', 'number', 0),
(265, N'منابع آب', 'text', 1);

-- ویژگی‌های پرورش شتر (CategoryId = 266)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(266, N'تعداد شتر', 'number', 1),
(266, N'متراژ زمین (هکتار)', 'number', 1),
(266, N'نوع شتر', 'text', 1),
(266, N'امکانات نگهداری', 'text', 0),
(266, N'منابع آب', 'text', 1),
(266, N'دسترسی به علوفه', 'text', 1),
(266, N'شرایط آب و هوایی', 'text', 0);

-- ویژگی‌های پرورش گوسفند پرواری (CategoryId = 267)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(267, N'ظرفیت نگهداری (راس)', 'number', 1),
(267, N'متراژ زمین (هکتار)', 'number', 1),
(267, N'متراژ سالن (متر مربع)', 'number', 1),
(267, N'نژاد گوسفند', 'text', 1),
(267, N'تجهیزات تغذیه', 'text', 0),
(267, N'منابع آب', 'text', 1),
(267, N'دسترسی به علوفه', 'text', 1);

-- ویژگی‌های کشت سبزیجات (CategoryId = 268)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(268, N'متراژ زمین (هکتار)', 'number', 1),
(268, N'نوع سبزیجات', 'text', 1),
(268, N'نوع آبیاری', 'select', 1),
(268, N'منابع آب', 'text', 1),
(268, N'نوع خاک', 'text', 0),
(268, N'تجهیزات کشاورزی', 'text', 0),
(268, N'گلخانه', 'select', 0);

-- ویژگی‌های کشت صیفی‌جات (CategoryId = 269)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(269, N'متراژ زمین (هکتار)', 'number', 1),
(269, N'نوع محصول', 'text', 1),
(269, N'نوع آبیاری', 'select', 1),
(269, N'منابع آب', 'text', 1),
(269, N'نوع خاک', 'text', 0),
(269, N'تجهیزات کشاورزی', 'text', 0),
(269, N'فصل کاشت', 'text', 1);

-- ویژگی‌های کشت کلزا (CategoryId = 270)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(270, N'متراژ زمین (هکتار)', 'number', 1),
(270, N'نوع آبیاری', 'select', 1),
(270, N'منابع آب', 'text', 1),
(270, N'نوع خاک', 'text', 0),
(270, N'تجهیزات کشاورزی', 'text', 0),
(270, N'میزان تولید سالانه (تن)', 'number', 0),
(270, N'دسترسی به جاده', 'select', 1);

-- ویژگی‌های مرغداری گوشتی (CategoryId = 271)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(271, N'ظرفیت پرورش (قطعه)', 'number', 1),
(271, N'تعداد سالن', 'number', 1),
(271, N'متراژ هر سالن (متر مربع)', 'number', 1),
(271, N'سیستم تهویه', 'select', 1),
(271, N'سیستم آبخوری', 'select', 1),
(271, N'سیستم غذادهی', 'select', 1),
(271, N'تجهیزات کنترل دما', 'text', 0);

-- ویژگی‌های مرغداری مرغ تخمگذار (CategoryId = 272)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(272, N'ظرفیت پرورش (قطعه)', 'number', 1),
(272, N'تعداد سالن', 'number', 1),
(272, N'متراژ هر سالن (متر مربع)', 'number', 1),
(272, N'سیستم تهویه', 'select', 1),
(272, N'سیستم آبخوری', 'select', 1),
(272, N'سیستم غذادهی', 'select', 1),
(272, N'ظرفیت تولید تخم روزانه', 'number', 0);

-- ویژگی‌های واحد تولید جوجه یکروزه (CategoryId = 273)
INSERT INTO CategoryFeatures (CategoryId, Title, InputType, IsRequired) VALUES
(273, N'ظرفیت تولید ماهانه (قطعه)', 'number', 1),
(273, N'تعداد دستگاه جوجه‌کشی', 'number', 1),
(273, N'ظرفیت هر دستگاه', 'number', 1),
(273, N'سیستم کنترل دما و رطوبت', 'text', 1),
(273, N'تجهیزات نگهداری مولد', 'text', 1),
(273, N'آزمایشگاه کنترل کیفیت', 'select', 0),
(273, N'مجوزهای بهداشتی', 'text', 1);

-- نمایش خلاصه ویژگی‌های اضافه شده
PRINT N'ویژگی‌های زیردسته‌بندی‌ها با موفقیت اضافه شدند.';
PRINT N'';
PRINT N'=== خلاصه ویژگی‌های اضافه شده ===';
SELECT
    c.Id as N'شناسه',
    c.Title as N'زیردسته‌بندی',
    COUNT(cf.Id) as N'تعداد ویژگی'
FROM Categories c
LEFT JOIN CategoryFeatures cf ON c.Id = cf.CategoryId
WHERE c.Id IN (230, 231, 232, 233, 239, 240, 241, 242, 243, 244, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273)
GROUP BY c.Id, c.Title
ORDER BY c.Id;

-- نمایش جزئیات تمام ویژگی‌ها
PRINT N'';
PRINT N'=== جزئیات ویژگی‌ها ===';
SELECT
    c.Id as N'شناسه دسته',
    c.Title as N'زیردسته‌بندی',
    cf.Title as N'عنوان ویژگی',
    cf.InputType as N'نوع ورودی',
    CASE WHEN cf.IsRequired = 1 THEN N'اجباری' ELSE N'اختیاری' END as N'وضعیت'
FROM CategoryFeatures cf
INNER JOIN Categories c ON cf.CategoryId = c.Id
WHERE c.Id IN (230, 231, 232, 233, 239, 240, 241, 242, 243, 244, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273)
ORDER BY c.Id, cf.Id;
