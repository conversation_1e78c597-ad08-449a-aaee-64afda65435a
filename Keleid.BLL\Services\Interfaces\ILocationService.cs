using Keleid.BLL.DTOs;

namespace Keleid.BLL.Services.Interfaces
{
    public interface ILocationService
    {
        /// <summary>
        /// دریافت تمام استان‌ها
        /// </summary>
        /// <returns>لیست استان‌ها</returns>
        Task<List<ProvinceDto>> GetAllProvincesAsync();

        /// <summary>
        /// دریافت شهرهای یک استان
        /// </summary>
        /// <param name="provinceId">شناسه استان</param>
        /// <returns>لیست شهرهای استان</returns>
        Task<List<CityDto>> GetCitiesByProvinceIdAsync(int provinceId);

        /// <summary>
        /// دریافت شهرهای یک استان بر اساس نام استان
        /// </summary>
        /// <param name="provinceName">نام استان</param>
        /// <returns>لیست شهرهای استان</returns>
        Task<List<CityDto>> GetCitiesByProvinceNameAsync(string provinceName);

        /// <summary>
        /// دریافت استان بر اساس نام
        /// </summary>
        /// <param name="provinceName">نام استان</param>
        /// <returns>اطلاعات استان</returns>
        Task<ProvinceDto?> GetProvinceByNameAsync(string provinceName);

        /// <summary>
        /// دریافت شهر بر اساس نام
        /// </summary>
        /// <param name="cityName">نام شهر</param>
        /// <returns>اطلاعات شهر</returns>
        Task<CityDto?> GetCityByNameAsync(string cityName);
    }
}
