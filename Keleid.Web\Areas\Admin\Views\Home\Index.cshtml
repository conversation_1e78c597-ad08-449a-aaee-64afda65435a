﻿@model Keleid.BLL.DTOs.DashboardDto
@{
    ViewBag.Title = "داشبورد";
}

<!-- Left Sidebar -->
@await Html.PartialAsync("_LeftSidebar")

<!-- Main Content -->
<section class="content home">

    <!-- Breadcrumb -->
    @await Html.PartialAsync("_Breadcrumb")

    <div class="container-fluid">
        <div class="card widget_2">
            <ul class="row clearfix list-unstyled m-b-0">
                <li class="col-lg-3 col-md-6 col-sm-12">
                    <div class="body">
                        <div class="row">
                            <div class="col-7">
                                <h5 class="m-t-0">اعتبار پنل SMS</h5>
                            </div>
                            <div class="col-5 text-left">
                                <h2 style="font-size:20px">@Model.Stats.SmsBalance.ToString("N0")</h2>
                                <small class="info">ریال</small>
                            </div>
                            <div class="col-12">
                                <div class="progress m-t-20">
                                    <div class="progress-bar l-amber" role="progressbar" aria-valuenow="100"
                                         aria-valuemin="0" aria-valuemax="100" style="width: 100%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                <li class="col-lg-3 col-md-6 col-sm-12">
                    <div class="body">
                        <div class="row">
                            <div class="col-7">
                                <h5 class="m-t-0">کاربران سایت</h5>
                            </div>
                            <div class="col-5 text-left">
                                <h2 class="">@Model.Stats.TotalUsers.ToString("N0")</h2>
                                <small class="info">عدد</small>
                            </div>
                            <div class="col-12">
                                <div class="progress m-t-20">
                                    <div class="progress-bar l-parpl" role="progressbar" aria-valuenow="100"
                                         aria-valuemin="0" aria-valuemax="100" style="width: 100%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                <li class="col-lg-3 col-md-6 col-sm-12">
                    <div class="body">
                        <div class="row">
                            <div class="col-7">
                                <h5 class="m-t-0">در انتظار تایید</h5>
                            </div>
                            <div class="col-5 text-left">
                                <h2 class="">@Model.Stats.PendingAdvertisements.ToString("N0")</h2>
                                <small class="info">عدد</small>
                            </div>
                            <div class="col-12">
                                <div class="progress m-t-20">
                                    <div class="progress-bar l-blue" role="progressbar" aria-valuenow="100"
                                         aria-valuemin="0" aria-valuemax="100" style="width: 100%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                <li class="col-lg-3 col-md-6 col-sm-12">
                    <div class="body">
                        <div class="row">
                            <div class="col-7">
                                <h5 class="m-t-0">آگهی های فعال</h5>
                            </div>
                            <div class="col-5 text-left">
                                <h2 class="">@Model.Stats.ApprovedAdvertisements.ToString("N0")</h2>
                                <small class="info">عدد</small>
                            </div>
                            <div class="col-12">
                                <div class="progress m-t-20">
                                    <div class="progress-bar l-turquoise" role="progressbar" aria-valuenow="100"
                                         aria-valuemin="0" aria-valuemax="100" style="width: 100%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
        </div>

        <!-- آمار امروز -->
        <div class="row clearfix">
            <div class="col-lg-6 col-md-6 col-sm-12">
                <div class="card">
                    <div class="header">
                        <h2><strong>آمار امروز</strong></h2>
                    </div>
                    <div class="body">
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center">
                                    <h3 class="text-primary">@Model.Stats.TodayRegistrations</h3>
                                    <p class="mb-0">ثبت‌نام امروز</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <h3 class="text-success">@Model.Stats.TodayAdvertisements</h3>
                                    <p class="mb-0">آگهی امروز</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6 col-sm-12">
                <div class="card">
                    <div class="header">
                        <h2><strong>وضعیت سیستم</strong></h2>
                    </div>
                    <div class="body">
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center">
                                    <h3 class="text-warning">@Model.Stats.UnreadNotifications</h3>
                                    <p class="mb-0">اعلان خوانده نشده</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <h3 class="text-info">@Model.Stats.PendingTasks</h3>
                                    <p class="mb-0">وظیفه در انتظار</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row clearfix">
            <div class="col-md-12 col-sm-12 col-xs-12">
                <div class="card project_list">
                    <div class="header">
                        <h2>آخرین <strong>آگهی ها</strong></h2>
                        <ul class="header-dropdown m-r--5">
                            <li>
                                <a asp-area="Admin" asp-controller="Ads" asp-action="Index" class="btn btn-primary btn-sm">
                                    مشاهده همه آگهی‌ها
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th style="width:50px;">شماره</th>
                                        <th>کاربر</th>
                                        <th class="hidden-md-down">عنوان</th>
                                        <th> دسته بندی </th>
                                        <th>وضعیت</th>
                                        <th>تاریخ ثبت</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (Model.LatestAdvertisements.Any())
                                    {
                                        @foreach (var ad in Model.LatestAdvertisements)
                                        {
                                            <tr>
                                                <td>@ad.Id</td>
                                                <td>
                                                    <a class="single-user-name" href="#">@ad.UserPhone</a><br>
                                                </td>
                                                <td>
                                                    <strong>@ad.Title</strong><br>
                                                </td>
                                                <td class="hidden-md-down">
                                                    <p>@ad.CategoryTitle</p>
                                                </td>
                                                <td><span class="badge @ad.StatusClass">@ad.StatusText</span></td>
                                                <td>@ad.RelativeTime</td>
                                                <td>
                                                    <a asp-area="Admin" asp-controller="Ads" asp-action="Details" asp-route-id="@ad.Id"
                                                       class="btn btn-sm btn-primary">مشاهده</a>
                                                </td>
                                            </tr>
                                        }
                                    }
                                    else
                                    {
                                        <tr>
                                            <td colspan="7" class="text-center">
                                                آگهی‌ای وجود ندارد
                                                @if (ViewBag.DebugMessage != null)
                                                {
                                                    <br><small class="text-muted">@ViewBag.DebugMessage</small>
                                                }
                                                @if (ViewBag.ErrorMessage != null)
                                                {
                                                    <br><small class="text-danger">@ViewBag.ErrorMessage</small>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>