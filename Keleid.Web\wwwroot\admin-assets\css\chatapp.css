.chat-app .people-list {
    width: 280px;
    position: absolute;
    right: 0;
    top: 0;
    padding: 20px;
    z-index: 999
}

.chat-app .chat {
    margin-right: 280px;
    border-right: 1px solid #eaeaea
}

.chat-app .list_btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    padding: 0;
    width: 40px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    display: none;
    box-shadow: 0px 10px 25px 0px rgba(0, 0, 0, 0.3);
    border-radius: 3px
}

.people-list {
    -moz-transition: .5s;
    -o-transition: .5s;
    -webkit-transition: .5s;
    transition: .5s
}

.people-list .chat-list li {
    padding: 10px 15px;
    list-style: none;
    border-radius: 3px
}

.people-list .chat-list li:hover {
    background: #efefef;
    cursor: pointer
}

.people-list .chat-list li.active {
    background: #efefef
}

.people-list .chat-list li .name {
    font-size: 15px
}

.people-list .chat-list img {
    width: 45px;
    border-radius: 50%
}

.people-list img {
    float: right;
    border-radius: 50%
}

.people-list .about {
    float: right;
    padding-right: 8px
}

.people-list .status {
    color: #999;
    font-size: 13px
}

.chat .chat-header {
    padding: 20px;
    border-bottom: 2px solid white
}

.chat .chat-header img {
    float: right;
    border-radius: 50%;
    width: 45px
}

.chat .chat-header .chat-about {
    float: right;
    padding-right: 10px
}

.chat .chat-header .chat-with {
    font-size: 16px;
    font-family:iransans-bold
}

.chat .chat-header .chat-num-messages {
    color: 434651
}

.chat .chat-history {
    padding: 20px;
    border-bottom: 2px solid white
}

.chat .chat-history ul {
    padding: 0
}

.chat .chat-history ul li {
    list-style: none
}

.chat .chat-history .message-data {
    margin-bottom: 15px
}

.chat .chat-history .message-data .message-data-name {
    font-size: 16px;
    font-family:iransans-bold
}

.chat .chat-history .message-data-time {
    color: #434651;
    padding-right: 6px
}

.chat .chat-history .message {
    color: #444;
    padding: 18px 20px;
    line-height: 26px;
    font-size: 16px;
    border-radius: 7px;
    margin-bottom: 30px;
    width: 90%;
    position: relative
}

.chat .chat-history .message:after {
    bottom: 100%;
    right: 7%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-bottom-color: #fff;
    border-width: 10px;
    margin-right: -10px
}

.chat .chat-history .my-message {
    background: #e8e8e8
}

.chat .chat-history .my-message:after {
    bottom: 100%;
    left: 7%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-bottom-color: #e8e8e8;
    border-width: 10px;
    margin-left: -10px
}

.chat .chat-history .other-message {
    background: #d9e7ea
}

.chat .chat-history .other-message:after {
    border-bottom-color: #d9e7ea;
    right: 93%
}

.chat .chat-message {
    padding: 20px
}

.chat .chat-message textarea {
    width: 100%;
    border: none;
    padding: 10px 20px;
    margin-bottom: 10px;
    border-radius: 5px;
    resize: none
}

.chat .chat-message .fa-file-o,
.chat .chat-message .fa-file-image-o {
    font-size: 16px;
    color: gray;
    cursor: pointer
}

.online,
.offline,
.me {
    margin-left: 3px;
    font-size: 10px
}

.online {
    color: #86BB71
}

.offline {
    color: #E38968
}

.me {
    color: #0498bd
}

.float-right {
    float: right
}

.clearfix:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0
}

@media only screen and (max-width: 767px) {
    .chat-app {
        margin: 0
    }
    .chat-app .list_btn {
        display: block
    }
    .chat-app .people-list {
        height: 465px;
        width: 100%;
        overflow-x: auto;
        background: #fff;
        right: -400px
    }
    .chat-app .people-list.open {
        left: 0
    }
    .chat-app .chat {
        margin: 0
    }
    .chat-app .chat-history {
        height: 240px;
        overflow-x: auto
    }
}

@media only screen and (min-width: 768px) and (max-width: 992px) {
    .chat-app {
        margin: 0
    }
    .chat-app .chat-list {
        height: 650px;
        overflow-x: auto
    }
    .chat-app .chat-history {
        height: 600px;
        overflow-x: auto
    }
}

@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) and (orientation: landscape) and (-webkit-min-device-pixel-ratio: 1) {
    .chat-app {
        margin: 0
    }
    .chat-app .chat-list {
        height: 450px;
        overflow-x: auto
    }
    .chat-app .chat-history {
        height: 380px;
        overflow-x: auto
    }
}