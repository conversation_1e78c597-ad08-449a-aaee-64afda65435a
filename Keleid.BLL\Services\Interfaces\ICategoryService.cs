﻿using Keleid.BLL.DTOs;

namespace Keleid.BLL.Services.Interfaces
{
    public interface ICategoryService
    {
        /// <summary>
        /// دریافت تمام دسته‌بندی‌های اصلی همراه با زیردسته‌هایشان
        /// </summary>
        /// <returns>لیست دسته‌بندی‌های اصلی با زیردسته‌ها</returns>
        Task<List<CategoryWithSubCategoriesDto>> GetMainCategoriesWithSubCategoriesAsync();

        /// <summary>
        /// دریافت تمام دسته‌بندی‌های اصلی (بدون زیردسته‌ها)
        /// </summary>
        /// <returns>لیست دسته‌بندی‌های اصلی</returns>
        Task<List<CategoryDto>> GetMainCategoriesAsync();

        /// <summary>
        /// دریافت زیردسته‌های یک دسته‌بندی
        /// </summary>
        /// <param name="parentCategoryId">شناسه دسته‌بندی والد</param>
        /// <returns>لیست زیردسته‌ها</returns>
        Task<List<CategoryDto>> GetSubCategoriesAsync(int parentCategoryId);

        /// <summary>
        /// دریافت یک دسته‌بندی با شناسه
        /// </summary>
        /// <param name="categoryId">شناسه دسته‌بندی</param>
        /// <returns>دسته‌بندی</returns>
        Task<CategoryDto?> GetCategoryByIdAsync(int categoryId);

        /// <summary>
        /// دریافت تمام دسته‌بندی‌ها (اصلی و فرعی) به صورت ساده
        /// </summary>
        /// <returns>لیست تمام دسته‌بندی‌ها</returns>
        Task<List<CategoryDto>> GetAllCategoriesAsync();

        /// <summary>
        /// دریافت یک دسته‌بندی با slug
        /// </summary>
        /// <param name="slug">slug دسته‌بندی</param>
        /// <returns>دسته‌بندی</returns>
        Task<CategoryDto?> GetCategoryBySlugAsync(string slug);

        /// <summary>
        /// دریافت ویژگی‌های یک دسته‌بندی
        /// </summary>
        /// <param name="categoryId">شناسه دسته‌بندی</param>
        /// <returns>لیست ویژگی‌های دسته‌بندی</returns>
        Task<List<CategoryFeatureDto>> GetCategoryFeaturesAsync(int categoryId);

        /// <summary>
        /// به‌روزرسانی ویژگی‌های یک دسته‌بندی
        /// </summary>
        /// <param name="categoryId">شناسه دسته‌بندی</param>
        /// <param name="features">لیست ویژگی‌های جدید</param>
        /// <returns>نتیجه عملیات</returns>
        Task<bool> UpdateCategoryFeaturesAsync(int categoryId, List<UpdateCategoryFeatureDto> features);

        /// <summary>
        /// ایجاد دسته اصلی جدید
        /// </summary>
        /// <param name="title">عنوان دسته</param>
        /// <param name="slug">نامک دسته</param>
        /// <returns>نتیجه عملیات</returns>
        Task<bool> CreateMainCategoryAsync(string title, string slug);

        /// <summary>
        /// ایجاد زیردسته جدید
        /// </summary>
        /// <param name="parentId">شناسه دسته والد</param>
        /// <param name="title">عنوان زیردسته</param>
        /// <param name="slug">نامک زیردسته</param>
        /// <returns>نتیجه عملیات</returns>
        Task<bool> CreateSubCategoryAsync(int parentId, string title, string slug);

        /// <summary>
        /// ویرایش عنوان و نامک دسته‌بندی
        /// </summary>
        /// <param name="id">شناسه دسته</param>
        /// <param name="title">عنوان جدید</param>
        /// <param name="slug">نامک جدید</param>
        /// <returns>نتیجه عملیات</returns>
        Task<bool> UpdateCategoryTitleAsync(int id, string title, string slug);

        /// <summary>
        /// حذف دسته‌بندی
        /// </summary>
        /// <param name="id">شناسه دسته</param>
        /// <returns>نتیجه عملیات</returns>
        Task<bool> DeleteCategoryAsync(int id);
    }
}
