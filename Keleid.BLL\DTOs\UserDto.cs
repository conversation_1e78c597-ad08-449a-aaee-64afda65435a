namespace Keleid.BLL.DTOs
{
    public class UserDto
    {
        public string Id { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string? Email { get; set; }
        public DateTime RegisterDate { get; set; }
        public bool IsActive { get; set; }
        public bool PhoneNumberConfirmed { get; set; }
        public int AdvertisementCount { get; set; }
        public int ApprovedAdvertisementCount { get; set; }
        public int PendingAdvertisementCount { get; set; }
        public int FavoriteCount { get; set; }
        public string RelativeRegisterTime { get; set; } = string.Empty;
        public string StatusText => IsActive ? "فعال" : "غیرفعال";
        public string StatusClass => IsActive ? "badge-success" : "badge-danger";
    }

    public class UserPageDto
    {
        public List<UserDto> Users { get; set; } = new();
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public int TotalCount { get; set; }
        public bool HasPreviousPage => CurrentPage > 1;
        public bool HasNextPage => CurrentPage < TotalPages;
    }

    public class UserDetailDto
    {
        public string Id { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string? Email { get; set; }
        public DateTime RegisterDate { get; set; }
        public bool IsActive { get; set; }
        public bool PhoneNumberConfirmed { get; set; }
        public string RelativeRegisterTime { get; set; } = string.Empty;
        
        // Statistics
        public int TotalAdvertisements { get; set; }
        public int ApprovedAdvertisements { get; set; }
        public int PendingAdvertisements { get; set; }
        public int RejectedAdvertisements { get; set; }
        public int TotalFavorites { get; set; }
        
        // Recent Activities
        public List<AdvertisementDto> RecentAdvertisements { get; set; } = new();
        public List<FavoriteDto> RecentFavorites { get; set; } = new();
        
        // Status
        public string StatusText => IsActive ? "فعال" : "غیرفعال";
        public string StatusClass => IsActive ? "badge-success" : "badge-danger";
        public string PhoneConfirmedText => PhoneNumberConfirmed ? "تایید شده" : "تایید نشده";
        public string PhoneConfirmedClass => PhoneNumberConfirmed ? "badge-success" : "badge-warning";
    }
}
