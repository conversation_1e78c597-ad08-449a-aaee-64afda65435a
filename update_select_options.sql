-- اسکریپت بروزرسانی گزینه‌های فیلدهای select در CategoryFeatures
-- ابتدا ستون Options را اضافه می‌کنیم (اگر موجود نباشد)

-- اضافه کردن ستون Options اگر موجود نباشد
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'CategoryFeatures' AND COLUMN_NAME = 'Options')
BEGIN
    ALTER TABLE CategoryFeatures ADD Options NVARCHAR(MAX) NULL;
    PRINT N'ستون Options به جدول CategoryFeatures اضافه شد.';
END
ELSE
    PRINT N'ستون Options قبلاً موجود است.';

-- بروزرسانی گزینه‌های فیلدهای select

-- نوع مالکیت
UPDATE CategoryFeatures 
SET Options = N'شخصی|اجاره‌ای|رهنی|مشاع|وقفی'
WHERE Title = N'نوع مالکیت' AND InputType = 'select';

-- وضعیت مجوز
UPDATE CategoryFeatures 
SET Options = N'دارای مجوز کامل|در حال تمدید|نیاز به تمدید|بدون مجوز'
WHERE Title = N'وضعیت مجوز' AND InputType = 'select';

-- نوع کاربری (برای زمین‌های صنعتی)
UPDATE CategoryFeatures 
SET Options = N'صنعتی|تولیدی|انبارداری|لجستیک|مخلوط'
WHERE Title = N'نوع کاربری' AND InputType = 'select';

-- دسترسی به جاده اصلی
UPDATE CategoryFeatures 
SET Options = N'مستقیم|کمتر از 500 متر|500 متر تا 1 کیلومتر|بیش از 1 کیلومتر'
WHERE Title = N'دسترسی به جاده اصلی' AND InputType = 'select';

-- سیستم سرمایش
UPDATE CategoryFeatures 
SET Options = N'تبرید مکانیکی|تبرید طبیعی|سیستم ترکیبی|بدون سیستم سرمایش'
WHERE Title = N'سیستم سرمایش' AND InputType = 'select';

-- وضعیت سازه (برای سوله دست دوم)
UPDATE CategoryFeatures 
SET Options = N'عالی|خوب|متوسط|نیاز به تعمیر'
WHERE Title = N'وضعیت سازه' AND InputType = 'select';

-- دسترسی خودرو
UPDATE CategoryFeatures 
SET Options = N'مستقیم|با رمپ|نیاز به بارگیر|محدودیت دارد'
WHERE Title = N'دسترسی خودرو' AND InputType = 'select';

-- نوع دام
UPDATE CategoryFeatures 
SET Options = N'گاو|گوسفند|بز|شتر|اسب|ترکیبی'
WHERE Title = N'نوع دام' AND InputType = 'select';

-- سیستم تهویه
UPDATE CategoryFeatures 
SET Options = N'طبیعی|مکانیکی|اتوماتیک|ترکیبی'
WHERE Title = N'سیستم تهویه' AND InputType = 'select';

-- سیستم آبخوری
UPDATE CategoryFeatures 
SET Options = N'نیپل|ناودانی|اتوماتیک|دستی'
WHERE Title = N'سیستم آبخوری' AND InputType = 'select';

-- سیستم غذادهی
UPDATE CategoryFeatures 
SET Options = N'اتوماتیک|نیمه اتوماتیک|دستی|ترکیبی'
WHERE Title = N'سیستم غذادهی' AND InputType = 'select';

-- نوع آبیاری
UPDATE CategoryFeatures 
SET Options = N'قطره‌ای|بارانی|سنتی|غرقابی|ترکیبی'
WHERE Title = N'نوع آبیاری' AND InputType = 'select';

-- دسترسی به جاده
UPDATE CategoryFeatures 
SET Options = N'مستقیم|کمتر از 100 متر|100 تا 500 متر|بیش از 500 متر'
WHERE Title = N'دسترسی به جاده' AND InputType = 'select';

-- گلخانه (برای کشت سبزیجات)
UPDATE CategoryFeatures 
SET Options = N'دارد|ندارد|در حال ساخت'
WHERE Title = N'گلخانه' AND InputType = 'select';

-- آزمایشگاه کنترل کیفیت
UPDATE CategoryFeatures 
SET Options = N'دارد|ندارد|مشترک با سایر واحدها'
WHERE Title = N'آزمایشگاه کنترل کیفیت' AND InputType = 'select';

-- نوع انبار
UPDATE CategoryFeatures 
SET Options = N'انبار عمومی|سردخانه|انبار مواد غذایی|انبار صنعتی|انبار کشاورزی'
WHERE Title = N'نوع انبار' AND InputType = 'select';

-- سیستم انجماد
UPDATE CategoryFeatures 
SET Options = N'انجماد سریع|انجماد تدریجی|انجماد شوکی|سیستم ترکیبی'
WHERE Title = N'سیستم انجماد' AND InputType = 'select';

-- نمایش نتایج بروزرسانی
PRINT N'';
PRINT N'=== گزینه‌های فیلدهای select بروزرسانی شدند ===';

SELECT 
    c.Title as N'زیردسته‌بندی',
    cf.Title as N'عنوان ویژگی',
    cf.InputType as N'نوع ورودی',
    cf.Options as N'گزینه‌ها'
FROM CategoryFeatures cf
INNER JOIN Categories c ON cf.CategoryId = c.Id
WHERE cf.InputType = 'select' AND cf.Options IS NOT NULL
ORDER BY c.Id, cf.Id;

-- شمارش کل فیلدهای select که گزینه دارند
SELECT 
    COUNT(*) as N'تعداد فیلدهای select با گزینه'
FROM CategoryFeatures 
WHERE InputType = 'select' AND Options IS NOT NULL;

-- فیلدهای select بدون گزینه
SELECT 
    c.Title as N'زیردسته‌بندی',
    cf.Title as N'عنوان ویژگی'
FROM CategoryFeatures cf
INNER JOIN Categories c ON cf.CategoryId = c.Id
WHERE cf.InputType = 'select' AND (cf.Options IS NULL OR cf.Options = '')
ORDER BY c.Id, cf.Id;
