namespace Keleid.Web.ViewModels
{
    public class CreateAdvertisementViewModel
    {
        // مرحله 1: انتخاب دسته‌بندی
        public int CategoryId { get; set; }
        public string CategoryTitle { get; set; } = "";

        // مرحله 2: مشخصات آگهی
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public long? Price { get; set; }
        public bool IsPriceless { get; set; }

        // ویژگی‌های اختصاصی
        public Dictionary<int, string> FeatureValues { get; set; } = new Dictionary<int, string>();

        // مرحله 3: تصاویر
        public List<string> ImageUrls { get; set; } = new List<string>();
        public int? MainImageIndex { get; set; }

        // مرحله 4: موقعیت و تماس
        public string Province { get; set; } = "";
        public string City { get; set; } = "";
        public string Address { get; set; } = "";
        public string ContactName { get; set; } = "";
        public string ContactPhone { get; set; } = "";
        public string ContactEmail { get; set; } = "";

        // وضعیت فرآیند
        public int CurrentStep { get; set; } = 1;
        public bool IsCompleted { get; set; } = false;
    }
}
