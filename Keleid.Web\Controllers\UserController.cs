﻿using Keleid.BLL.Services;
using Keleid.BLL.Services.Interfaces;
using Keleid.DAL.Models;
using Keleid.Web.ViewModels;
using Keleid.Web.Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using System.Globalization;

namespace Keleid.Web.Controllers
{
    [Authorize]
    public class UserController : BaseController
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly IAdvertisementService _advertisementService;
        private readonly LoggingService _loggingService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public UserController(UserManager<ApplicationUser> userManager, SignInManager<ApplicationUser> signInManager, IAdvertisementService advertisementService, LoggingService loggingService, IHttpContextAccessor httpContextAccessor)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _advertisementService = advertisementService;
            _loggingService = loggingService;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<IActionResult> Index()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return RedirectToAction("Index", "Account");
            }

            var viewModel = new UserIndexViewModel
            {
                PhoneNumber = user.PhoneNumber ?? "نامشخص",
                RegisterDate = user.RegisterDate,
                IsActive = user.IsActive,
                PhoneNumberConfirmed = user.PhoneNumberConfirmed
            };

            return View(viewModel);
        }

        public async Task<IActionResult> Favs(int page = 1)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return RedirectToAction("Index", "Account");
            }

            const int pageSize = 10;
            var (favorites, totalCount, totalPages) = await _advertisementService.GetUserFavoritesWithPaginationAsync(user.Id, page, pageSize);

            var viewModel = new FavsViewModel
            {
                Favorites = favorites,
                CurrentPage = page,
                TotalPages = totalPages,
                TotalCount = totalCount,
                PageSize = pageSize
            };

            return View(viewModel);
        }

        public async Task<IActionResult> MyAds(int page = 1)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return RedirectToAction("Index", "Account");
            }

            const int pageSize = 10;
            var (advertisements, totalCount, totalPages) = await _advertisementService.GetUserAdvertisementsWithPaginationAsync(user.Id, page, pageSize);

            var viewModel = new MyAdsViewModel
            {
                Advertisements = advertisements,
                CurrentPage = page,
                TotalPages = totalPages,
                TotalCount = totalCount,
                PageSize = pageSize
            };

            return View(viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteAdvertisement(int id)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "کاربر یافت نشد" });
            }

            // دریافت اطلاعات آگهی برای لاگ
            var advertisement = await _advertisementService.GetAdvertisementByIdAsync(id);
            var result = await _advertisementService.SoftDeleteAdvertisementAsync(id, user.Id);

            if (result)
            {
                // لاگ کردن حذف آگهی
                await _loggingService.LogAdvertisementDeleteAsync(id, user.Id, advertisement?.Title, _httpContextAccessor);
                return Json(new { success = true, message = "آگهی با موفقیت حذف شد" });
            }
            else
            {
                return Json(new { success = false, message = "خطا در حذف آگهی" });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RemoveFavorite(int id)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "کاربر یافت نشد" });
            }

            var result = await _advertisementService.RemoveFavoriteAsync(id, user.Id);

            if (result)
            {
                return Json(new { success = true, message = "آگهی از نشان شده‌ها حذف شد" });
            }
            else
            {
                return Json(new { success = false, message = "خطا در حذف از نشان شده‌ها" });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AllowAnonymous]
        public async Task<IActionResult> Logout()
        {
            await _signInManager.SignOutAsync();
            HttpContext.Session.Clear();
            return RedirectToAction("Index", "Home");
        }
    }
}
