namespace Keleid.BLL.Services.Interfaces
{
    public interface ISitemapService
    {
        /// <summary>
        /// تولید sitemap.xml کامل شامل صفحات ثابت و داینامیک
        /// </summary>
        /// <returns>محتوای XML sitemap</returns>
        Task<string> GenerateSitemapXmlAsync();

        /// <summary>
        /// به‌روزرسانی sitemap پس از تغییر در آگهی‌ها
        /// </summary>
        /// <returns>نتیجه عملیات</returns>
        Task<bool> RefreshSitemapAsync();

        /// <summary>
        /// دریافت تاریخ آخرین به‌روزرسانی sitemap
        /// </summary>
        /// <returns>تاریخ آخرین به‌روزرسانی</returns>
        Task<DateTime?> GetLastModifiedAsync();

        /// <summary>
        /// بررسی نیاز به به‌روزرسانی sitemap
        /// </summary>
        /// <returns>true اگر نیاز به به‌روزرسانی باشد</returns>
        Task<bool> NeedsUpdateAsync();
    }
}
