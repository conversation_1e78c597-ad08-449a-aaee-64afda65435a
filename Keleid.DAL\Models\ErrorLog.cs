namespace Keleid.DAL.Models
{
    public class ErrorLog
    {
        public int Id { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? Exception { get; set; } // Exception type (e.g., NullReferenceException)
        public string? StackTrace { get; set; }
        public DateTime Timestamp { get; set; }
        public string? Source { get; set; } // Source of the error (Controller, Service, etc.)
        public string? InnerException { get; set; }
        public string? Properties { get; set; } // JSON string for additional properties
        public string? UserId { get; set; } // User who triggered the error (if applicable)
        public string? IpAddress { get; set; }
        public string? UserAgent { get; set; }
        public string? RequestPath { get; set; }
        public string? RequestMethod { get; set; } // GET, POST, etc.
        public int? StatusCode { get; set; } // HTTP status code
        public string Severity { get; set; } = "Error"; // Error, Critical, Fatal
    }
}
