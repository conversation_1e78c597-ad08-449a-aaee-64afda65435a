using Keleid.BLL.Services;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Diagnostics;

namespace Keleid.Web.Filters
{
    public class LogActionFilter : IAsyncActionFilter
    {
        private readonly LoggingService _loggingService;
        private readonly ILogger<LogActionFilter> _logger;

        public LogActionFilter(LoggingService loggingService, ILogger<LogActionFilter> logger)
        {
            _loggingService = loggingService;
            _logger = logger;
        }

        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            var stopwatch = Stopwatch.StartNew();
            var actionName = $"{context.Controller.GetType().Name}.{context.ActionDescriptor.DisplayName}";
            var userId = context.HttpContext.User?.Identity?.Name;

            // لاگ شروع عملیات
            var startProperties = new
            {
                Action = actionName,
                Parameters = context.ActionArguments,
                Method = context.HttpContext.Request.Method,
                Path = context.HttpContext.Request.Path.ToString(),
                QueryString = context.HttpContext.Request.QueryString.ToString()
            };

            await _loggingService.LogDebugAsync(
                message: $"شروع عملیات: {actionName}",
                source: context.Controller.GetType().Name,
                userId: userId,
                properties: startProperties
            );

            // اجرای action
            var executedContext = await next();
            stopwatch.Stop();

            // لاگ پایان عملیات
            var endProperties = new
            {
                Action = actionName,
                Duration = $"{stopwatch.ElapsedMilliseconds}ms",
                StatusCode = context.HttpContext.Response.StatusCode,
                Success = executedContext.Exception == null
            };

            if (executedContext.Exception == null)
            {
                await _loggingService.LogInformationAsync(
                    message: $"عملیات موفق: {actionName} در {stopwatch.ElapsedMilliseconds}ms",
                    source: context.Controller.GetType().Name,
                    userId: userId,
                    properties: endProperties
                );
            }
            else
            {
                await _loggingService.LogErrorAsync(
                    message: $"خطا در عملیات: {actionName}",
                    exception: executedContext.Exception,
                    source: context.Controller.GetType().Name,
                    userId: userId,
                    properties: endProperties
                );
            }
        }
    }
}
