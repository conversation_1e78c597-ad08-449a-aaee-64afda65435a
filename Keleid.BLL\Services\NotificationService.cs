using Keleid.BLL.DTOs;
using Keleid.DAL;
using Keleid.DAL.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Keleid.BLL.Services
{
    public class NotificationService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<NotificationService> _logger;

        public NotificationService(ApplicationDbContext context, ILogger<NotificationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<NotificationDto>> GetRecentNotificationsAsync(int count = 5)
        {
            try
            {
                var notifications = await _context.Notifications
                    .OrderByDescending(n => n.Date)
                    .Take(count)
                    .ToListAsync();

                return notifications.Select(MapToDto).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recent notifications");
                return new List<NotificationDto>();
            }
        }

        public async Task<List<NotificationDto>> GetUnreadNotificationsAsync(int count = 5)
        {
            try
            {
                var notifications = await _context.Notifications
                    .Where(n => !n.Status)
                    .OrderByDescending(n => n.Date)
                    .Take(count)
                    .ToListAsync();

                return notifications.Select(MapToDto).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unread notifications");
                return new List<NotificationDto>();
            }
        }

        public async Task<int> GetUnreadNotificationCountAsync()
        {
            try
            {
                return await _context.Notifications
                    .Where(n => !n.Status)
                    .CountAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unread notification count");
                return 0;
            }
        }

        public async Task<bool> MarkAsReadAsync(int notificationId, string? userId = null)
        {
            try
            {
                var notification = await _context.Notifications.FindAsync(notificationId);
                if (notification != null)
                {
                    notification.Status = true;
                    if (!string.IsNullOrEmpty(userId))
                    {
                        notification.UserId = userId;
                    }
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking notification as read: {NotificationId}", notificationId);
                return false;
            }
        }

        public async Task<(List<NotificationDto> notifications, int totalCount, int totalPages)> GetNotificationsWithPaginationAsync(int pageNumber = 1, int pageSize = 10)
        {
            try
            {
                var query = _context.Notifications.OrderByDescending(n => n.Date);

                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                var notifications = await query
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var notificationDtos = notifications.Select(MapToDto).ToList();

                return (notificationDtos, totalCount, totalPages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting notifications with pagination");
                return (new List<NotificationDto>(), 0, 0);
            }
        }

        public async Task<bool> CreateNewAdvertisementNotificationAsync(int advertisementId, string categoryTitle, string advertisementTitle)
        {
            try
            {
                var notification = new Notification
                {
                    Title = "آگهی جدید ثبت شد",
                    Body = $"آگهی جدید '{advertisementTitle}' در دسته‌بندی {categoryTitle} ثبت شده است",
                    Date = DateTime.Now,
                    Status = false,
                    UserId = null // برای همه ادمین‌ها
                };

                _context.Notifications.Add(notification);
                await _context.SaveChangesAsync();

                _logger.LogInformation("New advertisement notification created for advertisement {AdvertisementId}", advertisementId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating new advertisement notification for advertisement {AdvertisementId}", advertisementId);
                return false;
            }
        }

        private NotificationDto MapToDto(Notification notification)
        {
            return new NotificationDto
            {
                Id = notification.Id,
                Title = notification.Title,
                Body = notification.Body,
                Date = notification.Date,
                Status = notification.Status,
                UserId = notification.UserId,
                RelativeTime = GetRelativeTime(notification.Date)
            };
        }

        private string GetRelativeTime(DateTime date)
        {
            var timeSpan = DateTime.Now - date;

            if (timeSpan.TotalMinutes < 15)
                return "لحظاتی پیش";
            else if (timeSpan.TotalMinutes < 30)
                return "ربع ساعت پیش";
            else if (timeSpan.TotalMinutes < 60)
                return "نیم ساعت پیش";
            else if (timeSpan.TotalHours < 2)
                return "یک ساعت پیش";
            else if (date.Date == DateTime.Today)
                return "امروز";
            else if (date.Date == DateTime.Today.AddDays(-1))
                return "دیروز";
            else
                return date.ToString("dd MMMM yyyy", new System.Globalization.CultureInfo("fa-IR"));
        }
    }
}
