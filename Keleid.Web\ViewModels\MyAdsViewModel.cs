using Keleid.BLL.DTOs;
using Keleid.BLL.Helpers;

namespace Keleid.Web.ViewModels
{
    public class MyAdsViewModel
    {
        public List<AdvertisementDto> Advertisements { get; set; } = new List<AdvertisementDto>();
        public int CurrentPage { get; set; } = 1;
        public int TotalPages { get; set; } = 1;
        public int TotalCount { get; set; } = 0;
        public int PageSize { get; set; } = 10;

        // Pagination helpers
        public bool HasPreviousPage => CurrentPage > 1;
        public bool HasNextPage => CurrentPage < TotalPages;
        public int PreviousPage => CurrentPage - 1;
        public int NextPage => CurrentPage + 1;

        // Page range for pagination display
        public List<int> GetPageRange()
        {
            var pages = new List<int>();
            var startPage = Math.Max(1, CurrentPage - 2);
            var endPage = Math.Min(TotalPages, CurrentPage + 2);

            for (int i = startPage; i <= endPage; i++)
            {
                pages.Add(i);
            }

            return pages;
        }

        // Check if we should show ellipsis before or after page range
        public bool ShowStartEllipsis => GetPageRange().First() > 2;
        public bool ShowEndEllipsis => GetPageRange().Last() < TotalPages - 1;

        // Helper methods for advertisement display
        public string GetAdvertisementStatus(AdvertisementDto ad)
        {
            if (ad.IsDeleted)
                return "حذف شده";
            if (!ad.IsApproved)
                return "در انتظار تایید";
            return "فعال";
        }

        public string GetAdvertisementStatusBadgeClass(AdvertisementDto ad)
        {
            if (ad.IsDeleted)
                return "bg-danger";
            if (!ad.IsApproved)
                return "bg-warning";
            return "bg-success";
        }

        public string GetRelativeTime(AdvertisementDto ad)
        {
            return PersianDateHelper.GetRelativeTime(ad.CreatedAt);
        }

        public string GetAdvertisementImageClass(AdvertisementDto ad)
        {
            return ad.IsDeleted ? "opacity-50" : "";
        }
    }
}
