namespace Keleid.Web.Services
{
    public class FileService : IFileService
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly ILogger<FileService> _logger;
        private readonly string[] _allowedExtensions = { ".jpg", ".jpeg", ".png", ".gif", ".webp" };
        private readonly long _maxFileSize = 10 * 1024 * 1024; // 10 MB
        private const string ADS_IMAGES_FOLDER = "AdsImages";

        public FileService(IWebHostEnvironment webHostEnvironment, ILogger<FileService> logger)
        {
            _webHostEnvironment = webHostEnvironment;
            _logger = logger;
        }

        public async Task<string> UploadAdvertisementImageAsync(IFormFile file, int advertisementId)
        {
            try
            {
                if (!IsValidImageFile(file))
                {
                    throw new ArgumentException("فایل انتخاب شده معتبر نیست");
                }

                // ایجاد مسیر فولدر آگهی
                var advertisementFolder = Path.Combine(_webHostEnvironment.WebRootPath, ADS_IMAGES_FOLDER, advertisementId.ToString());
                
                if (!Directory.Exists(advertisementFolder))
                {
                    Directory.CreateDirectory(advertisementFolder);
                }

                // تولید نام فایل با GUID
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                var fileName = $"{Guid.NewGuid()}{fileExtension}";
                var filePath = Path.Combine(advertisementFolder, fileName);

                // ذخیره فایل
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                // برگرداندن مسیر نسبی
                var relativePath = $"{ADS_IMAGES_FOLDER}/{advertisementId}/{fileName}";
                _logger.LogInformation("Image uploaded successfully: {RelativePath}", relativePath);
                
                return relativePath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading image for advertisement {AdvertisementId}", advertisementId);
                throw;
            }
        }

        public async Task<List<string>> UploadAdvertisementImagesAsync(IFormFileCollection files, int advertisementId)
        {
            var uploadedPaths = new List<string>();

            try
            {
                foreach (var file in files)
                {
                    if (IsValidImageFile(file))
                    {
                        var path = await UploadAdvertisementImageAsync(file, advertisementId);
                        uploadedPaths.Add(path);
                    }
                }

                return uploadedPaths;
            }
            catch (Exception ex)
            {
                // در صورت خطا، فایل‌های آپلود شده را حذف کن
                foreach (var path in uploadedPaths)
                {
                    await DeleteAdvertisementImageAsync(path);
                }
                
                _logger.LogError(ex, "Error uploading images for advertisement {AdvertisementId}", advertisementId);
                throw;
            }
        }

        public async Task<bool> DeleteAdvertisementImageAsync(string imagePath)
        {
            try
            {
                if (string.IsNullOrEmpty(imagePath))
                    return false;

                var fullPath = GetFullPath(imagePath);
                
                if (File.Exists(fullPath))
                {
                    await Task.Run(() => File.Delete(fullPath));
                    _logger.LogInformation("Image deleted successfully: {ImagePath}", imagePath);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting image: {ImagePath}", imagePath);
                return false;
            }
        }

        public async Task<bool> DeleteAdvertisementImagesAsync(int advertisementId)
        {
            try
            {
                var advertisementFolder = Path.Combine(_webHostEnvironment.WebRootPath, ADS_IMAGES_FOLDER, advertisementId.ToString());
                
                if (Directory.Exists(advertisementFolder))
                {
                    await Task.Run(() => Directory.Delete(advertisementFolder, true));
                    _logger.LogInformation("Advertisement images folder deleted: {AdvertisementId}", advertisementId);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting advertisement images folder: {AdvertisementId}", advertisementId);
                return false;
            }
        }

        public bool IsValidImageFile(IFormFile file)
        {
            if (file == null || file.Length == 0)
                return false;

            // بررسی حجم فایل
            if (file.Length > _maxFileSize)
                return false;

            // بررسی پسوند فایل
            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            if (!_allowedExtensions.Contains(extension))
                return false;

            // بررسی Content-Type
            var allowedContentTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp" };
            if (!allowedContentTypes.Contains(file.ContentType.ToLowerInvariant()))
                return false;

            return true;
        }

        public string GetFullPath(string relativePath)
        {
            if (string.IsNullOrEmpty(relativePath))
                return string.Empty;

            // حذف اسلش ابتدایی اگر وجود دارد
            relativePath = relativePath.TrimStart('/');
            
            return Path.Combine(_webHostEnvironment.WebRootPath, relativePath);
        }
    }
}
