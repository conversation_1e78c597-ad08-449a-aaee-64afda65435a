namespace Keleid.BLL.DTOs
{
    public class UserTaskDto
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string Body { get; set; }
        public DateTime Date { get; set; }
        public bool Status { get; set; }
        public string? UserId { get; set; }
        public string RelativeTime { get; set; }
    }

    public class UserTaskPageDto
    {
        public List<UserTaskDto> Tasks { get; set; } = new();
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public int TotalCount { get; set; }
        public bool HasPreviousPage => CurrentPage > 1;
        public bool HasNextPage => CurrentPage < TotalPages;
    }
}
