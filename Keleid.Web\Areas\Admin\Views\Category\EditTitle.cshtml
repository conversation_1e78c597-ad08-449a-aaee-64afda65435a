﻿@{
    ViewBag.Title = "ویرایش عنوان دسته بندی";
}

<!-- Left Sidebar -->
@await Html.PartialAsync("_LeftSidebar")

<!-- Main Content -->
<section class="content home">

    <!-- Breadcrumb -->
    @await Html.PartialAsync("_Breadcrumb")

    <div class="container-fluid">
        <div class="row clearfix">
            <div class="col-lg-8 col-md-12 mx-auto">
                <div class="card">
                    <div class="header">
                        <h2><i class="fas fa-edit text-warning"></i> ویرایش <strong>عنوان دسته بندی</strong></h2>
                    </div>
                    <div class="body">
                        <form asp-area="Admin" asp-controller="Category" asp-action="EditTitle" method="post">
                            <input type="hidden" name="Id" value="@ViewBag.CategoryId" />

                            <!-- نمایش نوع دسته -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="alert @(ViewBag.IsMainCategory == true ? "alert-primary" : "alert-info")">
                                        <i class="fas @(ViewBag.IsMainCategory == true ? "fa-folder" : "fa-folder-open")"></i>
                                        <strong>نوع:</strong>
                                        @(ViewBag.IsMainCategory == true ? "دسته اصلی" : "زیردسته")
                                        @if (ViewBag.IsMainCategory == false && !string.IsNullOrEmpty(ViewBag.ParentTitle))
                                        {
                                            <br>
                                            <small><strong>دسته والد:</strong> @ViewBag.ParentTitle</small>
                                        }
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="title" class="form-label">
                                            <i class="fas fa-tag"></i> عنوان جدید <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" id="title" name="Title" class="form-control"
                                               value="@ViewBag.CurrentTitle" required maxlength="100">
                                        <small class="form-text text-muted">
                                            عنوان باید منحصر به فرد باشد
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="slug" class="form-label">
                                            <i class="fas fa-link"></i> نامک جدید (Slug) <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" id="slug" name="Slug" class="form-control"
                                               value="@ViewBag.CurrentSlug" required maxlength="100">
                                        <small class="form-text text-muted">
                                            فقط حروف انگلیسی، اعداد و خط تیره مجاز است
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-info-circle"></i> پیش‌نمایش تغییرات
                                        </label>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="alert alert-light">
                                                    <strong>قبل:</strong><br>
                                                    <i class="fas @(ViewBag.IsMainCategory == true ? "fa-folder" : "fa-folder-open") text-muted"></i>
                                                    @ViewBag.CurrentTitle
                                                    <small class="text-muted d-block">
                                                        URL: /category/@ViewBag.CurrentSlug
                                                    </small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="alert alert-warning">
                                                    <strong>بعد:</strong><br>
                                                    <i class="fas @(ViewBag.IsMainCategory == true ? "fa-folder" : "fa-folder-open") text-warning"></i>
                                                    <span id="preview-title">@ViewBag.CurrentTitle</span>
                                                    <small class="text-muted d-block">
                                                        URL: /category/<span id="preview-slug">@ViewBag.CurrentSlug</span>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <hr>
                                    <button type="submit" class="btn btn-warning btn-round waves-effect">
                                        <i class="fas fa-save"></i> ذخیره تغییرات
                                    </button>
                                    <a asp-area="Admin" asp-controller="Category" asp-action="Index"
                                       class="btn btn-secondary btn-round waves-effect">
                                        <i class="fas fa-arrow-left"></i> بازگشت
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const titleInput = document.getElementById('title');
    const slugInput = document.getElementById('slug');
    const previewTitle = document.getElementById('preview-title');
    const previewSlug = document.getElementById('preview-slug');

    // Update preview when title changes
    titleInput.addEventListener('input', function() {
        const title = this.value;
        previewTitle.textContent = title || '@ViewBag.CurrentTitle';

        // Generate Persian slug
        let slug = title
            .replace(/\s+/g, '-')         // Replace spaces with hyphens
            .replace(/-+/g, '-')          // Replace multiple hyphens with single
            .replace(/^-+|-+$/g, '');     // Remove leading/trailing hyphens

        if (slug) {
            slugInput.value = slug;
            previewSlug.textContent = slug;
        }
    });

    // Update preview when slug changes
    slugInput.addEventListener('input', function() {
        previewSlug.textContent = this.value || '@ViewBag.CurrentSlug';
    });
});
</script>