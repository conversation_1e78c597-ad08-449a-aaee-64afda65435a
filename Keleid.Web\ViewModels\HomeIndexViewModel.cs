using Keleid.BLL.DTOs;

namespace Keleid.Web.ViewModels
{
    public class HomeIndexViewModel
    {
        public List<CategoryWithSubCategoriesDto> Categories { get; set; } = new List<CategoryWithSubCategoriesDto>();
        public List<AdvertisementDto> LatestAdvertisements { get; set; } = new List<AdvertisementDto>();
        public CategoryDto? SelectedCategory { get; set; }
        public string? SelectedSlug { get; set; }
        public string? SearchTerm { get; set; }
        public bool HasMoreAds { get; set; }
        public int CurrentPage { get; set; } = 1;
    }
}
