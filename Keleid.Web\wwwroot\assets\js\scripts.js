﻿
// Cookie management functions
function setCookie(name, value, days) {
    const expires = new Date();
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
    document.cookie = `${name}=${encodeURIComponent(value)};expires=${expires.toUTCString()};path=/`;
}

function getCookie(name) {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
        let c = ca[i];
        while (c.charAt(0) === ' ') c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) === 0) {
            return decodeURIComponent(c.substring(nameEQ.length, c.length));
        }
    }
    return null;
}

// City Selection Modal
const citySearchInput = document.getElementById('citySearchInput');
const cityList = document.querySelector('.city-list');
const confirmButton = document.getElementById('confirmCitySelection');
const selectedCityElements = document.querySelectorAll('.selected-city');
const cityModal = document.getElementById('cityModal');
const modal = new bootstrap.Modal(cityModal);

// Load selected province from cookie on page load
document.addEventListener('DOMContentLoaded', function() {
    const savedProvince = getCookie('selectedProvince') || 'کل ایران';

    // Update UI elements
    selectedCityElements.forEach(element => {
        element.textContent = savedProvince;
    });

    // Set the radio button in modal
    const radioButtons = document.querySelectorAll('input[name="city"]');
    radioButtons.forEach(radio => {
        if (radio.value === savedProvince) {
            radio.checked = true;
        }
    });
});

// Search functionality
citySearchInput.addEventListener('input', function () {
    const searchTerm = this.value.toLowerCase();
    const cities = cityList.querySelectorAll('.form-check');

    cities.forEach(city => {
        const cityName = city.querySelector('label').textContent.trim().toLowerCase();
        if (cityName.includes(searchTerm)) {
            city.style.display = '';
        } else {
            city.style.display = 'none';
        }
    });
});

// Confirm selection
confirmButton.addEventListener('click', function () {
    const selectedCity = document.querySelector('input[name="city"]:checked');
    if (selectedCity) {
        const provinceName = selectedCity.value;

        // Update UI elements
        selectedCityElements.forEach(element => {
            element.textContent = provinceName;
        });

        // Save to cookie (expires in 30 days)
        setCookie('selectedProvince', provinceName, 30);

        // Hide modal
        modal.hide();

        // Reload page to apply filter
        window.location.reload();
    }
});

// Clear search on modal close
cityModal.addEventListener('hidden.bs.modal', function () {
    citySearchInput.value = '';
    const cities = cityList.querySelectorAll('.form-check');
    cities.forEach(city => {
        city.style.display = '';
    });
});

// Search functionality
function handleSearch() {
    const searchInputs = document.querySelectorAll('.search-box input, .mobile-search input');

    searchInputs.forEach(input => {
        // Handle Enter key press
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                performSearch(this.value.trim());
            }
        });

        // Handle search icon click
        const searchIcon = input.parentElement.querySelector('.input-group-text');
        if (searchIcon) {
            searchIcon.addEventListener('click', function() {
                performSearch(input.value.trim());
            });
        }
    });
}

function performSearch(searchTerm) {
    if (!searchTerm) {
        return;
    }

    // Convert Persian/Arabic text to URL-friendly format
    const urlFriendlyTerm = encodeURIComponent(searchTerm);

    // Navigate to search URL
    window.location.href = `/search/${urlFriendlyTerm}`;
}

// Clear search functionality
function handleClearSearch() {
    // Use event delegation to handle dynamically added buttons
    document.addEventListener('click', function(e) {
        if (e.target.closest('.clear-search-btn')) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Clear search button clicked'); // برای debug
            // Navigate to home page to clear search
            window.location.href = '/';
        }
    });
}

// Initialize search functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    handleSearch();
    handleClearSearch();
});