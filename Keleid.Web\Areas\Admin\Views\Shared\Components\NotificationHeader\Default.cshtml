@model List<Keleid.BLL.DTOs.NotificationDto>

<li class="dropdown">
    <a href="javascript:void(0);" class="dropdown-toggle" data-toggle="dropdown" role="button">
        <i class="zmdi zmdi-notifications"></i>
        @if (ViewBag.UnreadCount > 0)
        {
            <div class="notify"><span class="heartbit"></span><span class="point"></span></div>
        }
    </a>
    <ul class="dropdown-menu dropdown-menu-right slideDown">
        <li class="header">اعلان ها (@ViewBag.UnreadCount)</li>
        <li class="body">
            <ul class="menu list-unstyled">
                @if (Model.Any())
                {
                    @foreach (var notification in Model)
                    {
                        <li>
                            <a href="javascript:void(0);" onclick="markNotificationAsRead(@notification.Id)">
                                <div class="icon-circle @(notification.Status ? "bg-green" : "bg-blue")">
                                    <i class="zmdi zmdi-account"></i>
                                </div>
                                <div class="menu-info">
                                    <h4>@notification.Title</h4>
                                    <p>
                                        <i class="zmdi zmdi-time"></i> @notification.RelativeTime
                                    </p>
                                </div>
                            </a>
                        </li>
                    }
                }
                else
                {
                    <li>
                        <div class="menu-info text-center">
                            <p>اعلان جدیدی وجود ندارد</p>
                        </div>
                    </li>
                }
            </ul>
        </li>
        <li class="footer">
            <a asp-area="Admin" asp-controller="Home" asp-action="Notifications">نمایش همه</a>
        </li>
    </ul>
</li>

<script>
    function markNotificationAsRead(notificationId) {
        fetch('/Admin/Home/MarkNotificationAsRead', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            body: JSON.stringify({ id: notificationId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        });
    }
</script>
