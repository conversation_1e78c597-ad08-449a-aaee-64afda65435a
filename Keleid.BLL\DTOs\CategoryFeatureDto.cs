namespace Keleid.BLL.DTOs
{
    public class CategoryFeatureDto
    {
        public int Id { get; set; }
        public int CategoryId { get; set; }
        public string Title { get; set; }
        public string InputType { get; set; }
        public bool IsRequired { get; set; }
        public string? Options { get; set; }
        
        /// <summary>
        /// گزینه‌های انتخابی به صورت لیست
        /// </summary>
        public List<string> OptionsList => 
            string.IsNullOrEmpty(Options) ? new List<string>() : Options.Split('|').Select(o => o.Trim()).ToList();
    }
}
