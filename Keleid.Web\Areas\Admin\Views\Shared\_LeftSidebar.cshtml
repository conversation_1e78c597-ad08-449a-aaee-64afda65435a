﻿@{
    string currentAction = ViewContext.RouteData.Values["action"]?.ToString();
    string currentController = ViewContext.RouteData.Values["controller"]?.ToString();

    string IsActive(string controller) =>
        (controller == currentController) ? "active open" : "";

    string IsActive2(string controller, string action) =>
        (controller == currentController && action == currentAction) ? "active" : "";
}

<aside id="leftsidebar" class="sidebar">
    <div class="menu">
        <ul class="list">
            <li>
                <div class="user-info">
                    <div class="image">
                        <img src="~/admin-assets/images/profile.png" alt="کاربر">
                    </div>
                    <div class="detail">
                        <h4>مدیر سایت</h4>
                        <small> مدیر کل </small>
                    </div>
                    <form asp-area="Admin" asp-controller="Home" asp-action="Logout" method="post" style="margin: 0; display: inline;" onsubmit="return confirmLogout()">
                        @Html.AntiForgeryToken()
                        <button type="submit" style="border: none; background: none; color: inherit; cursor: pointer;" title="خروج از سیستم">
                            <i class="zmdi zmdi-power"></i>
                        </button>
                    </form>
                </div>
            </li>
            <li class="@IsActive("Home")">
                <a href="javascript:void(0);" class="menu-toggle">
                    <i class="zmdi zmdi-home"></i><span>داشبورد</span>
                </a>
                <ul class="ml-menu">
                    <li class="@IsActive2("Home", "Index")"><a asp-area="Admin" asp-controller="Home" asp-action="Index">داشبورد</a> </li>
                    <li class="@IsActive2("Home", "Notifications")"><a asp-area="Admin" asp-controller="Home" asp-action="Notifications"> اعلان ها</a></li>
                    <li class="@IsActive2("Home", "Tasks")"><a asp-area="Admin" asp-controller="Home" asp-action="Tasks"> وظایف </a></li>
                </ul>
            </li>
            <li class="@IsActive("Ads")">
                <a href="javascript:void(0);" class="menu-toggle">
                    <i class="zmdi zmdi-apps"></i><span>
                        آگهی ها
                    </span>
                </a>
                <ul class="ml-menu">
                    <li class="@IsActive2("Ads", "Index")"><a asp-area="Admin" asp-controller="Ads" asp-action="Index"> آخرین آگهی‌ها</a></li>
                    <li class="@IsActive2("Ads", "Waiting")"><a asp-area="Admin" asp-controller="Ads" asp-action="Waiting">در انتظار تایید</a></li>
                    <li class="@IsActive2("Ads", "Approved")"><a asp-area="Admin" asp-controller="Ads" asp-action="Approved">تایید شده</a></li>
                    <li class="@IsActive2("Ads", "Removed")"><a asp-area="Admin" asp-controller="Ads" asp-action="Removed">حذف شده</a></li>
                </ul>
            </li>
            <li class="@IsActive("Category")">
                <a href="javascript:void(0);" class="menu-toggle">
                    <i class="zmdi zmdi-swap-alt"></i><span>
                        دسته بندی
                        ها
                    </span>
                </a>
                <ul class="ml-menu">
                    <li class="@IsActive2("Category", "Index")"> <a asp-area="Admin" asp-controller="Category" asp-action="Index">لیست دسته بندی‌ها</a> </li>
                    <li class="@IsActive2("Category", "Add")"> <a asp-area="Admin" asp-controller="Category" asp-action="Add">افزودن دسته بندی</a> </li>
                </ul>
            </li>
            <li class="@IsActive("User")">
                <a href="javascript:void(0);" class="menu-toggle">
                    <i class="zmdi zmdi-assignment"></i><span>کاربران</span>
                </a>
                <ul class="ml-menu">
                    <li class="@IsActive2("User", "Index")"><a asp-area="Admin" asp-controller="User" asp-action="Index"> لیست کاربران </a> </li>
                </ul>
            </li>
            <li class="@IsActive("Log")">
                <a href="javascript:void(0);" class="menu-toggle">
                    <i class="zmdi zmdi-grid"></i><span>لاگ ها</span>
                </a>
                <ul class="ml-menu">
                    <li class="@IsActive2("Log", "Sms")"> <a asp-area="Admin" asp-controller="Log" asp-action="Sms"> پیامک ها </a> </li>
                    <li class="@IsActive2("Log", "Errors")"> <a asp-area="Admin" asp-controller="Log" asp-action="Errors"> خطا ها </a> </li>
                    <li class="@IsActive2("Log", "Index")"> <a asp-area="Admin" asp-controller="Log" asp-action="Index"> لاگ ها</a> </li>
                </ul>
            </li>
        </ul>
    </div>
</aside>