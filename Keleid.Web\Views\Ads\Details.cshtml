﻿@model Keleid.Web.ViewModels.AdsDetailsViewModel
@{
    ViewData["Title"] = "مشخصات آگهی";
}

<!-- Header -->
@await Html.PartialAsync("_Se<PERSON><PERSON>eader", "ثبت آگهی")

<div class="container my-5">
    <!-- Step Indicator -->
    <div class="step-indicator mb-4">
        <div class="step-line"></div>
        <div class="step">
            <div class="step-circle">1</div>
            <div class="step-title">دسته‌بندی</div>
        </div>
        <div class="step active">
            <div class="step-circle">2</div>
            <div class="step-title">مشخصات آگهی</div>
        </div>
        <div class="step">
            <div class="step-circle">3</div>
            <div class="step-title">تصاویر</div>
        </div>
        <div class="step">
            <div class="step-circle">4</div>
            <div class="step-title">موقعیت و تماس</div>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <h5 class="card-title mb-4">مشخصات آگهی - @Model.CategoryTitle</h5>

            <form id="listingForm" class="needs-validation" method="post" novalidate>
                <input type="hidden" name="CategoryId" value="@Model.CategoryId" />
                @Html.AntiForgeryToken()
                <!-- فیلدهای ثابت -->
                <div class="mb-4">
                    <h6 class="mb-3">اطلاعات کلی</h6>
                    <div class="mb-3">
                        <label class="form-label required">عنوان آگهی</label>
                        <input type="text" name="Title" class="form-control" required minlength="10"
                               placeholder="یک عنوان مناسب انتخاب کنید" value="@Model.Title">
                        <div class="form-text">عنوان باید حداقل ۱۰ کاراکتر باشد</div>
                        @Html.ValidationMessage("Title", "", new { @class = "invalid-feedback d-block" })
                    </div>

                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label class="form-label" id="priceLabel">قیمت</label>
                            <div class="input-group">
                                <input type="number" name="Price" id="priceInput" class="form-control" min="0"
                                       placeholder="مبلغ را وارد کنید" value="@Model.Price">
                                <span class="input-group-text">تومان</span>
                            </div>
                            <div class="form-text" id="priceinwords">صفر تومان</div>
                            @Html.ValidationMessage("Price", "", new { @class = "invalid-feedback d-block" })
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">نوع قیمت</label>
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="checkbox" name="IsPriceless"
                                       id="isPriceless" value="true" @(Model.IsPriceless ? "checked" : "")>
                                <input type="hidden" name="IsPriceless" value="false">
                                <label class="form-check-label" for="isPriceless">
                                    توافقی
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label required">توضیحات آگهی</label>
                        <textarea name="Description" class="form-control" rows="5" required minlength="30"
                                  placeholder="جزئیات و توضیحات مربوط به آگهی را وارد کنید...">@Model.Description</textarea>
                        <div class="form-text">توضیحات باید حداقل ۳۰ کاراکتر باشد</div>
                        @Html.ValidationMessage("Description", "", new { @class = "invalid-feedback d-block" })
                    </div>
                </div>

                <!-- فیلدهای اختصاصی -->
                @if (Model.CategoryFeatures.Any())
                {
                    <div class="mb-4">
                        <h6 class="mb-3">مشخصات اختصاصی</h6>
                        <div id="dynamicFields">
                            @foreach (var feature in Model.CategoryFeatures)
                            {
                                <div class="mb-3">
                                    <label class="form-label @(feature.IsRequired ? "required" : "")">
                                        @feature.Title
                                    </label>

                                    @if (feature.InputType == "text")
                                    {
                                        <input type="text"
                                               name="Features[@feature.Id]"
                                               class="form-control"
                                               @(feature.IsRequired ? "required" : "")
                                               placeholder="@feature.Title را وارد کنید"
                                               value="@(Model.FeatureValues.ContainsKey(feature.Id) ? Model.FeatureValues[feature.Id] : "")">
                                    }
                                    else if (feature.InputType == "number")
                                    {
                                        <input type="number"
                                               name="Features[@feature.Id]"
                                               class="form-control"
                                               @(feature.IsRequired ? "required" : "")
                                               min="0"
                                               placeholder="@feature.Title را وارد کنید"
                                               value="@(Model.FeatureValues.ContainsKey(feature.Id) ? Model.FeatureValues[feature.Id] : "")">
                                    }
                                    else if (feature.InputType == "select")
                                    {
                                        <select name="Features[@feature.Id]"
                                                class="form-select"
                                                @(feature.IsRequired ? "required" : "")>
                                            <option value="">انتخاب کنید</option>
                                            @foreach (var option in feature.OptionsList)
                                            {
                                                var isSelected = Model.FeatureValues.ContainsKey(feature.Id) && Model.FeatureValues[feature.Id] == option;
                                                <option value="@option" selected="@isSelected">@option</option>
                                            }
                                        </select>
                                    }

                                    @if (feature.IsRequired)
                                    {
                                        <div class="form-text">این فیلد اجباری است</div>
                                    }

                                    <!-- نمایش خطای validation -->
                                    @if (ViewData.ModelState.ContainsKey($"Features[{feature.Id}]"))
                                    {
                                        var errors = ViewData.ModelState[$"Features[{feature.Id}]"].Errors;
                                        if (errors.Any())
                                        {
                                            <div class="invalid-feedback d-block">
                                                @errors.First().ErrorMessage
                                            </div>
                                        }
                                    }
                                </div>
                            }
                        </div>
                    </div>
                }

                <!-- دکمه‌های کنترلی -->
                <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-outline-secondary px-4" onclick="window.location.href='/Ads/Index'">
                        <i class="fas fa-arrow-right ml-1"></i>
                        مرحله قبل
                    </button>
                    <button type="submit" class="btn btn-danger px-4">
                        مرحله بعد
                        <i class="fas fa-arrow-left mr-1"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('listingForm');
        const priceInput = document.getElementById('priceInput');
        const isPricelessCheckbox = document.getElementById('isPriceless');
        const priceLabel = document.getElementById('priceLabel');

        // مدیریت checkbox توافقی
        function updatePriceField() {
            if (isPricelessCheckbox.checked) {
                priceInput.value = '';
                priceInput.disabled = true;
                priceInput.removeAttribute('required');
                priceLabel.textContent = 'قیمت (توافقی)';
                priceInput.classList.remove('is-invalid');
            } else {
                priceInput.disabled = false;
                priceInput.setAttribute('required', 'required');
                priceLabel.innerHTML = 'قیمت <span class="text-danger">*</span>';
            }
        }

        if (isPricelessCheckbox && priceInput) {
            isPricelessCheckbox.addEventListener('change', updatePriceField);

            // بررسی وضعیت اولیه
            updatePriceField();
        }

        // اعتبارسنجی سفارشی
        form.addEventListener('submit', function(event) {
            let isValid = true;

            // بررسی عنوان
            const titleInput = document.querySelector('input[name="Title"]');
            if (!titleInput.value || titleInput.value.length < 10) {
                titleInput.classList.add('is-invalid');
                isValid = false;
            } else {
                titleInput.classList.remove('is-invalid');
            }

            // بررسی توضیحات
            const descInput = document.querySelector('textarea[name="Description"]');
            if (!descInput.value || descInput.value.length < 30) {
                descInput.classList.add('is-invalid');
                isValid = false;
            } else {
                descInput.classList.remove('is-invalid');
            }

            // بررسی قیمت (فقط اگر توافقی انتخاب نشده باشد)
            if (!isPricelessCheckbox.checked) {
                if (!priceInput.value || parseFloat(priceInput.value) <= 0) {
                    priceInput.classList.add('is-invalid');
                    isValid = false;
                } else {
                    priceInput.classList.remove('is-invalid');
                }
            }

            // بررسی فیلدهای اجباری اختصاصی
            const requiredFeatures = document.querySelectorAll('[name^="Features["][required]');
            requiredFeatures.forEach(function(field) {
                if (!field.value) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            if (!isValid) {
                event.preventDefault();
                form.classList.add('was-validated');

                // اسکرول به اولین فیلد نامعتبر
                const firstInvalid = form.querySelector('.is-invalid');
                if (firstInvalid) {
                    firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    firstInvalid.focus();
                }
            }
        });
    });
</script>

<script>
    function numberToPersianWords(num) {
        if (num === 0) return "صفر";

        const yekan = ["", "یک", "دو", "سه", "چهار", "پنج", "شش", "هفت", "هشت", "نه"];
        const dah = ["", "ده", "بیست", "سی", "چهل", "پنجاه", "شصت", "هفتاد", "هشتاد", "نود"];
        const dahgan = ["ده", "یازده", "دوازده", "سیزده", "چهارده", "پانزده", "شانزده", "هفده", "هجده", "نوزده"];
        const sadgan = ["", "صد", "دویست", "سیصد", "چهارصد", "پانصد", "ششصد", "هفتصد", "هشتصد", "نهصد"];
        const bigUnits = ["", "هزار", "میلیون", "میلیارد", "تریلیون"];

        function threeDigitToWord(n) {
            let s = [];
            const sad = Math.floor(n / 100);
            const da = Math.floor((n % 100) / 10);
            const ye = n % 10;

            if (sad > 0) s.push(sadgan[sad]);

            if (da > 1) {
                s.push(dah[da]);
                if (ye > 0) s.push(yekan[ye]);
            } else if (da === 1) {
                s.push(dahgan[ye]);
            } else if (ye > 0) {
                s.push(yekan[ye]);
            }

            return s.join(" و ");
        }

        let parts = [];
        let group = 0;

        while (num > 0) {
            let part = num % 1000;
            if (part !== 0) {
                let word = threeDigitToWord(part);
                if (bigUnits[group]) word += " " + bigUnits[group];
                parts.unshift(word);
            }
            num = Math.floor(num / 1000);
            group++;
        }

        return parts.join(" و ");
    }

    // اتصال به input
    document.getElementById("priceInput").addEventListener("input", function () {
        const value = parseInt(this.value) || 0;
        const text = numberToPersianWords(value);
        document.getElementById("priceinwords").innerText = text + " تومان";
    });
</script>

<script>
    document.getElementById("priceInput").addEventListener("input", function () {
        // فقط 15 رقم عددی (اعداد بزرگ)
        if (this.value.length > 15) {
            this.value = this.value.slice(0, 15); // برش می‌زنیم به 15 رقم
        }

        // نمایش عدد به حروف
        const value = parseInt(this.value) || 0;
        const text = numberToPersianWords(value);
        document.getElementById("priceinwords").innerText = text + " تومان";
    });
</script>



<style>
    .required::after {
        content: " *";
        color: red;
    }

    .form-control:invalid,
    .form-select:invalid {
        border-color: #dc3545;
    }

    .form-control:valid,
    .form-select:valid {
        border-color: #198754;
    }

    .was-validated .form-control:invalid,
    .was-validated .form-select:invalid {
        border-color: #dc3545;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: left calc(0.375em + 0.1875rem) center;
        background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }

    .was-validated .form-control:valid,
    .was-validated .form-select:valid {
        border-color: #198754;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5 6.47 3.56 4.26 5.78z'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: left calc(0.375em + 0.1875rem) center;
        background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }
</style>