﻿using Keleid.BLL.Services;
using Keleid.BLL.Services.Interfaces;
using Keleid.Web.Extensions;
using Microsoft.AspNetCore.Mvc;

namespace Keleid.Web.Areas.Admin.Controllers
{
    public class AdsController : BaseAdminController
    {
        private readonly IAdvertisementService _advertisementService;
        private readonly LoggingService _loggingService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public AdsController(IAdvertisementService advertisementService, LoggingService loggingService, IHttpContextAccessor httpContextAccessor)
        {
            _advertisementService = advertisementService;
            _loggingService = loggingService;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<IActionResult> Index(int page = 1)
        {
            const int pageSize = 10;
            var (advertisements, totalCount, totalPages) = await _advertisementService.GetAllAdvertisementsWithPaginationAsync(page, pageSize);

            var model = new Keleid.BLL.DTOs.AdvertisementPageDto
            {
                Advertisements = advertisements,
                CurrentPage = page,
                TotalPages = totalPages,
                TotalCount = totalCount
            };

            return View(model);
        }

        public async Task<IActionResult> Waiting(int page = 1)
        {
            const int pageSize = 10;
            var (advertisements, totalCount, totalPages) = await _advertisementService.GetPendingAdvertisementsWithPaginationAsync(page, pageSize);

            var model = new Keleid.BLL.DTOs.AdvertisementPageDto
            {
                Advertisements = advertisements,
                CurrentPage = page,
                TotalPages = totalPages,
                TotalCount = totalCount
            };

            return View(model);
        }



        public async Task<IActionResult> Approved(int page = 1)
        {
            const int pageSize = 10;
            var (advertisements, totalCount, totalPages) = await _advertisementService.GetApprovedAdvertisementsWithPaginationAsync(page, pageSize);

            var model = new Keleid.BLL.DTOs.AdvertisementPageDto
            {
                Advertisements = advertisements,
                CurrentPage = page,
                TotalPages = totalPages,
                TotalCount = totalCount
            };

            return View(model);
        }

        public async Task<IActionResult> Removed(int page = 1)
        {
            const int pageSize = 10;
            var (advertisements, totalCount, totalPages) = await _advertisementService.GetDeletedAdvertisementsWithPaginationAsync(page, pageSize);

            var model = new Keleid.BLL.DTOs.AdvertisementPageDto
            {
                Advertisements = advertisements,
                CurrentPage = page,
                TotalPages = totalPages,
                TotalCount = totalCount
            };

            return View(model);
        }

        public async Task<IActionResult> Details(int id)
        {
            var advertisement = await _advertisementService.GetAdvertisementByIdForAdminAsync(id);
            if (advertisement == null)
            {
                return NotFound();
            }
            return View(advertisement);
        }

        [HttpPost]
        public async Task<IActionResult> Approve(int id, string? returnUrl = null)
        {
            // دریافت UserId ادمین فعلی
            var currentUserId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            var adminUsername = User.Identity?.Name ?? "Unknown";

            // دریافت اطلاعات آگهی برای لاگ
            var advertisement = await _advertisementService.GetAdvertisementByIdAsync(id);

            var result = await _advertisementService.ApproveAdvertisementAsync(id, currentUserId);
            if (result)
            {
                // لاگ کردن تایید آگهی
                await _loggingService.LogAdvertisementApproveAsync(id, adminUsername, advertisement?.Title, _httpContextAccessor);
                TempData["Success"] = "آگهی با موفقیت تایید شد.";
            }
            else
            {
                TempData["Error"] = "خطا در تایید آگهی.";
            }

            // تشخیص صفحه مبدأ و redirect مناسب
            if (!string.IsNullOrEmpty(returnUrl))
            {
                if (returnUrl.Contains("Waiting"))
                    return RedirectToAction("Waiting");
                else if (returnUrl.Contains("Approved"))
                    return RedirectToAction("Approved");
                else if (returnUrl.Contains("Removed"))
                    return RedirectToAction("Removed");
            }

            return RedirectToAction("Index");
        }

        [HttpPost]
        public async Task<IActionResult> Reject(int id, string? returnUrl = null)
        {
            // دریافت UserId ادمین فعلی
            var currentUserId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            var adminUsername = User.Identity?.Name ?? "Unknown";

            // دریافت اطلاعات آگهی برای لاگ
            var advertisement = await _advertisementService.GetAdvertisementByIdAsync(id);

            var result = await _advertisementService.RejectAdvertisementAsync(id, currentUserId);
            if (result)
            {
                // لاگ کردن رد آگهی
                await _loggingService.LogAdvertisementRejectAsync(id, adminUsername, advertisement?.Title, "رد شده توسط ادمین", _httpContextAccessor);
                TempData["Success"] = "آگهی رد شد.";
            }
            else
            {
                TempData["Error"] = "خطا در رد آگهی.";
            }

            // تشخیص صفحه مبدأ و redirect مناسب
            if (!string.IsNullOrEmpty(returnUrl))
            {
                if (returnUrl.Contains("Waiting"))
                    return RedirectToAction("Waiting");
                else if (returnUrl.Contains("Approved"))
                    return RedirectToAction("Approved");
                else if (returnUrl.Contains("Removed"))
                    return RedirectToAction("Removed");
            }

            return RedirectToAction("Index");
        }
    }
}
