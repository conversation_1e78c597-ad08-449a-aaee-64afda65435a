﻿@{
    string currentAction = ViewContext.RouteData.Values["action"]?.ToString();
    string currentController = ViewContext.RouteData.Values["controller"]?.ToString();

    string IsActive(string controller) =>
        (controller == currentController) ? "active" : "";

    string IsActive2(string controller, string action) =>
        (controller == currentController && action == currentAction) ? "active" : "";
}

<div class="mobile-nav d-lg-none">
    <a asp-controller="Home" asp-action="Index" class="mobile-nav-item @IsActive("Home")">
        <i class="fas fa-home"></i>
        <span>آگهی‌ها</span>
    </a>
    <a asp-controller="User" asp-action="Favs" class="mobile-nav-item @IsActive2("User","Favs")">
        <i class="far fa-bookmark"></i>
        <span>نشان‌ها</span>
    </a>
    <a asp-controller="Ads" asp-action="Index" class="mobile-nav-item @IsActive("Ads")">
        <i class="fas fa-plus"></i>
        <span>ثبت آگهی</span>
    </a>
    <a asp-controller="User" asp-action="MyAds" class="mobile-nav-item @IsActive2("User","MyAds")">
        <i class="fas fa-clipboard-list"></i>
        <span>آگهی‌های من</span>
    </a>
    <a asp-controller="User" asp-action="Index" class="mobile-nav-item @IsActive2("User","Index")">
        <i class="far fa-user"></i>
        <span>کلید من</span>
    </a>
</div>