using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace Keleid.Web.Controllers
{
    public class BaseController : Controller
    {
        public override void OnActionExecuting(ActionExecutingContext context)
        {
            // خواندن استان انتخابی از کوکی و تنظیم ViewBag
            var selectedProvince = Request.Cookies["selectedProvince"] ?? "کل ایران";
            ViewBag.SelectedProvince = selectedProvince;

            // تنظیم SearchTerm در ViewBag
            var searchTerm = context.ActionArguments.ContainsKey("search") ? context.ActionArguments["search"]?.ToString() :
                           context.ActionArguments.ContainsKey("searchTerm") ? context.ActionArguments["searchTerm"]?.ToString() :
                           Request.Query["search"].ToString();
            ViewBag.SearchTerm = !string.IsNullOrEmpty(searchTerm) ? searchTerm : null;

            base.OnActionExecuting(context);
        }
    }
}
